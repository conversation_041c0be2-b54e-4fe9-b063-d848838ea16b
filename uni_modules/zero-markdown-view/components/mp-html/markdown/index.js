/**
 * @fileoverview markdown 插件
 * Include marked (https://github.com/markedjs/marked)
 * Include github-markdown-css (https://github.com/sindresorhus/github-markdown-css)
 */
import marked from "./marked.min";
let index = 0;

function Markdown(vm) {
  this.vm = vm;
  vm._ids = {};
}
let fixStyle = [
  `<style>
ul ul,ul ol {margin-left:-26px;background:#f00} 
ul ul ol,ul ul ul,ul ol ul,ul ol ol {margin-left:-30px;}
ol {margin-left:-20px;} 
ol ul,ol ol {margin-left:-26px;} 
ol ul ol,ol ul ul,ol ol ul,ol ol ol {margin-left:-30px;}
</style>`,
  `<style>
ol {margin-left:-20px;} 
ul,ul ul,ul ol,ol ul,ol ol {margin-left:0px;padding:0} 
ul{display:flex;flex-direction:column;gap:10px; list-style:none;}
ul li{
  position:relative;
  padding-left:22px;
  padding-top:10px;
  padding-right:10px;
  padding-bottom:10px;
  display:flex;
  flex-direction:column;
  gap:10px; 
  background:#F7F8FA;
  border-radius:16px;
}
ul li:before{
  position:absolute;
  top:15px;
  left:10px;
  width:8px;
  height:8px;
  border-radius:2px;
  background:#18C2A5;
  font-weight: bold;
}

li h2{
margin-left:-10px
}
</style>`,
];

function maxListLevel(markdownText) {
  const listItemRegex = /^(\s*)(-\s|1\.)/;
  let maxLevel = 0;
  const lines = markdownText.split(/\r?\n/);
  for (let i = 0; i < lines.length; i++) {
    const match = lines[i].match(listItemRegex);
    if (match) {
      const level = (match[1].length / 2) | 0;
      maxLevel = Math.max(maxLevel, level);
    }
  }
  return maxLevel;
}
Markdown.prototype.onUpdate = function (content) {
  if (this.vm.markdown) {
    let level = maxListLevel(content);
    return fixStyle[level < 2 ? 1 : 0] + marked(content);
  }
};

Markdown.prototype.onParse = function (node, vm) {
  if (vm.options.markdown) {
    // 中文 id 需要转换，否则无法跳转
    if (
      vm.options.useAnchor &&
      node.attrs &&
      /[\u4e00-\u9fa5]/.test(node.attrs.id)
    ) {
      const id = "t" + index++;
      this.vm._ids[node.attrs.id] = id;
      node.attrs.id = id;
    }
    if (
      node.name === "p" ||
      node.name === "table" ||
      node.name === "tr" ||
      node.name === "th" ||
      node.name === "td" ||
      node.name === "blockquote" ||
      node.name === "pre" ||
      node.name === "code"
    ) {
      node.attrs.class = `md-${node.name} ${node.attrs.class || ""}`;
    }
  }
};

export default Markdown;
