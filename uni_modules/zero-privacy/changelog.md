## 1.1.5（2023-10-13）
增加隐私协议内容区域高度自定义,默认为30vh,超出滚动显示
## 1.1.4（2023-09-14）
优化文档 : 进阶使用方法,请仔细看文档中的各种使用方法   

### 需要手动重新触发可以 使用 ref 再次调用插件内的  checkPrivacySetting() 方法   

### 使用 @needAuthorization 判断当前是否需要授权才能使用api接口, false为已经授权不需要弹窗,可直接使用
## 1.1.3（2023-09-14）
文档优化
## 1.1.2（2023-09-13）
增加emit 事件needAuthorization,用于判断是否需要授权,诶需授权则直接执行自己的业务逻辑
## 1.1.1（2023-09-13）
使用时请添加微信小程序条件编译,具体看下方使用方法
## 1.1.0（2023-09-12）
优化文档
## 1.0.9（2023-09-10）
优化按钮样式
## 1.0.8（2023-09-10）
1. 新增自定义按钮文案功能   
2. 优化按钮样式   
3. 优化使用文档    
## 1.0.7（2023-09-08）
pref: created触发授权判断
## 1.0.6（2023-09-07）
优化说明文档
## 1.0.5（2023-09-07）
支持自定义弹窗标题
支持自定义协议内容
支持自定义协议名称,不传由组件默认获取
## 1.0.4（2023-09-05）
fix: position的type为String
## 1.0.3（2023-09-04）
细节优化
## 1.0.2（2023-09-03）
优化使用说明
## 1.0.1（2023-09-03）
优化代码,使用说明
## 1.0.0（2023-09-03）
首次发布
