<template>
  <view class="container">
    <view
      class="pulse-bubble pulse-bubble-1"
      :style="{ background: bgColor }"
    ></view>
    <view
      class="pulse-bubble pulse-bubble-2"
      :style="{ background: bgColor }"
    ></view>
    <view
      class="pulse-bubble pulse-bubble-3"
      :style="{ background: bgColor }"
    ></view>
  </view>
</template>

<script>
export default {
  name: "loading-pulse",
  props: {
    bgColor: {
      type: String,
      default: "#999",
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
/* pulse */
.container {
  // width: 120rpx;
  max-width: 90rpx;
  display: flex;
  //   justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 50%;
  left: 50%;
  //top: 22rpx;
  //left: 80rpx;
  transform: translate(-50%, -50%);
}

.pulse-bubble {
  width: 16rpx;
  height: 16rpx;
  margin-right: 8rpx;
  border-radius: 50%;
  background: #999;
}

.pulse-bubble-1 {
  background: #999;
  animation: pulse 0.4s ease 0s infinite alternate;
}

.pulse-bubble-2 {
  background: #999;
  animation: pulse 0.4s ease 0.2s infinite alternate;
}

.pulse-bubble-3 {
  background: #999;
  animation: pulse 0.4s ease 0.4s infinite alternate;
}

@keyframes pulse {
  from {
    opacity: 1;
    // transform: scale(1.25);
  }

  to {
    opacity: 0.25;
    // transform: scale(0.75);
  }
}
</style>
