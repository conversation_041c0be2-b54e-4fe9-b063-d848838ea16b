<template>
  <u-popup :show="show" round="19">
    <view>
      <view class="header">
        <text @click="onCancel">取消</text>
        <text>期望城市</text>
        <text @click="confirm">确定</text>
      </view>
      <view>
        <picker-view
          indicator-class="indicator"
          :value="selectIndex"
          @change="changeHandler"
          class="picker-view"
        >
          <picker-view-column class="column one">
            <view class="item" v-for="(item, index) in cityData" :key="index">{{
              item
            }}</view>
          </picker-view-column>
          <picker-view-column class="column two">
            <view class="item" v-for="(item, index) in areaData" :key="index">{{
              item
            }}</view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </u-popup>
  <!-- <view>
    <u-picker
      :defaultIndex="[0, 0]"
      round="10"
      :show="show"
      title="选择地址"
      ref="uPicker"
      :columns="columns"
      @confirm="confirm"
      @cancel="show = false"
      @change="changeHandler"
      closeOnClickOverlay
      @close="show = false"
      :selectIndex="selectIndex"
    ></u-picker>
  </view> -->
</template>

<script>
import { columns, columnData } from "./province.js";
export default {
  data() {
    return {
      show: false,
      cityData: columns[0],
      areaData: columnData[0],
      selectIndex: [0, 0],
      cityVal: "",
      areaVal: "",
    };
  },
  watch: {
    show(val) {
      if (val) {
        this.selectIndex = [0, 0];
        this.cityData = columns[0];
        this.areaData = columnData[0];
      }
    },
  },
  methods: {
    changeHandler(e) {
      const val = e.detail.value;
      console.log(val, "val", this.selectIndex);
      if (val[0] !== this.selectIndex[0]) {
        this.areaData = columnData[val[0]];
        this.selectIndex = [val[0], 0];
      } else {
        this.selectIndex = [val[0], val[1]];
      }
    },
    confirm() {
      this.cityVal = this.cityData[this.selectIndex[0]];
      this.areaVal = this.areaData[this.selectIndex[1]];
      this.show = false;
      let address = this.cityVal + this.areaVal;
      this.$emit("get-address", address);
    },
    onCancel() {
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 113rpx;
  align-items: center;
  border-bottom: 1rpx solid #f6f6f6;
  text:first-child {
    font-size: 14px;
    color: #666666;
  }
  text:last-child {
    font-size: 14px;
    color: #18C2A5;
  }
}
.picker-view {
  width: 100%;
  height: 330rpx;
  margin-top: 30rpx;
  box-sizing: border-box;
}
.item {
  height: 96rpx;
  align-items: center;
  justify-content: center;
  text-align: center;
}
::v-deep .column {
  font-size: 31rpx;
  color: #000;
  text-align: center;
  line-height: 96rpx;
}
::v-deep .column .indicator {
  width: 100% !important;
  overflow: hidden;
  height: 96rpx;
  background: #F4F4F5;
  z-index: -1;
}
</style>
