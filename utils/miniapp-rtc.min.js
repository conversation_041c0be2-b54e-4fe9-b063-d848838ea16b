/*!
 * @byted/miniapp-rtc v3.1.0
 * (c) 2020-2023 The VolcEngineRTC project authors.
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).MiniappRtc=t()}(this,(function(){"use strict";var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};function t(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}var n=function(){return(n=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function o(e,t,n,o){var r,i=arguments.length,s=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(i<3?r(s):i>3?r(t,n,s):r(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s}function r(e,t,n,o){return new(n||(n=Promise))((function(r,i){function s(e){try{c(o.next(e))}catch(e){i(e)}}function a(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((o=o.apply(e,t||[])).next())}))}function i(e,t){var n,o,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,o&&(r=2&i[0]?o.return:i[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,i[1])).done)return r;switch(o=0,r&&(i=[2&i[0],r.value]),i[0]){case 0:case 1:r=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,o=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!r||i[1]>r[0]&&i[1]<r[3])){s.label=i[1];break}if(6===i[0]&&s.label<r[1]){s.label=r[1],r=i;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(i);break}r[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],o=0}finally{n=r=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}var s,a=function(e){var t={exports:{}};return e(t,t.exports),t.exports}((function(e){var t=Object.prototype.hasOwnProperty,n="~";function o(){}function r(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function i(e,t,o,i,s){if("function"!=typeof o)throw new TypeError("The listener must be a function");var a=new r(o,i||e,s),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],a]:e._events[c].push(a):(e._events[c]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function a(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),a.prototype.eventNames=function(){var e,o,r=[];if(0===this._eventsCount)return r;for(o in e=this._events)t.call(e,o)&&r.push(n?o.slice(1):o);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},a.prototype.listeners=function(e){var t=n?n+e:e,o=this._events[t];if(!o)return[];if(o.fn)return[o.fn];for(var r=0,i=o.length,s=new Array(i);r<i;r++)s[r]=o[r].fn;return s},a.prototype.listenerCount=function(e){var t=n?n+e:e,o=this._events[t];return o?o.fn?1:o.length:0},a.prototype.emit=function(e,t,o,r,i,s){var a=n?n+e:e;if(!this._events[a])return!1;var c,u,d=this._events[a],l=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),l){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,o),!0;case 4:return d.fn.call(d.context,t,o,r),!0;case 5:return d.fn.call(d.context,t,o,r,i),!0;case 6:return d.fn.call(d.context,t,o,r,i,s),!0}for(u=1,c=new Array(l-1);u<l;u++)c[u-1]=arguments[u];d.fn.apply(d.context,c)}else{var _,h=d.length;for(u=0;u<h;u++)switch(d[u].once&&this.removeListener(e,d[u].fn,void 0,!0),l){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,t);break;case 3:d[u].fn.call(d[u].context,t,o);break;case 4:d[u].fn.call(d[u].context,t,o,r);break;default:if(!c)for(_=1,c=new Array(l-1);_<l;_++)c[_-1]=arguments[_];d[u].fn.apply(d[u].context,c)}}return!0},a.prototype.on=function(e,t,n){return i(this,e,t,n,!1)},a.prototype.once=function(e,t,n){return i(this,e,t,n,!0)},a.prototype.removeListener=function(e,t,o,r){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return s(this,i),this;var a=this._events[i];if(a.fn)a.fn!==t||r&&!a.once||o&&a.context!==o||s(this,i);else{for(var c=0,u=[],d=a.length;c<d;c++)(a[c].fn!==t||r&&!a[c].once||o&&a[c].context!==o)&&u.push(a[c]);u.length?this._events[i]=1===u.length?u[0]:u:s(this,i)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new o,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=n,a.EventEmitter=a,e.exports=a}));!function(e){e.userLeave="userLeave",e.connectionLost="connectionLost",e.roleChanged="roleChanged",e.userDuplicateLogin="userDuplicateLogin",e.kickedByAdmin="kickedByAdmin",e.roomDismissed="roomDismissed",e.roomForbidden="roomForbidden",e.userForbidden="userForbidden",e.serverError="serverError"}(s||(s={}));var c,u=function(){for(var e=[],t="0123456789abcdef",n=0;n<36;n++)e[n]=t.substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-",e.join("")},d=100001,l=100002,_=100003,h=100004,p=100005,m=100007,f=100008,v=200001,g=200002,y=200003,b=300001,I=300002,S=300003,R=300004,w=300006,x=300007,k=300008,T=300009,E=300010,O=300011,D=300012,A=300013,C=300014,U=500001,M=500002,N=Object.freeze({__proto__:null,INVALID_PARAMS:d,NOT_IN_ROOM:l,ALREADY_IN_ROOM:_,CONNECTING:h,ALREADY_PUBLISHED:p,NOT_PUBLISHED:100006,UID_NOT_FOUND:m,HAS_INITED:f,STREAM_ID_NOT_FOUND:v,GET_CONFIG_ERROR:g,NO_WEBSOCLET_URL:y,SUBSCRIBE_FAILED:b,UNSUBSCRIBE_FAILED:I,MUTE_LOCAL_ERROR:S,MUTE_REMOTE_ERROR:R,LEAVE_ERROR:300005,PUBLISH_ERROR:w,UNPUBLISH_ERROR:x,JOIN_ROOM_ERROR:k,SIGNALING_TIMEOUT:T,WEBSOCKET_NOT_CONNECTED:E,KICKED_OUT:O,DUPLICATE_LOGIN:D,ROOM_DISMISS:A,SERVER_ERROR:C,WEBSOCKET_FAILED:U,DOMAIN_IN_BLACKLIST:M});!function(e){e.init="init",e.connecting="connecting",e.connected="connected",e.reconnecting="reconnecting",e.reconnected="reconnected",e.disconnected="disconnected",e.failed="failed"}(c||(c={}));var P=function(e){function o(t,n){void 0===n&&(n=!1);var o=e.call(this)||this;return o._ctx=t,o._isReconnect=n,o._host="",o._pingInterval=5e3,o._pingTimeout=1e4,o._pingTimer=null,o._maxRetryTime=3,o._requestId=-1,o._cache=new Map,o._state=c.init,o._sessionId=u(),o._monitor=t.monitor,o._monitor.updateHeader({rtc_session_id:o._sessionId}),wx.__test__1006=function(){o._socketTask.close({success:function(){o.emit("@signaling/onClose",{code:1006,reason:""}),o._state=c.disconnected}})},o}return t(o,e),o.prototype.connect=function(e,t){var o=this,r="wss://"+e+t;return this._host=e,new Promise((function(e,t){var i=u(),s=r+"?wsid="+i+"&appid="+o._ctx.appId+"&ua=miniapp-3.1.0&EIO=3&transport=websocket";o._reportStateChange(o._isReconnect?c.reconnecting:c.connecting,s);var a=wx.connectSocket({url:s});a.onError((function(e){t({code:U,reason:e.errMsg}),o._isDisconnect()||(o._reportStateChange(c.failed,JSON.stringify(e),e.code),o.emit("@signaling/error",n(n({},e),{code:e.code||U})))})),a.onOpen((function(t){o._socketTask=a,o._ping(),o._reportStateChange(o._isReconnect?c.reconnected:c.connected,s),e()})),a.onClose((function(e){o._isDisconnect()||(o._reportStateChange(c.disconnected,JSON.stringify(e),e.code),o.emit("@signaling/onClose",e))})),a.onMessage((function(e){"string"==typeof(null==e?void 0:e.data)&&o._handleMessage(e.data)}))}))},o.prototype.disconnect=function(){var e=this;return new Promise((function(t,n){if(e._socketTask.readyState!==e._socketTask.OPEN)return t();e._socketTask.close({success:t,fail:n})}))},o.prototype.sendSignaling=function(e,t,n){return void 0===n&&(n=0),r(this,void 0,void 0,(function(){var o;return i(this,(function(r){switch(r.label){case 0:return[4,this._sendSignaling(e,t)];case 1:return o=r.sent(),n<this._maxRetryTime&&/5[0-9][0-9]/.test(""+o.code)?[4,this.sendSignaling(e,t,n+1)]:[3,3];case 2:o=r.sent(),r.label=3;case 3:return[2,o]}}))}))},o.prototype._sendSignaling=function(e,t){var n=this;return new Promise((function(o,r){var i;if(n._socketTask&&(null===(i=n._socketTask)||void 0===i?void 0:i.readyState)===n._socketTask.OPEN){t.sessionId=n._sessionId,t.timestamp=(new Date).valueOf();var s=++n._requestId,a="joinRoom"===e?60:10,c=setTimeout((function(){n._cache.delete(s),r({code:T,message:e+" signaling timeout",reason:"timeout_"+a})}),1e3*a);n._cache.set(s,{startTs:Date.now(),signaling_event:e,success:function(e){clearTimeout(c),o(e)},error:function(e){clearTimeout(c),r(e)}});var u=[e,t];n._reportRtcSignaling({signaling_event:"call-"+e,direction:"up",message:t}),n._socketTask.send({data:"42"+n._requestId+JSON.stringify(u)})}else r({code:E,reason:"websocket not open"})}))},o.prototype._feedbackMessage=function(e,t){var n;if(this._socketTask&&(null===(n=this._socketTask)||void 0===n?void 0:n.readyState)===this._socketTask.OPEN){var o=[e,t];this._reportRtcSignaling({signaling_event:e,direction:"up",message:t}),this._socketTask.send({data:"42"+JSON.stringify(o)})}},o.prototype._ping=function(){var e,t=this;this._socketTask&&(null===(e=this._socketTask)||void 0===e?void 0:e.readyState)===this._socketTask.OPEN&&(this._socketTask.send({data:"2"}),this._pingTimer=setTimeout((function(){t._isDisconnect()||(t._reportStateChange(c.disconnected,"pong timeout",-1),t.emit("@signaling/onClose",{code:1006,reason:"pong timeout"}),t._socketTask.close({}))}),this._pingTimeout))},o.prototype._handleMessage=function(e){var t,o,r,i=this;if("3"===e)return this._clearPingTimer(),void setTimeout((function(){return i._ping()}),this._pingInterval);var s=this._parseMessage(e);if(0===s.type){var a=(null==s?void 0:s.payload)||{},c=a.pingInterval,u=void 0===c?5e3:c,d=a.pingTimeout,l=void 0===d?1e4:d;return this._pingInterval=u,void(this._pingTimeout=l)}if(4===s.type)if(3===s.packetType){var _=s.requestId;if(_){var h=this._cache.get(+_);this._reportRtcSignaling({signaling_event:s.signalingType,direction:"down",message:s.payload,code:s.payload.code,elapse:Date.now()-((null==h?void 0:h.startTs)||Date.now())}),200===s.payload.code?null==h||h.success(s.payload):null==h||h.error({code:s.payload.code,message:s.payload.message,reason:"number"==typeof s.payload.code?"signaling_error":"ack_failed"}),this._cache.delete(+_)}}else 2===s.packetType&&(this._reportRtcSignaling(n({signaling_event:"on-"+s.signalingType,direction:"down",message:s.payload,code:(null===(t=s.payload)||void 0===t?void 0:t.code)||200},"streamFailed"===s.signalingType?{signaling_type:null===(o=s.payload)||void 0===o?void 0:o.type}:{})),setTimeout((function(){i.emit("@signaling/"+s.signalingType,s.payload)}),0),s.signalingType&&s.payload&&this._feedbackMessage(s.signalingType+"-res",{messageId:null===(r=null==s?void 0:s.payload)||void 0===r?void 0:r.messageId}))},o.prototype._reportRtcSignaling=function(e){var t=n(n({},e),{signaling_server:this._host}),o=e.message,r=o.streamId,i=o.eventSessionId,s=o.userType,a=o.streamUserId,c=o.screen;if("string"==typeof r){t.stream_id=r;var u=this._ctx.getUserInfoByStreamId(r),d=this._ctx.getStreamInfoByStreamId(r)[0];u&&(t.peer_user_type=u.userType,t.stream_user_id=u.clientId),d&&(t.pc_session_id=d.pcSessionId)}if("string"==typeof a){t.stream_user_id=a;u=this._ctx.getUserInfo(a),d=this._ctx.getRemoteStreamInfo(a,!!c);u&&(t.peer_user_type=u.userType),d&&(t.pc_session_id=d.pcSessionId)}s&&(t.peer_user_type=s),i&&(t.pc_session_id=i),this._monitor.report("rtc_signaling",t)},o.prototype._reportStateChange=function(e,t,n){void 0===t&&(t=""),void 0===n&&(n=0),this._state=e,this._monitor.report("rtc_websocket",{error_code:n,signaling_server:this._host,message:t,signaling_event:e})},o.prototype._isDigitChar=function(e){return/\d/.test(e)},o.prototype._parseMessage=function(e){for(var t={type:-1,packetType:-1,requestId:"",payload:null,signalingType:""},n=0,o="",r=0;r<e.length;r++){var i=e[r];if(!this._isDigitChar(i)){n=r;break}0===r?t.type=+i:1===r?t.packetType=+i:o+=i}if(t.requestId=o,3===t.packetType){var s=this._cache.get(+o);s&&(t.signalingType=s.signaling_event)}try{var a=JSON.parse(e.substr(n));Array.isArray(a)?1===a.length?t.payload=a[0]:2===a.length&&(t.signalingType=a[0],t.payload=a[1]):t.payload=a}catch(e){}return t},o.prototype.destroy=function(t){this._monitor.report("rtc_invoke_status",{sdk_api_name:"signaling.destroy",message:t}),this._cache.forEach((function(e){e.error({code:U,message:t,reason:"leave_room"})})),this._cache.clear(),e.prototype.removeAllListeners.call(this),this._clearPingTimer(),this.disconnect().catch((function(){}))},o.prototype._clearPingTimer=function(){this._pingTimer&&(clearInterval(this._pingTimer),this._pingTimer=null)},o.prototype._isDisconnect=function(){return this._state===c.failed||this._state===c.disconnected},o}(a.EventEmitter),L=function(){function e(){}return e.setCache=function(e,t){return new Promise((function(n,o){wx.setStorage({key:e,data:t,success:n,fail:o})}))},e.setCacheSync=function(e,t){try{return wx.setStorageSync(e,t),!0}catch(e){return!1}},e.getCache=function(e){return new Promise((function(e,t){wx.getStorage({key:"key",success:function(t){e(t.data)},fail:t})}))},e.getCacheSync=function(e,t){void 0===t&&(t=null);try{var n=wx.getStorageSync(e);return n||t}catch(e){return t}},e}(),j="keep_alive_url",B="device_id";function H(e,t){return new Promise((function(n,o){wx.request({url:e,data:t.body,method:t.method,header:{"content-type":"application/json"},success:function(e){n(e.data)},fail:o})}))}var K,F=function(e,t){return new Promise((function(n,o){var r=(e=Array.isArray(e)?e:[]).length,i=[],s=!1;0===r?o([]):e.forEach((function(e){e.then((function(e){s&&t&&t(e),s=!0,n(e)}),(function(e){r--,i.push(e),0===r&&o(i)}))}))}))};function q(){var e=L.getCacheSync(B);return e||(e=u(),L.setCache(B,e),e)}!function(e){e.Init="init",e.Connecting="connecting",e.Connected="connected",e.Disconnected="disconnected",e.Reconnecting="reconnecting"}(K||(K={}));var J="peer-online",V="peer-leave",G="client-banned",W="stream-added",z="stream-removed",Y="unmute-audio",Q="mute-audio",X="unmute-video",Z="mute-video",$="disconnect",ee="error",te="close",ne="update-url",oe="stream-failed",re=Object.freeze({__proto__:null,PEER_ONLINE:J,PEER_LEAVE:V,CLIENT_BANNED:G,STREAM_ADDED:W,STREAM_REMOVED:z,UNMUTE_AUDIO:Y,MUTE_AUDIO:Q,UNMUTE_VIDEO:X,MUTE_VIDEO:Z,RECONNECTING:"connecting",RECONNECTED:"reconnected",DISCONNECT:$,ERROR:ee,CLOSE:te,UPDATE_URL:ne,STREAM_FAILED:oe}),ie={host:"ws.rtc.volcvideo.com",path:"/rtmp_socket/"},se=["common.rtc.volcvideo.com","common-hl.rtc.volcvideo.com"];function ae(e){return"boolean"==typeof e}var ce=function(e){function o(t){var n=e.call(this)||this;return n._state=K.Init,n._enableCamera=!0,n._enableMic=!0,n._publishing=!1,n._reconnectFailed=!1,n._isLeaving=!1,n._ctx=t.ctx,n._emit=t.emit,n._monitor=t.ctx.monitor,n}return t(o,e),o.prototype.connect=function(){return r(this,void 0,void 0,(function(){var e,t,n,o,r;return i(this,(function(i){switch(i.label){case 0:if(this._state===K.Connected)return[2];(e=L.getCacheSync(j))&&0!==e.length||(e=[ie]),this._state=K.Connecting,t=this._ctx.getSocketHost(),n=this._ctx.getServers(),i.label=1;case 1:return i.trys.push([1,12,,13]),"string"!=typeof t?[3,2]:(e=[{host:t,path:"/rtmp_socket/"}],[3,4]);case 2:return n?[4,this._getKeepAliveUrlsAndCache(n)]:[3,4];case 3:e=i.sent(),i.label=4;case 4:if(!Array.isArray(e)||0===e.length)throw{code:y,reason:"get websocket url failed"};this._wsDoamin=e.shift(),i.label=5;case 5:return i.trys.push([5,7,,11]),this._signaling=new P(this._ctx),[4,this._signaling.connect(this._wsDoamin.host,this._wsDoamin.path)];case 6:return i.sent(),[3,11];case 7:return i.sent(),[4,this._getKeepAliveUrlsAndCache(se)];case 8:return(o=i.sent())&&o.length>0?(this._wsDoamin=o[0],this._signaling=new P(this._ctx),[4,this._signaling.connect(o[0].host,o[0].path)]):[3,10];case 9:i.sent(),i.label=10;case 10:return[3,11];case 11:return this._handleSignaling(),[3,13];case 12:throw r=i.sent(),this._state=K.Disconnected,r;case 13:return[2]}}))}))},o.prototype.join=function(e){return void 0===e&&(e=!1),r(this,void 0,void 0,(function(){var t=this;return i(this,(function(o){return this._checkConnect(),[2,new Promise((function(o,r){t._joinRoomResolve=o,t._joinRoomReject=r,t._signaling.sendSignaling(e?"reconnected":"joinRoom",{roomId:t._ctx.roomId,userAttributes:{role:t._ctx.getRole()},params:{deviceType:"miniapp",userAgent:t._ctx.getUA(),sdkVersion:"3.1.0",deviceId:q(),appId:t._ctx.appId,roomId:t._ctx.roomId,userId:t._ctx.userId,businessId:"",rtcSid:""},Authorization:t._ctx.token.startsWith("Basic")?t._ctx.token:"Bearer "+t._ctx.token,_userAgentIP:t._ctx.getUserAgentIP()||"",_lambdaAddr:t._ctx.getLambdaAddr()||""}).then((function(e){var n;t._state=K.Connected,t._handleJoinRoomACK(e),null===(n=t._joinRoomResolve)||void 0===n||n.call(t,e)})).catch((function(e){var o;t._state=K.Disconnected,e.code>=700&&e.code<800&&(e.reason="token_error"),null===(o=t._joinRoomReject)||void 0===o||o.call(t,n(n({},e),{code:k}))})).finally((function(){delete t._joinRoomResolve,delete t._joinRoomReject}))}))]}))}))},o.prototype.leave=function(){var e;return r(this,void 0,void 0,(function(){var t=this;return i(this,(function(n){return this._isLeaving||(this._isLeaving=!0,this._checkConnect(),this._joinRoomReject&&this._joinRoomResolve&&(this._joinRoomReject({code:k,reason:"leave_room",message:"call leaveRoom"}),delete this._joinRoomResolve,delete this._joinRoomReject),null===(e=this._signaling)||void 0===e||e.sendSignaling("leaveRoom",{roomId:this._ctx.roomId,Authorization:this._ctx.token.startsWith("Basic")?this._ctx.token:"Bearer "+this._ctx.token}).then((function(e){var n;return t._state=K.Disconnected,null===(n=t._signaling)||void 0===n||n.destroy("leave room"),delete t._signaling,t._isLeaving=!1,e})).catch((function(e){}))),[2]}))}))},o.prototype.getLocalStreamId=function(){return this._publishStreamId},o.prototype.destroy=function(){var e;this.removeAllListeners(),this.leave().catch((function(){})),null===(e=this._signaling)||void 0===e||e.destroy("destroy"),delete this._signaling,this._state=K.Init,this._publishRtmp="",this._publishStreamId="",this._enableCamera=!0,this._enableMic=!0,this._reconnectFailed=!1,this._reconnectTimer&&(clearTimeout(this._reconnectTimer),delete this._reconnectTimer)},o.prototype.publish=function(e){return void 0===e&&(e=!1),r(this,void 0,void 0,(function(){var t,n;return i(this,(function(o){switch(o.label){case 0:this._publishing=!0,o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this._publish(e)];case 2:return t=o.sent(),this._publishing=!1,[2,t];case 3:throw n=o.sent(),this._publishing=!1,n;case 4:return[2]}}))}))},o.prototype._publish=function(e){return void 0===e&&(e=!1),r(this,void 0,void 0,(function(){var t,o;return i(this,(function(r){switch(r.label){case 0:if(this._checkConnect(),!e&&this._publishRtmp)throw{code:p,reason:"published"};r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this._signaling.sendSignaling("publish",{roomId:this._ctx.roomId,screen:!1,streamId:this._publishStreamId||"",eventSessionId:this._ctx.publishSessionId,attributes:{audiostream:this._enableMic,videostream:this._enableCamera},_expectedMSAddr:this._ctx.getMsAddr()})];case 2:return t=r.sent(),this._publishRtmp=t.publishRtmpAddr,this._publishStreamId=t.streamId,[2,{rtmp:this._publishRtmp,streamId:this._publishStreamId}];case 3:throw o=r.sent(),n(n({},o),{code:w});case 4:return[2]}}))}))},o.prototype.unpublish=function(){return r(this,void 0,void 0,(function(){var e;return i(this,(function(t){switch(t.label){case 0:if(!this._publishRtmp)return[2];this._checkConnect(),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this._signaling.sendSignaling("unpublish",{roomId:this._ctx.roomId,streamId:this._publishStreamId,eventSessionId:this._ctx.publishSessionId})];case 2:return t.sent(),this._publishRtmp="",this._publishStreamId="",[3,4];case 3:throw e=t.sent(),n(n({},e),{code:x});case 4:return[2]}}))}))},o.prototype.subscribe=function(e,t){return r(this,void 0,void 0,(function(){var o=this;return i(this,(function(r){return this._checkConnect(),[2,new Promise((function(r,i){var s,a=o._ctx.getUserInfo(e);if(!a)return i({code:m,reason:"uid not found"});var c=t.audio,u=t.video,d=t.screen,l=o._ctx.getRemoteStreamInfo(e,d);if(!l)return i({code:v,reason:"stream not found"});l.subOption=t;var _=d?"recvScreenAudio":"recvAudio";"boolean"==typeof a[_]?c=a[_]:"boolean"!=typeof c&&(c=!0);var h=d?"recvScreenVideo":"recvVideo";"boolean"==typeof a[h]?u=a[h]:"boolean"!=typeof u&&(u=!0),null===(s=o._signaling)||void 0===s||s.sendSignaling("subscribe",{roomId:o._ctx.roomId,streamUserId:e,screen:!!d,uniqueKey:l.uniqueKey,eventSessionId:o._ctx.getSubscribeId(e,d),config:{enableMediaType:{audio:c,video:u}},_expectedMSAddr:o._ctx.getMsAddr()}).then((function(e){t.screen?a.screenStream&&(a.screenStream.rtmp=e.subscribeRtmpAddr,a.screenStream.streamId=e.streamId):a.stream&&(a.stream.rtmp=e.subscribeRtmpAddr,a.stream.streamId=e.streamId),r({rtmp:e.subscribeRtmpAddr,streamId:e.streamId})})).catch((function(e){return i(n(n({},e),{code:b}))}))}))]}))}))},o.prototype.unsubscribe=function(e,t){return r(this,void 0,void 0,(function(){var o=this;return i(this,(function(r){return this._checkConnect(),[2,new Promise((function(r,i){var s,a,c,u=o._ctx.getUserInfo(e),d="";return u?(d=(null==t?void 0:t.screen)?null===(s=null==u?void 0:u.screenStream)||void 0===s?void 0:s.streamId:null===(a=null==u?void 0:u.stream)||void 0===a?void 0:a.streamId)?void(null===(c=o._signaling)||void 0===c||c.sendSignaling("unsubscribe",{roomId:o._ctx.roomId,streamId:d,eventSessionId:o._ctx.getSubscribeId(e,null==t?void 0:t.screen)}).then((function(){r()})).catch((function(e){return i(n(n({},e),{code:I}))}))):i({code:v,reason:"stream not found"}):i({code:m,reason:"uid not found"})}))]}))}))},o.prototype.muteLocal=function(e){return r(this,void 0,void 0,(function(){var t=this;return i(this,(function(o){return this._checkConnect(),[2,new Promise((function(o,r){if("boolean"==typeof e.audio&&(t._enableMic=!e.audio),"boolean"==typeof e.video&&(t._enableCamera=!e.video),!t._publishRtmp&&!t._publishing)return o();var i={};"boolean"==typeof e.audio&&(i.audiostream=!e.audio),"boolean"==typeof e.video&&(i.videostream=!e.video),t._signaling.sendSignaling("updateStreamAttributes",{roomId:t._ctx.roomId,streamId:t._publishStreamId,attributes:i,eventSessionId:t._ctx.publishSessionId}).then((function(){return o()})).catch((function(e){return r(n(n({},e),{code:S}))}))}))]}))}))},o.prototype.muteRemote=function(e,t){return r(this,void 0,void 0,(function(){var o=this;return i(this,(function(r){return this._checkConnect(),[2,new Promise((function(r,i){var s,a,c,u=o._ctx.getUserInfo(e);if(!u)return i({code:m,reason:"uid not found"});var d=void 0;d=t.screen?null===(s=null==u?void 0:u.screenStream)||void 0===s?void 0:s.streamId:null===(a=null==u?void 0:u.stream)||void 0===a?void 0:a.streamId;var l=screen?"recvScreenAudio":"recvAudio";"boolean"==typeof t.audio?u[l]=!t.audio:t.audio="boolean"==typeof u[l]&&!u[l];var _=screen?"recvScreenVideo":"recvVideo";if("boolean"==typeof t.video?u[_]=!t.video:t.video="boolean"==typeof u[_]&&!u[_],!d)return r();null===(c=o._signaling)||void 0===c||c.sendSignaling("updateSubscribe",{roomId:o._ctx.roomId,streamId:d,config:{enableMediaType:{audio:!t.audio,video:!t.video}},eventSessionId:o._ctx.getSubscribeId(e,t.screen)}).then((function(){return r()})).catch((function(e){return i(n(n({},e),{code:R}))}))}))]}))}))},o.prototype.updateUserAttributes=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return this._checkConnect(),this._ctx.setRole(e.role),[4,this._signaling.sendSignaling("updateUserAttributes",{roomId:this._ctx.roomId,attributes:e,eventSessionId:this._ctx.publishSessionId})];case 1:return t.sent(),[2]}}))}))},o.prototype.getStreamInfo=function(e){var t,n,o={streamId:"",rtmp:""},r=this._ctx.getUserInfo(e);return(null==r?void 0:r.stream)&&(o.streamId=null===(t=r.stream)||void 0===t?void 0:t.streamId,o.rtmp=null===(n=r.stream)||void 0===n?void 0:n.rtmp),o},o.prototype._getKeepAliveUrlsAndCache=function(e){return r(this,void 0,void 0,(function(){var t,n=this;return i(this,(function(o){return t=e.map((function(t){var o={appID:n._ctx.appId,roomID:n._ctx.roomId,keys:["multiDomain"],labelSelector:"serviceType=rtmp",sdkVersion:"3.1.0",deviceType:"miniapp"};return n._monitor.report("rtc_get_config",{error_code:0,host:e,type:"request",message:JSON.stringify(o)}),H("https://"+t+"/decision/v1/multi",{method:"POST",body:o})})),[2,F(t).then((function(e){var t;void 0===e&&(e={});var o=e.multiDomain,r=e.domainBlacklist;return n._monitor.report("rtc_get_config",{error_code:0,message:JSON.stringify(e),type:"response"}),L.setCacheSync(j,o),Array.isArray(r)&&r.find((function(e){var t;return e===(null===(t=n._wsDoamin)||void 0===t?void 0:t.host)}))&&n._clientEmit(te,{code:M,reason:"current websocket domain "+(null===(t=n._wsDoamin)||void 0===t?void 0:t.host)+" in blacklist"}),o})).catch((function(e){var t,o,r;throw n._monitor.report("rtc_get_config",{error_code:null===(t=e[0])||void 0===t?void 0:t.errno,message:null===(o=e[0])||void 0===o?void 0:o.errMsg,type:"response"}),{code:g,reason:null===(r=e[0])||void 0===r?void 0:r.errMsg}}))]}))}))},o.prototype._handleSignaling=function(){var e,t,n,o,r,i,s,a,c=this;null===(e=this._signaling)||void 0===e||e.on("@signaling/onClose",(function(e){var t,n;if(1006===e.code)return c._monitor.report("rtc_invoke_status",{sdk_api_name:"1006",message:e.reason}),null===(t=c._signaling)||void 0===t||t.destroy("websocket close: 1006"),c._signaling=new P(c._ctx,!0),c._handleSignaling(),void(c._reconnectTimer=setTimeout((function(){var e;c._monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect",message:"start"}),null===(e=c._signaling)||void 0===e||e.connect(c._wsDoamin.host,c._wsDoamin.path).then((function(){c.join(!0).then(c._reconnectSuccess.bind(c)),c._reconnectFailed=!1,delete c._reconnectTimer,c._monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect",message:"success"})})).catch((function(){c._reconnectFailed=!0,delete c._reconnectTimer,c._monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect",message:"failed"})}))}),c._reconnectFailed?3e3:0));c._state=K.Disconnected,null===(n=c._signaling)||void 0===n||n.destroy("websocket close"),delete c._signaling,c._clientEmit($,e)})),null===(t=this._signaling)||void 0===t||t.on("@signaling/error",(function(e){var t;c._monitor.report("rtc_error",{error_code:e.code,message:{from:"wx.socket onerror",reason:e.reason}}),null===(t=c._signaling)||void 0===t||t.destroy("websocker close"),delete c._signaling,c._state=K.Disconnected,c._clientEmit(ee,e)})),null===(n=this._signaling)||void 0===n||n.on("@signaling/userConnection",this._onUserConnect.bind(this)),null===(o=this._signaling)||void 0===o||o.on("@signaling/userDisconnection",this._onUserDisconnect.bind(this)),null===(r=this._signaling)||void 0===r||r.on("@signaling/onAddStream",this._onAddStream.bind(this)),null===(i=this._signaling)||void 0===i||i.on("@signaling/onRemoveStream",this._onRemoveStream.bind(this)),null===(s=this._signaling)||void 0===s||s.on("@signaling/streamFailed",this._onStreamFailed.bind(this)),null===(a=this._signaling)||void 0===a||a.on("@signaling/onUpdateStreamAttributes",this._onUpdateStreamAttributes.bind(this))},o.prototype._handleJoinRoomACK=function(e){var t=this,n=e.clientDetail,o=e.streams,r=new Map(this._ctx.getRomoteUserMap());Array.isArray(n)&&n.forEach((function(e){r.has(e.clientId)?r.delete(e.clientId):t._onUserConnect(e)})),r.forEach((function(e){t._onUserDisconnect({clientId:e.clientId})})),Array.isArray(o)&&o.forEach(this._onAddStream.bind(this))},o.prototype._reconnectSuccess=function(){return r(this,void 0,void 0,(function(){var e,t;return i(this,(function(n){switch(n.label){case 0:if(!this._publishRtmp)return[3,4];e=this._publishRtmp,n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.publish(!0)];case 2:return(t=n.sent().rtmp)!==e&&this._clientEmit(ne,{uid:this._ctx.userId,screen:!1,url:t}),[3,4];case 3:return n.sent(),this._clientEmit(oe,{uid:this._ctx.userId,screen:!1,reason:"publish failed after reconnection"}),[3,4];case 4:return[2]}}))}))},o.prototype._onUserConnect=function(e){e.clientId!==this._ctx.userId&&(this._ctx.addRemoteUser(e),this._clientEmit(J,{uid:e.clientId}))},o.prototype._onUserDisconnect=function(e){var t=e.clientId,n=e.tag;if(t===this._ctx.userId){var o=null;n===s.kickedByAdmin?o=O:n===s.roomDismissed?o=A:n===s.userDuplicateLogin?o=D:n===s.serverError&&(o=C),o&&(this._clientEmit(G,{errorCode:o}),this.emit("@room/client-banned",{errorCode:o}))}else this._onRemoveStream({clientId:t,screen:!0,roomId:this._ctx.roomId}),this._onRemoveStream({clientId:t,screen:!1,roomId:this._ctx.roomId}),this._clientEmit(V,{uid:t}),this._ctx.removeRemoteUser(t)},o.prototype._onAddStream=function(e){var t,n;return r(this,void 0,void 0,(function(){var o,r,s,a,c;return i(this,(function(i){switch(i.label){case 0:return o=this._ctx.getUserInfo(e.streamUserId),r=e.screen?"screenStream":"stream",o?(s=o[r])?[3,1]:(o[r]={uniqueKey:e.uniqueKey,attributes:e.attributes},this._clientEmit(W,{uid:e.streamUserId,screen:e.screen}),[3,6]):[3,6];case 1:if(s.uniqueKey===e.uniqueKey)return[3,6];this._monitor.report("rtc_invoke_status",{sdk_api_name:"uniqueKey.change",message:JSON.stringify({stream:s,uniqueKey:e.uniqueKey})}),s.uniqueKey=e.uniqueKey,a="",i.label=2;case 2:return i.trys.push([2,4,,5]),[4,this.subscribe(e.streamUserId,{screen:e.screen,audio:null===(t=s.subOption)||void 0===t?void 0:t.audio,video:null===(n=s.subOption)||void 0===n?void 0:n.video})];case 3:return c=i.sent(),a=c.rtmp,[3,5];case 4:return i.sent(),[3,5];case 5:a?this._clientEmit(ne,{uid:e.streamUserId,screen:e.screen,url:a}):this._clientEmit(oe,{uid:e.streamUserId,screen:e.screen,reason:"re-subscribe failed."}),i.label=6;case 6:return[2]}}))}))},o.prototype._onRemoveStream=function(e){var t=e.clientId,n=e.screen;if(t!==this._ctx.userId){var o=this._ctx.getUserInfo(t);if(o){if(n){if(!o.screenStream)return;delete o.screenStream}else{if(!o.stream)return;delete o.stream}this._clientEmit(z,{uid:t,screen:n})}}},o.prototype._onStreamFailed=function(e){var t,n;return r(this,void 0,void 0,(function(){var o,r,s,a,c,u,d;return i(this,(function(i){switch(i.label){case 0:if(o=/4[0-9][0-9]/.test(e.code.toString()),e.eventSessionId!==this._ctx.publishSessionId)return[3,6];this._publishRtmp="",u="",i.label=1;case 1:return i.trys.push([1,4,,5]),o?[3,3]:[4,this.publish()];case 2:d=i.sent(),u=d.rtmp,i.label=3;case 3:return[3,5];case 4:return i.sent(),[3,5];case 5:return u?this._clientEmit(ne,{uid:this._ctx.userId,screen:!1,url:u}):this._clientEmit(oe,{uid:this._ctx.userId,screen:!1,reason:e.message}),[3,12];case 6:if(r=this._ctx.getUserInfoByStreamId(e.streamId),s=this._ctx.getStreamInfoByStreamId(e.streamId),a=s[0],c=s[1],!r||!a||e.eventSessionId!==a.pcSessionId)return[3,12];u="",i.label=7;case 7:return i.trys.push([7,10,,11]),o?[3,9]:[4,this.subscribe(r.clientId,{audio:null===(t=null==a?void 0:a.subOption)||void 0===t?void 0:t.audio,video:null===(n=null==a?void 0:a.subOption)||void 0===n?void 0:n.video,screen:c})];case 8:d=i.sent(),u=d.rtmp,i.label=9;case 9:return[3,11];case 10:return i.sent(),[3,11];case 11:u?this._clientEmit(ne,{uid:r.clientId,screen:c,url:u}):(this._clientEmit(z,{uid:r.clientId,screen:c}),c?delete r.screenStream:delete r.stream),i.label=12;case 12:return[2]}}))}))},o.prototype._onUpdateStreamAttributes=function(e){var t=e.attributes,o=e.streamUserId,r=e.screen,i=this._ctx.getUserInfo(o),s=this._ctx.getRemoteStreamInfo(o,r);if(i&&s){var a=t.audiostream,c=t.videostream,u=s.attributes,d=u.audiostream,l=u.videostream;ae(a)&&ae(d)&&a!==d&&(a?this._clientEmit(Y,{uid:i.clientId,screen:r}):this._clientEmit(Q,{uid:i.clientId,screen:r})),ae(c)&&ae(l)&&c!==l&&(c?this._clientEmit(X,{uid:i.clientId,screen:r}):this._clientEmit(Z,{uid:i.clientId,screen:r})),s.attributes||(s.attributes={}),s.attributes=n(n({},s.attributes),t)}},o.prototype._checkConnect=function(){if(!this._signaling)throw{code:E,reason:"websocket not open"}},o.prototype._clientEmit=function(e,t){this._monitor.report("rtc_sdk_callback",{sdk_callback_name:e,error_code:0,message:t}),this._emit.call(null,e,t)},Object.defineProperty(o.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"localRtmp",{get:function(){return this._publishRtmp},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"localStreamId",{get:function(){return this._publishStreamId},enumerable:!1,configurable:!0}),o}(a.EventEmitter),ue=q();function de(e,t){var n={event:t.event,streamID:t.streamID,streamAddr:t.streamAddr,userID:t.userID,roomID:t.roomID,deviceID:ue,rtcSID:ue,appID:t.appID,sdkVersion:"3.1.0",result:t.result,reason:t.reason,code:t.code};e.report("rtc_invoke_status",{sdk_api_name:"UploadRTMPResult",message:"start"+JSON.stringify(n)}),H("https://"+(t.logHost||"common.rtc.volcvideo.com")+"/dispatch/v1/Report?Action=UploadRTMPResult",{method:"POST",body:n}).then((function(){e.report("rtc_invoke_status",{sdk_api_name:"UploadRTMPResult",message:"success"})})).catch((function(t){e.report("rtc_invoke_status",{sdk_api_name:"UploadRTMPResult",message:"error"+JSON.stringify(t)})}))}function le(e){return void 0===e&&(e=0),function(t,n,o){var r=o.value;o.value=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var o=r.apply(this,t);return o.then(t[e],t[e+1]),o}}}function _e(e){return new Promise((function(t){setTimeout((function(){t()}),e)}))}var he=function(){function e(){this._destroyed=!1,this._header={product_line:"rtc",report_version:"5",rtc_sdk_version:"3.1.0",device_id:q(),engine_session_id:u(),rtc_app_id:"",room_id:"",user_id:"",report_time:Date.now(),report_log_count:0,access:""},this._data=[],this._reportId=0,this._send2server()}return e.prototype.updateHeader=function(e){this._header=n(n({},this._header),e)},e.prototype.report=function(e,t){"rtc_media_statistics"!==e&&console.log("monitor> ",e,t,this._header);var o=Date.now();this._data.push(n(n({event_key:e},t),{message:JSON.stringify(t.message||{}),event_type:"event",report_id:this._reportId++,time:o,rtc_time:o}))},e.prototype.destroy=function(){this._destroyed=!0},e.prototype._send2server=function(){var e=this;if(this._data.length){var t=this._data.splice(0,200);this._header.report_time=Date.now(),this._header.report_log_count=t.length,this._header.access=pe.networkType,H("https://log.snssdk.com/video/v1/webrtc_log/",{method:"POST",body:{data:t,header:this._header,os:"miniapp",from:"miniapp",version:"3"}}).then((function(){})).catch((function(){e._data=[].concat(t,e._data)}))}else if(this._destroyed)return;setTimeout(this._send2server.bind(this),5e3)},e}(),pe=function(){function e(t){var n;this._options=t,this.appId="",this.roomId="",this.userId="",this.token="",this.publishSessionId="",this.monitor=new he,this._role="normalUser",this._userMap=new Map,this.monitor.updateHeader({os_version:(null===(n=e.systemInfo)||void 0===n?void 0:n.version)||"",user_agent:this.getUA()})}return e.initWxEnv=function(){e.systemInfo=wx.getSystemInfoSync(),console.log(e.systemInfo),wx.getNetworkType({success:function(t){e.networkType=t.networkType}}),wx.onNetworkStatusChange((function(t){e.networkType=t.networkType}))},e.prototype.getUA=function(){var t=e.systemInfo||{},n=t.system,o=t.model,r=t.platform,i=t.brand;return JSON.stringify({system:n,model:o,platform:r,brand:i})},e.prototype.getLogHost=function(){return this._options.logHost},e.prototype.getSocketHost=function(){return this._options.sshost},e.prototype.getServers=function(){return this._options.servers},e.prototype.getUserAgentIP=function(){return this._options._userAgentIP},e.prototype.getLambdaAddr=function(){return this._options._lambdaAddr},e.prototype.getMsAddr=function(){return this._options._expectedMSAddr},e.prototype.getRole=function(){return this._role},e.prototype.setRole=function(e){this._role=e},e.prototype.getUserInfo=function(e){return this._userMap.get(e)},e.prototype.getRemoteStreamInfo=function(e,t){var n=this.getUserInfo(e);return t?null==n?void 0:n.screenStream:null==n?void 0:n.stream},e.prototype.updateSubscribeId=function(e,t,n){var o=this.getRemoteStreamInfo(e,t);o&&(o.pcSessionId=n)},e.prototype.getSubscribeId=function(e,t){var n;return(null===(n=this.getRemoteStreamInfo(e,t))||void 0===n?void 0:n.pcSessionId)||""},e.prototype.getRemoteUserType=function(e){var t;return(null===(t=this.getUserInfo(e))||void 0===t?void 0:t.userType)||""},e.prototype.getRemoteStreamId=function(e,t){var n;return null===(n=this.getRemoteStreamInfo(e,t))||void 0===n?void 0:n.streamId},e.prototype.addRemoteUser=function(e){this._userMap.set(e.clientId,n({},e))},e.prototype.removeRemoteUser=function(e){this._userMap.delete(e)},e.prototype.getRomoteUserMap=function(){return this._userMap},e.prototype.getUserInfoByStreamId=function(e){var t;return this._userMap.forEach((function(n){var o,r;(null===(o=n.stream)||void 0===o?void 0:o.streamId)!==e&&(null===(r=n.screenStream)||void 0===r?void 0:r.streamId)!==e||(t=n)})),t},e.prototype.getStreamInfoByStreamId=function(e){var t,n=!1;return this._userMap.forEach((function(o){var r,i;(null===(r=o.stream)||void 0===r?void 0:r.streamId)===e&&(t=o.stream),(null===(i=o.screenStream)||void 0===i?void 0:i.streamId)===e&&(t=o.screenStream,n=!0)})),[t,n]},e.prototype.reset=function(){this.appId="",this.roomId="",this.userId="",this._role="normalUser"},e.networkType="unknown",e}();pe.initWxEnv();var me={1001:"推流：已经连接推流服务器",1002:"推流：已经与服务器握手完毕，开始推流",1003:"推流：打开摄像头成功",1004:"推流：录屏启动成功",1005:"推流：推流动态调整分辨率",1006:"推流：推流动态调整码率",1007:"推流：首帧画面采集完成",1008:"推流：编码器启动",1018:"推流：进房成功（ROOM协议特有）",1019:"推流：退房成功（ROOM协议特有有）",1020:"推流：远端主播列表变化（ROOM协议特有）",1021:"推流：网络变更时重进房，WiFi 切换到4G 会触发断线重连（ROOM协议特有）",1022:"推流：进入房间失败（ROOM协议特有）",1031:"推流：远端主播进房通知（ROOM协议特有）",1032:"推流：远端主播退房通知（ROOM协议特有）",1033:"推流：远端主播视频状态位变化（RROOM协议特有）",1034:"推流：远端主播音频状态位变化（ROOM协议特有）",1101:"推流：网络状况不佳：上行带宽太小，上传数据受阻",1102:"推流：网络断连, 已启动自动重连",1103:"推流：硬编码启动失败, 采用软编码",1104:"推流：视频编码失败, 内部会重启编码器",2001:"拉流：已经连接服务器",2002:"拉流：已经连接 RTMP 服务器,开始拉流",2003:"拉流：网络接收到首个视频数据包(IDR)",2004:"拉流：视频播放开始",2005:"拉流：视频播放进度",2006:"拉流：视频播放结束",2007:"拉流：视频播放Loading",2008:"拉流：解码器启动",2009:"拉流：视频分辨率改变",2030:"音频设备发生改变，即当前的输入输出设备发生改变，比如耳机被拔出",2032:"拉流：视频渲染首帧事件",2101:"拉流：当前视频帧解码失败",2102:"拉流：当前音频帧解码失败",2103:"拉流：网络断连, 已启动自动重连",2104:"拉流：网络来包不稳：可能是下行带宽不足，或由于主播端出流不均匀",2105:"拉流：当前视频播放出现卡顿",2106:"拉流：硬解启动失败，采用软解",2107:"拉流：当前视频帧不连续，可能丢帧",2108:"拉流：当前流硬解第一个I帧失败，SDK自动切软解",3001:"RTMP DNS解析失败",3002:"RTMP服务器连接失败",3003:"RTMP服务器握手失败",3004:"RTMP服务器主动断开，请检查推流地址的合法性或防盗链有效期",3005:"RTMP 读/写失败",4998:"Mic状态切换的时候，enable-mic触发(iOS特有)",4999:"mute状态切换的时候，muted 触发(iOS特有)",5001:"系统电话打断或者微信音视频电话打断",10001:"用户禁止使用摄像头",10002:"用户禁止使用录音",10003:"背景音资源（BGM）加载失败",10004:"等待画面资源（waiting-image）加载失败","-1301":"推流：打开摄像头失败","-1302":"推流：打开麦克风失败","-1303":"推流：视频编码失败","-1304":"推流：音频编码失败","-1305":"推流：不支持的视频分辨率","-1306":"推流：不支持的音频采样率","-1307":"推流：网络断连，且经多次重连抢救无效，更多重试请自行重启推流","-1308":"推流：开始录屏失败，可能是被用户拒绝","-1309":"推流：录屏失败，不支持的Android系统版本，需要5.0以上的系统","-1310":"推流：录屏被其他应用打断了","-1311":"推流：Android Mic打开成功，但是录不到音频数据","-1312":"推流：录屏动态切横竖屏失败",0:"无错误"};function fe(){return function(e,t,n){if("function"==typeof n.value){var o=n.value;n.value=function(){for(var e,n,r=this,i=[],s=0;s<arguments.length;s++)i[s]=arguments[s];null===(e=this._monitor)||void 0===e||e.report("rtc_sdk_api_call",{sdk_api_name:t,message:i.filter((function(e){return"function"!=typeof e}))});var a=o.apply(this,i);return"function"==typeof(null==a?void 0:a.then)?a.then((function(e){var n;return null===(n=r._monitor)||void 0===n||n.report("rtc_sdk_callback",{sdk_callback_name:t,error_code:0,message:JSON.stringify(e||{})}),e})).catch((function(e){var n;throw null===(n=r._monitor)||void 0===n||n.report("rtc_sdk_callback",{sdk_callback_name:t,error_code:e.code,message:JSON.stringify(e.message||e.reason)}),e})):(null===(n=this._monitor)||void 0===n||n.report("rtc_sdk_callback",{sdk_callback_name:t,error_code:0,message:JSON.stringify(a||{})}),a)}}}}return{Client:function(e){function s(t){void 0===t&&(t={});var n=e.call(this)||this;return n.clsName="Client",n._inited=!1,n._reported={},n._ctx=new pe(t),n._monitor=n._ctx.monitor,wx.__client=n,n._monitor.report("rtc_sdk_api_call",{sdk_api_name:"constructor",message:t}),n}return t(s,e),s.prototype.init=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return r(this,void 0,void 0,(function(){return i(this,(function(t){if(!e||"string"!=typeof e)throw{code:d,reason:"appId must be a string"};if(this._inited)throw{code:f,reason:"Has been initialized"};return this._ctx.appId=e,this._inited=!0,this._monitor.updateHeader({rtc_app_id:e}),[2]}))}))},s.prototype.destroy=function(){for(var e,t,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return r(this,void 0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return(null===(e=this._room)||void 0===e?void 0:e.state)!==K.Connected?[3,2]:[4,this.leave().catch((function(){}))];case 1:n.sent(),n.label=2;case 2:this._room&&this._room.removeAllListeners("@room/client-banned");try{null===(t=this._room)||void 0===t||t.destroy(),this._inited=!1,this._ctx.reset(),delete this._room,this._reported={}}catch(e){}return this._monitor.destroy(),[2]}}))}))},s.prototype.setUserVisibility=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return r(this,void 0,void 0,(function(){var t;return i(this,(function(n){switch(n.label){case 0:if(t=e?"normalUser":"silentUser",this._ctx.getRole()===t)return[2];if(!this._room)return this._ctx.setRole(t),[2];if(this._room.state===K.Connecting||this._room.state===K.Reconnecting)throw{code:h,reason:"connecting"};return!e?[4,this._room.unpublish().catch((function(){}))]:[3,2];case 1:n.sent(),n.label=2;case 2:return[4,this._room.updateUserAttributes({role:t})];case 3:return n.sent(),this._ctx.setRole(t),[2]}}))}))},s.prototype.join=function(e,t,n){for(var o,s,a,c=[],u=3;u<arguments.length;u++)c[u-3]=arguments[u];return r(this,void 0,void 0,(function(){var r,c,u,l,p,m=this;return i(this,(function(i){switch(i.label){case 0:if(!t||"string"!=typeof t||!n||"string"!=typeof n)throw{code:d,reason:"roomId: "+t+" or userId: "+n+" must be a string"};if((null===(o=this._room)||void 0===o?void 0:o.state)===K.Connecting||(null===(s=this._room)||void 0===s?void 0:s.state)===K.Reconnecting)throw{code:h,reason:"connecting"};if((null===(a=this._room)||void 0===a?void 0:a.state)===K.Connected)throw{code:_,reason:"you are already in room"};r=Date.now(),this._joinRoomStartTs=r,this._monitor.report("join_room",{type:"begin",start:r}),this._monitor.updateHeader({rtc_app_id:this._ctx.appId,room_id:t,user_id:n}),this._room=new ce({ctx:this._ctx,emit:this.emit.bind(this)}),this._handleRoom(),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this._room.connect()];case 2:return i.sent(),[3,4];case 3:throw c=i.sent(),this._monitor.report("join_room",{type:"end",result:!1,reason:"connect_failed",start:r,elapse:Date.now()-r}),delete this._joinRoomStartTs,c;case 4:return[4,_e(0)];case 5:i.sent(),i.label=6;case 6:if(i.trys.push([6,8,,9]),!this._room)throw{code:k,reason:"leave_room",message:"call leaveRoom"};return this._ctx.roomId=t,this._ctx.userId=n,this._ctx.token=e,u=setTimeout((function(){m._monitor.report("join_room",{type:"end",result:!1,start:r,elapse:Date.now()-r,reason:"timeout_10"})}),1e4),[4,this._room.join()];case 7:return l=i.sent(),this._monitor.report("join_room",{type:"end",result:!0,start:r,elapse:Date.now()-r}),delete this._joinRoomStartTs,u&&clearTimeout(u),[2,l.clientId];case 8:throw p=i.sent(),this._monitor.report("join_room",{type:"end",result:!1,start:r,elapse:Date.now()-r,reason:p.reason}),delete this._joinRoomStartTs,u&&clearTimeout(u),p;case 9:return[2]}}))}))},s.prototype.leave=function(){for(var e,t,n,o=[],s=0;s<arguments.length;s++)o[s]=arguments[s];return r(this,void 0,void 0,(function(){return i(this,(function(o){switch(o.label){case 0:return(null===(e=this._room)||void 0===e?void 0:e.localRtmp)&&this.unpublish().catch((function(){})),[4,null===(t=this._room)||void 0===t?void 0:t.leave().catch((function(){}))];case 1:return o.sent(),null===(n=this._room)||void 0===n||n.destroy(),delete this._room,this._ctx.roomId="",this._ctx.userId="",this._ctx.token="",this._joinRoomStartTs&&this._monitor.report("join_room",{type:"end",result:!1,reason:"leave_room",start:this._joinRoomStartTs,elapse:Date.now()-this._joinRoomStartTs}),[2]}}))}))},s.prototype.publish=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r(this,void 0,void 0,(function(){var e,t,n,o,r,s;return i(this,(function(i){switch(i.label){case 0:if(!this._room||this._room.state!==K.Connected)throw{code:l,reason:"you are not in room"};if("silentUser"===this._ctx.getRole())throw{code:w,reason:"no publish permission"};e=Date.now(),t=u(),this._ctx.publishSessionId=t,i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this._room.publish()];case 2:return n=i.sent(),o=n.rtmp,r=n.streamId,this._monitor.report("rtc_publish",{start:e,elapse:Date.now()-e,stream_id:r,pc_session_id:t}),[2,o];case 3:throw s=i.sent(),this._monitor.report("rtc_publish_fail",{start:e,elapse:Date.now()-e,reason:s.reason,pc_session_id:t}),s;case 4:return[2]}}))}))},s.prototype.unpublish=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r(this,void 0,void 0,(function(){return i(this,(function(e){switch(e.label){case 0:if(!this._room||this._room.state!==K.Connected)throw{code:l,reason:"you are not in room"};return[4,this._room.unpublish()];case 1:return e.sent(),this._ctx.publishSessionId="",[2]}}))}))},s.prototype.subscribe=function(e,t){for(var o=[],s=2;s<arguments.length;s++)o[s-2]=arguments[s];return r(this,void 0,void 0,(function(){var o,r,s,a,c,_;return i(this,(function(i){switch(i.label){case 0:if(t=n(n({},{audio:!0,video:!0,screen:!1}),t),"string"!=typeof e)throw{code:d,reason:"userId: "+e+" must be a string"};if(!this._room||this._room.state!==K.Connected)throw{code:l,reason:"you are not in room"};o=Date.now(),r=u(),this._ctx.updateSubscribeId(e,t.screen,r),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this._room.subscribe(e,t)];case 2:return s=i.sent(),a=s.rtmp,c=s.streamId,this._monitor.report("rtc_subscribe",{start:o,elapse:Date.now()-o,stream_user_id:e,stream_id:c,peer_user_type:this._ctx.getRemoteUserType(e),pc_session_id:r}),[2,a];case 3:throw _=i.sent(),this._monitor.report("rtc_subscribe_fail",{start:o,reason:_.reason,elapse:Date.now()-o,stream_user_id:e,peer_user_type:this._ctx.getRemoteUserType(e),pc_session_id:r}),_;case 4:return[2]}}))}))},s.prototype.unsubscribe=function(e,t){for(var o=[],s=2;s<arguments.length;s++)o[s-2]=arguments[s];return r(this,void 0,void 0,(function(){return i(this,(function(o){switch(o.label){case 0:if(t=n(n({},{screen:!1}),t),"string"!=typeof e)throw{code:d,reason:"userId: "+e+" must be a string"};if(!this._room||this._room.state!==K.Connected)throw{code:l,reason:"you are not in room"};return[4,this._room.unsubscribe(e,t)];case 1:return o.sent(),this._ctx.updateSubscribeId(e,t.screen,""),[2]}}))}))},s.prototype.muteLocal=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return r(this,void 0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:if(!this._room||this._room.state!==K.Connected)throw{code:l,reason:"you are not in room"};return[4,this._room.muteLocal(e)];case 1:return[2,t.sent()]}}))}))},s.prototype.muteRemote=function(e,t){for(var o=[],s=2;s<arguments.length;s++)o[s-2]=arguments[s];return r(this,void 0,void 0,(function(){return i(this,(function(o){switch(o.label){case 0:if(t=n(n({},{screen:!1}),t),!this._room||this._room.state!==K.Connected)throw{code:l,reason:"you are not in room"};if(!e||"string"!=typeof e)throw{code:d,reason:"userId: "+e+" must be a string"};if("boolean"!=typeof(null==t?void 0:t.audio)&&"boolean"!=typeof(null==t?void 0:t.video))throw{code:d,reason:"options.audio or audio.video must be present"};return[4,this._room.muteRemote(e,t)];case 1:return[2,o.sent()]}}))}))},s.prototype._handleRoom=function(){var e=this;this._room&&this._room.on("@room/client-banned",(function(){var t;null===(t=e._room)||void 0===t||t.destroy(),e.destroy()}))},s.prototype.reportPusherStateChange=function(e,t){var n,o,r,i,s,a,c,u=me[e];if(u&&(t+="("+u+")"),this._monitor.report("rtc_media_statistics",{direction:"up",code:e,message:t,stream_id:null===(n=this._room)||void 0===n?void 0:n.getLocalStreamId(),sdk_api_name:"pusherStateChange",pc_session_id:this._ctx.publishSessionId}),1008===e){var d=this._ctx.userId+"_"+(null===(o=null==this?void 0:this._room)||void 0===o?void 0:o.localStreamId)+"_success";if(this._reported[d])return;this._reported[d]=!0,de(this._monitor,{event:"publish",streamID:(null===(r=this._room)||void 0===r?void 0:r.localStreamId)||"",streamAddr:(null===(i=this._room)||void 0===i?void 0:i.localRtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"success",reason:t,code:e,logHost:this._ctx.getLogHost()})}else if(-1307===e){d=this._ctx.userId+"_"+(null===(s=null==this?void 0:this._room)||void 0===s?void 0:s.localStreamId)+"_failed";if(this._reported[d])return;de(this._monitor,{event:"publish",streamID:(null===(a=this._room)||void 0===a?void 0:a.localStreamId)||"",streamAddr:(null===(c=this._room)||void 0===c?void 0:c.localRtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"failed",reason:t,code:e,logHost:this._ctx.getLogHost()})}},s.prototype.reportPusherNetStatusChange=function(e){var t;this._monitor.report("rtc_media_statistics",n(n({direction:"up",pc_session_id:this._ctx.publishSessionId},e),{stream_id:null===(t=this._room)||void 0===t?void 0:t.getLocalStreamId(),sdk_api_name:"pusherNetStatusChange"}))},s.prototype.reportPlayerStateChange=function(e,t,n,o){var r,i;void 0===o&&(o=!1);var s=me[t];if(s&&(n+="("+s+")"),this._monitor.report("rtc_media_statistics",{direction:"down",stream_user_id:e,code:t,message:n,stream_id:this._ctx.getRemoteStreamId(e,!!o),sdk_api_name:"playerStateChange",pc_session_id:this._ctx.getSubscribeId(e,o),peer_user_type:this._ctx.getRemoteUserType(e)}),2004===t){var a=e+"_"+(null==(c=null===(r=this._room)||void 0===r?void 0:r.getStreamInfo(e))?void 0:c.streamId)+"_success";if(this._reported[a])return;this._reported[a]=!0,de(this._monitor,{event:"subscribe",streamID:(null==c?void 0:c.streamId)||"",streamAddr:(null==c?void 0:c.rtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"success",reason:n,code:t,logHost:this._ctx.getLogHost()})}else if([-2301,3001,3002,3003,3005].includes(t)){var c;a=e+"_"+(null==(c=null===(i=this._room)||void 0===i?void 0:i.getStreamInfo(e))?void 0:c.streamId)+"_failed";if(this._reported[a])return;this._reported[a]=!0,de(this._monitor,{event:"subscribe",streamID:(null==c?void 0:c.streamId)||"",streamAddr:(null==c?void 0:c.rtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"failed",reason:n,code:t,logHost:this._ctx.getLogHost()})}},s.prototype.reportPlayerNetStatusChange=function(e,t,o){void 0===o&&(o=!1),this._monitor.report("rtc_media_statistics",n(n({direction:"down",stream_user_id:e},t),{stream_id:this._ctx.getRemoteStreamId(e,!!o),sdk_api_name:"playerNetStatusChange",pc_session_id:this._ctx.getSubscribeId(e,o),peer_user_type:this._ctx.getRemoteUserType(e)}))},Object.defineProperty(s.prototype,"state",{get:function(){var e,t;return null!==(t=null===(e=this._room)||void 0===e?void 0:e.state)&&void 0!==t?t:K.Init},enumerable:!1,configurable:!0}),o([fe(),le(1)],s.prototype,"init",null),o([fe(),le()],s.prototype,"destroy",null),o([fe(),le(1)],s.prototype,"setUserVisibility",null),o([fe(),le(3)],s.prototype,"join",null),o([fe(),le()],s.prototype,"leave",null),o([fe(),le()],s.prototype,"publish",null),o([fe(),le()],s.prototype,"unpublish",null),o([fe(),le(2)],s.prototype,"subscribe",null),o([fe(),le(2)],s.prototype,"unsubscribe",null),o([fe(),le(1)],s.prototype,"muteLocal",null),o([fe(),le(2)],s.prototype,"muteRemote",null),s}(a.EventEmitter),EVENTS:re,ERROR_CODES:N,getSdkVersion:function(){return"3.1.0"}}}));
