import store from "@/store/index";
import global from "@/common/global";
import baseUtil from "@/utils/baseUtil";

const uniRequest = {
  request(method, url, data = {}, loading = true, noHeader = true) {
    const NewHeader = noHeader
      ? {
          Authorization: "Bearer " + store.getters.token || "",
          clientid: global.CLIENT_ID,
        }
      : {};
    return new Promise((resolve, reject) => {
      // loading ? uni.showLoading() : "";
      if (baseUtil.isBlank(url)) {
        resolve({ code: 404, msg: "无效的请求地址" });
      }
      // if(url[0] != '/') url = '//////' + url;
      uni.request({
        method: method || "POST",
        url: global.BASE_API + url, // 接口地址
        timeout: 1000 * 60,
        data: data,
        header: NewHeader,
        success: (res) => {
          // uni.hideLoading();
          if (res.statusCode == 200) {
            if (res.data.code == 401) {
              store.dispatch("logout");
              return uni.showToast({
                title: "请登录后再进行操作",
                icon: "none",
              });
              // reject('未登录,获取数据失败');
            }
            let data = res.data;
            if (!res.data.code) {
              data.code = 200;
            }
            resolve(data);
          } else {
            if (res.data.code) {
              resolve(res.data);
            } else {
              resolve({
                code: res.statusCode || 1,
                msg: res.data.message || "请求失败",
              });
            }
          }
        },
        fail: (e) => {
          console.log("fail====>", e);
          // uni.hideLoading();
          // 目前只有地址错误,请求超时问题走fail,发现其他情况再来修改
          resolve({ code: 504, msg: "请求超时" });
        },
      });
    });
  },
  get(url, params, loading = true, noHeader = true) {
    return this.request("GET", url, params, loading, noHeader);
  },
  put(url, params, loading = true, noHeader = true) {
    return this.request("PUT", url, params, loading, noHeader);
  },
  post(url, data, loading = true, noHeader = true) {
    return this.request("POST", url, data, loading, noHeader);
  },
  deleted(url, data, loading = true, noHeader = true) {
    return this.request("DELETE", url, data, loading, noHeader);
  },
  /**
   * 选择并上传图片
   * 将本地资源上传到服务器，客户端发起一个 POST 请求，其中 content-type 为 multipart/form-data。
   * @param {data,success(task, res),progressFn(task, res)}
   * data: 上传时携带的参数; success: 上传成功后的处理函数; progressFn: 监听商城进度的函数
   * success与progressFn的参数说明
   * task: 当前上传任务; res: 当前事件的结果集;
   */
  uploadImg({ data = {}, success, progressFn }) {
    uni.chooseImage({
      success: (chooseImageRes) => {
        const tempFilePaths = chooseImageRes.tempFilePaths;
        const uploadTask = uni.uploadFile({
          url: global.UPLOAD_API, //上传接口地址
          filePath: tempFilePaths[0],
          header: {
            Authorization: "Bearer " + store.getters.token || "",
            clientid: global.CLIENT_ID,
          },
          name: "file",
          formData: data,
          success: (uploadFileRes) => {
            const data = baseUtil.toJson(uploadFileRes.data);
            if (data.code == 401) {
              store.dispatch("logout");
              return uni.showToast({
                title: "请登录后再进行操作",
                icon: "none",
              });
            }
            if (success) {
              success(data, uploadTask);
            }
          },
        });

        // 如果需要监听上传进度
        if (progressFn) {
          uploadTask.onProgressUpdate((progressUpdateRes) => {
            progressFn(uploadTask, progressUpdateRes);
            console.log("上传进度" + progressUpdateRes.progress);
            console.log(
              "已经上传的数据长度" + progressUpdateRes.totalBytesSent
            );
            console.log(
              "预期需要上传的数据总长度" +
                progressUpdateRes.totalBytesExpectedToSend
            );
          });
        }
      },
    });
  },
  /**
   * 上传文件
   * 将本地资源上传到服务器，客户端发起一个 POST 请求，其中 content-type 为 multipart/form-data。
   * @param {data,success(task, res),progressFn(task, res)}
   * data: 上传时携带的参数; success: 上传成功后的处理函数; progressFn: 监听商城进度的函数
   * success与progressFn的参数说明
   * task: 当前上传任务; res: 当前事件的结果集;
   */
  uploadFile({ filePath, data = {}, success, progressFn }) {
    const uploadTask = uni.uploadFile({
      url: global.UPLOAD_API, //上传接口地址
      header: {
        Authorization: "Bearer " + store.getters.token || "",
        clientid: global.CLIENT_ID,
      },
      filePath: filePath,
      name: "file",
      formData: data,
      success: (uploadFileRes) => {
        const data = baseUtil.toJson(uploadFileRes.data);
        if (data.code == 401) {
          store.dispatch("logout");
          return uni.showToast({
            title: "请登录后再进行操作",
            icon: "none",
          });
        }
        if (success) {
          success(data, uploadTask);
        }
      },
    });

    // 如果需要监听上传进度
    if (progressFn) {
      uploadTask.onProgressUpdate((progressUpdateRes) => {
        progressFn(uploadTask, progressUpdateRes);
        console.log("上传进度" + progressUpdateRes.progress);
        console.log("已经上传的数据长度" + progressUpdateRes.totalBytesSent);
        console.log(
          "预期需要上传的数据总长度" +
            progressUpdateRes.totalBytesExpectedToSend
        );
      });
    }
  },
  /**
   * 上传PDF或者图片格式简历
   * @param filePath
   * @param data
   * @param success
   */
  uploadResumeFile({ filePath, fileName, data = {}, success, error }) { 

    const uploadTask = uni.uploadFile({
      url: global.BASE_API + "/member/resume/attach?fileName=" + fileName,
      // url: global.BASE_API + "/chatai/chat/upload", //上传接口地址
      header: {
        Authorization: "Bearer " + store.getters.token || "",
        clientid: global.CLIENT_ID,
      },
      filePath: filePath,
      name: "file",
      formData: data,
      success: (uploadFileRes) => {
        const data = baseUtil.toJson(uploadFileRes.data);
        console.log(data,'data')
        if (data.code == 401) {
          store.dispatch("logout");
          return uni.showToast({
            title: "请登录后再进行操作",
            icon: "none",
          });
        }
        if (data.code == 200) {          
          success(data, uploadTask);
        }else {
          error(data)
        }
      },
      fail: (err) => {
        uni.$u.toast('上次失败')
      }
    });
  },
};

export default uniRequest;
