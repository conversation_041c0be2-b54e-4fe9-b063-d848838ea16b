import { param2Obj } from '@/utils/index.js'
export default class NavigateUtil {
	static switchTabs = [];

	static isTab(url) {
		return this.switchTabs.some(path => url.startsWith(path));
	}

	/**
	 * 如果能够后退（多层），则navigateBack，否则调用redirectTo
	 */
	static goto(url, isReLaunch = false) {
		if (isReLaunch) {
			uni.reLaunch({
				url: url
			});
			return false;
		}

		const pages = getCurrentPages()
		// route在低版本不兼容
		const index = pages.findIndex(item => url.indexOf(`/${item.route}`) > -1) || pages.findIndex(item => url
			.indexOf(`/${item.__route__}`) > -1)
		if (pages.length < 2 || index < 0) {
			if (this.isTab(url)) {
				if (url.indexOf('?') > -1) {
					const params = param2Obj(url)
					for (let key in params) {
						getApp().globalData[key] = params[key]
					}
				}
				uni.switchTab({
					url: url
				});
			} else {
				uni.navigateTo({
					url: url
				});
			}
		} else {
			const delta = pages.length - 1 - index
			uni.navigateBack({
				delta: delta
			});
		}
	}

	/**
	 * 后退到上一级
	 */
	static back(delta = 1, defaultUrl = '') {
		uni.navigateBack({
			delta: delta || 1,
			fail: () => {
				const url = defaultUrl || '/pages/index/index'
				uni.reLaunch({
					url: url
				});
			}
		});
	}

	/*
	  1.navigateTo
	  保留当前页面，跳转到应用内的某个页面，使用uni.navigateBack可以返回到原页面。
	  要注意的是navigateTo只能跳转的应用内非 tabBar 的页面的路径 , 路径后可以带参数；如果跳转url参数为tabBar的路径则无法进行跳转

	  2.redirectTo
	  关闭当前页面，跳转到应用内的某个页面。
	  需要跳转的应用内非 tabBar 的页面的路径，路径后可以带参数

	  3.reLaunch
	  关闭所有页面，打开到应用内的某个页面。
	  需要跳转的应用内页面路径 , 路径后可以带参数。参数与路径之间使用?分隔，参数键与参数值用=相连，不同参数用&分隔；如 'path?key=value&key2=value2'，与redirectTo不同的是如果跳转的页面路径是 tabBar 页面则不能带参数

	  4.switchTab
	  跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面。
	  需要跳转的 tabBar 页面的路径，路径后不能带参数
	*/
}