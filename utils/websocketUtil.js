import store from "@/store/index";
import global from "@/common/global";
import baseUtil from "@/utils/baseUtil";


class webSocketClass {
  constructor(url = global.WSS_URL, time = 10) {
    // this.url = `${url}?clientid=${global.CLIENT_ID}&Authorization=Bearer ${store.getters.token}`
    this.url = url;
    this.data = null;
    this.isCreate = false; // WebSocket 是否创建成功
    this.isConnect = false; // 是否已经连接
    this.isReconnect = false; // 是否发起重连
    this.reconnectTimes = 0; // 重连次数
    this.isInitiative = false; // 是否主动断开
    this.timeoutNumber = time; // 心跳检测间隔
    this.heartbeatTimer = null; // 心跳检测定时器
    this.reconnectTimer = null; // 断线重连定时器
    this.socketExamples = null; // websocket实例
    this.againTime = [50, 5000, 10000, 30000]; // 重连等待时间(单位ms)
    this.messageQueue = [];
    this.paused = false;
  }

  // 初始化websocket连接
  initSocket() {
    // 判断是否已经登录
    if (!store.getters.token) {
      // console.log("store.getters.token:", store.getters.token)
      return;
    }
    const _this = this;
    this.socketExamples = uni.connectSocket({
      url: _this.url,
      header: {
        "content-type": "application/json",
        Authorization: "Bearer " + store.getters.token || "",
        clientid: global.CLIENT_ID,
      },
      success: (res) => {
        _this.isCreate = true;
      },
      fail: (rej) => {
        console.error("connectSocket.error:", rej);
        _this.isCreate = false;
      },
    });
    this.createSocket();
  }
  // 创建websocket连接
  createSocket() {
    if (this.isCreate) {
      console.log("WebSocket 开始初始化");
      // 监听 WebSocket 连接打开事件
      try {
        this.socketExamples.onOpen(() => {
          if (this.paused) return;
          console.log("WebSocket 连接成功");
          this.isConnect = true;
          this.reconnectTimes = 0;
          this.isReconnect = false;
          clearInterval(this.heartbeatTimer);
          clearTimeout(this.reconnectTimer);
          // 打开心跳检测
          this.heartbeatCheck();
          this.processMessageQueue();
        });
        // 监听 WebSocket 接受到服务器的消息事件
        this.socketExamples.onMessage((res) => {
          if (!this.paused) {
            console.log("收到消息", res);
            let resp = baseUtil.toJson(res.data) || {};
            if (resp?.type !== "PONG") {
              uni.$emit("message", resp);
            }
          } else {
            console.log("消息发送被暂停。", res);
            console.log("消息发送被暂停。");
          }
        });
        // 监听 WebSocket 连接关闭事件
        this.socketExamples.onClose(() => {
          console.log("WebSocket 关闭了");
          this.isConnect = false;
          this.isReconnect = false;
          clearInterval(this.heartbeatTimer);
          clearTimeout(this.reconnectTimer);
          this.reconnect();
        });
        // 监听 WebSocket 错误事件
        this.socketExamples.onError((res) => {
          console.error("WebSocket 出错了", res);
          this.isInitiative = false;
        });
      } catch (error) {
        console.warn(error);
      }
    } else {
      console.warn("WebSocket 初始化失败!");
    }
  }

  // 发送消息预处理 会校验ws是否连接
  sendMsg(value) {
    if (!this.paused) {
      const param = JSON.stringify(value);
      return new Promise((resolve, reject) => {
        if (this.isConnect) {
          // 已连接 直接发送
          this.sendMessage(param, resolve, reject);
        } else {
          // ws连接尚未建立,先缓存起来
          this.messageQueue.push({ param, resolve, reject });
          this.checkConnect();
        }
      });
    }
  }
  // 连接成功建立以后,调用该方法,将缓存的消息发送出去
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const { param, resolve, reject } = this.messageQueue.shift();
      this.sendMessage(param, resolve, reject);
    }
  }
  // 实际发送消息
  sendMessage(param, resolve, reject) {
    if (this.paused) return;
    this.socketExamples.send({
      data: param,
      success() {
        // console.log(param, " 消息发送成功");
        resolve(true);
      },
      fail(error) {
        console.error("消息发送失败");
        reject(error);
      },
    });
  }
  // 开启心跳检测
  heartbeatCheck() {
    console.log("开启心跳,间隔:", this.timeoutNumber);
    this.data = { type: "PING" };
    this.heartbeatTimer = setInterval(() => {
      this.sendMsg(this.data);
    }, this.timeoutNumber * 1000);
  }

  checkConnect() {
    if (this.isConnect || this.isInitiative) {
      return;
    }
    if (this.isReconnect && this.reconnectTimes < 3) {
      return;
    }
    this.reconnectTimes = 0;
    this.isReconnect = false;
    clearTimeout(this.reconnectTimer);
    this.reconnect();
  }
  // 重新连接
  reconnect() {
    if (this.isConnect || this.isReconnect || this.isInitiative) {
      return;
    }
    this.isReconnect = true;
    let index = this.reconnectTimes++;
    if (index >= this.againTime.length) {
      index = this.againTime.length - 1;
    }
    let timeout = this.againTime[index];
    console.log("准备重连,timeout=" + timeout);
    this.reconnectTimer = setTimeout(() => {
      this.initSocket();
    }, timeout);
  }

  // 关闭 WebSocket 连接
  closeSocket(reason = "关闭") {
    const _this = this;
    if(!this.socketExamples){
      return;
    }
    this.socketExamples.close({
      reason,
      success() {
        _this.data = null;
        _this.isCreate = false;
        _this.isConnect = false;
        _this.isInitiative = true;
        _this.socketExamples = null;
        _this.isReconnect = false;
        _this.reconnectTimes = 0;
        clearInterval(_this.heartbeatTimer);
        clearTimeout(_this.reconnectTimer);
        console.log("关闭 WebSocket 成功");
      },
      fail(e) {
        console.log("关闭 WebSocket 失败 ", e);
      },
    });
  }
  // 暂停发送
  pauseSending() {
    this.paused = true;
    this.sendMsg("");
    console.log(this.paused);
  }
  // 恢复发送
  resumeSending() {
    this.paused = false;
  }
}

export default webSocketClass;
