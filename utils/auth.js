import storageUtil from "@/utils/storageUtil";
import store from "@/store/index";

export function checkLogin(isNavigateToLogin = true) {
  if (!store.getters.hasLogin) {
    if (isNavigateToLogin == true) {
      storageUtil.setItem("redirectUrl", getCurPageUrl());
      uni.navigateTo({
        url: "/pages/public/oauth",
      });
    }
    return false;
  } else {
    return true;
  }
}

export function getCurPageUrl() {
  let curPage = getCurPage();
  //在微信小程序或是app中，通过curPage.options；如果是H5，则需要curPage.$route.query（H5中的curPage.options为undefined，所以刚好就不需要条件编译了）
  let options = curPage.options;
  let url = curPage.route;
  if (options != null) {
    let query = "";
    for (let k in options) {
      if (query != "") {
        query += "&";
      }
      query += k + "=" + options[k];
    }
    if (query != "") {
      url += "?" + query;
    }
  }
  if (!url.startsWith("/")) {
    url = "/" + url;
  }
  return url;
}

export function getCurPage() {
  let pages = getCurrentPages();
  let curPage = pages[pages.length - 1];
  return curPage;
}
