/**
 * Created by jiachen<PERSON> on 16/11/18.
 */
export function parseTime(date, fmt) {
  if (date == undefined || date == "" || date == null) {
    return "";
  }
  if (typeof date == "string") {
    date = date.replace(".", "-").replace(".", "-").replace(".", "-");
  }

  date = date == undefined ? new Date() : new Date(date);
  //date = typeof date == 'number' ? new Date(date) : date;
  fmt = fmt || "yyyy-MM-dd HH:mm:ss";
  let obj = {
    y: date.getFullYear(), // 年份，注意必须用getFullYear
    M: date.getMonth() + 1, // 月份，注意是从0-11
    d: date.getDate(), // 日期
    q: Math.floor((date.getMonth() + 3) / 3), // 季度
    w: date.getDay(), // 星期，注意是0-6
    H: date.getHours(), // 24小时制
    h: date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, // 12小时制
    m: date.getMinutes(), // 分钟
    s: date.getSeconds(), // 秒
    S: date.getMilliseconds(), // 毫秒
  };
  let week = ["日", "一", "二", "三", "四", "五", "六"];
  let i, j, len;
  for (i in obj) {
    fmt = fmt.replace(new RegExp(i + "+", "g"), function (m) {
      let val = obj[i] + "";
      if (i == "w") return (m.length > 2 ? "星期" : "周") + week[val];
      for (j = 0, len = val.length; j < m.length - len; j++) val = "0" + val;
      return m.length == 1 ? val : val.substring(val.length - m.length);
    });
  }
  return fmt;
}

export function setTime(time) {
  if (!time) {
    return "";
  } else if (time.includes("-")) {
    return time.split("-")[0] + "年" + time.split("-")[1] + "月";
  } else if (time.includes(".")) {
    return time.split(".")[0] + "年" + time.split(".")[1] + "月";
  } else {
    return time;
  }
}

/*
以当前日期为基础。追加天数
d:要追加的天数
*/
export function isSameDay(date1, date2) {
  if (date1 == null || date2 == null) return false;
  var day1 = new Date(date1).getDate();
  var day2 = new Date(date2).getDate();
  return day1 == day2;
}

/*
以当前日期为基础。追加天数
d:要追加的天数
*/
export function isSameYear(date1, date2) {
  if (date1 == null || date2 == null) return false;
  var year1 = new Date(date1).getFullYear();
  var year2 = new Date(date2).getFullYear();
  return year1 == year2;
}

/*
以当前日期为基础。追加天数
d:要追加的天数
*/
export function addDays(date, d) {
  if (date == null) return null;
  var Year = date.getFullYear(); //当前年
  var Month = date.getMonth() + 1; //当前月
  var Day = date.getDate(); //当前日
  var dayNumber = new Date(Year, Month + 1, 0).getDate(); //当月天数

  for (var i = 0; i < d; i++) {
    Day++;
    if (Day > dayNumber) {
      Day = 1;
      Month++;
      if (Month > 12) {
        Month = 1;
        Year++;
      }
    }
  }
  return new Date(Year, Month - 1, Day);
}

/**
 * 价格显示格式化 1,231.00元
 * @param money 价格值
 * @param precision 精度
 * @param isUnit 是否需要单位(人民币：元、万元),默认不写
 * @param unit 单位, 不填则在前面加'￥'
 * @returns {string}
 */
export function fmMoney(money, precision, isUnit) {
  if (
    money === undefined ||
    money === null ||
    money.toString().replace(/^\s+|\s+$/g, "") === ""
  )
    money = 0;
  let isMinus = (money + "").indexOf("-") > -1;
  precision = precision || 0;
  if (precision > 2) precision = 2;
  isUnit = isUnit == undefined ? false : isUnit;
  let unit = "",
    i;
  if (isUnit) unit = "￥";
  money = parseFloat((money + "").replace(/[^\d\.]/g, ""));
  if (money == 0) {
    money = "0.";
    for (i = 0; i < precision; i++) {
      money += "0";
    }
    return unit + money;
  }
  money = money.toFixed(precision) + "";
  //_integer -- 整数部分，_dec -- 小数部分, _comma--每三位数用逗号分隔
  let _integer = money.split(".")[0].split("").reverse(),
    _dec = "",
    _comma = "";
  if (precision > 0) {
    _dec = "." + money.split(".")[1];
  }
  // if(_integer.length>4) {
  //   //过万元
  //   if(isUnit) unit = "万元";
  //   else unit = "W";
  //   return fmMoney(money / 10000, 2, false) + unit;
  // }
  for (i = 0; i < _integer.length; i++) {
    _comma +=
      _integer[i] + ((i + 1) % 3 == 0 && i + 1 != _integer.length ? "," : "");
  }
  return (
    unit + (isMinus ? "-" : "") + _comma.split("").reverse().join("") + _dec
  );
}

export function floatAdd(arg1, arg2) {
  let r1, r2, m;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  let d = Math.max(r1, r2);
  m = Math.pow(10, d);
  return ((arg1 * m + arg2 * m) / m).toFixed(d);
}

export function floatSub(arg1, arg2) {
  let r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  //动态控制精度长度
  n = Math.max(r1, r2);
  //n=(r1=r2)?r1:r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
}

export function floatMul(arg1, arg2) {
  let m = 0,
    s1 = arg1.toString(),
    s2 = arg2.toString();
  try {
    m += s1.split(".")[1].length;
  } catch (e) {}
  try {
    m += s2.split(".")[1].length;
  } catch (e) {}
  return (
    (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) /
    Math.pow(10, m)
  );
}

export function floatDiv(arg1, arg2) {
  let t1 = 0,
    t2 = 0,
    r1,
    r2;
  try {
    t1 = arg1.toString().split(".")[1].length;
  } catch (e) {}
  try {
    t2 = arg2.toString().split(".")[1].length;
  } catch (e) {}
  r1 = Number(arg1.toString().replace(".", ""));
  r2 = Number(arg2.toString().replace(".", ""));
  return (r1 / r2) * Math.pow(10, t2 - t1);
}

export function formatTime(time, option) {
  time = +time * 1000;
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

export function greeting() {
  const now = new Date(),
    hour = now.getHours();
  if (hour < 6) {
    return "凌晨好！";
  } else if (hour < 9) {
    return "早上好！";
  } else if (hour < 12) {
    return "上午好！";
  } else if (hour < 14) {
    return "中午好！";
  } else if (hour < 17) {
    return "下午好！";
  } else if (hour < 19) {
    return "傍晚好！";
  } else if (hour < 22) {
    return "晚上好！";
  } else {
    return "夜里好！";
  }
}

// 格式化时间
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 *get getByteLen
 * @param {Sting} val input value
 * @returns {number} output value
 */
export function getByteLen(val) {
  let len = 0,
    i;
  for (i = 0; i < val.length; i++) {
    if (val[i].match(/[^\x00-\xff]/gi) != null) {
      len += 1;
    } else {
      len += 0.5;
    }
  }
  return Math.floor(len);
}

export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

export function param2Obj(url) {
  const search = url.split("?")[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"') +
      '"}'
  );
}

export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

export function objectMerge(target, source) {
  /* Merges two  objects,
     giving the last one precedence */

  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += "" + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

export const pickerOptions = [
  {
    text: "今天",
    onClick(picker) {
      const end = new Date();
      const start = new Date(new Date().toDateString());
      end.setTime(start.getTime());
      picker.$emit("pick", [start, end]);
    },
  },
  {
    text: "最近一周",
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(end.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit("pick", [start, end]);
    },
  },
  {
    text: "最近一个月",
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      picker.$emit("pick", [start, end]);
    },
  },
  {
    text: "最近三个月",
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      picker.$emit("pick", [start, end]);
    },
  },
];

export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "shallowClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

//生成包含数字 字母  下划线的随机数
export function createRandom(len) {
  let str = "",
    arr = [
      "_",
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "a",
      "b",
      "c",
      "d",
      "e",
      "f",
      "g",
      "h",
      "i",
      "j",
      "k",
      "l",
      "m",
      "n",
      "o",
      "p",
      "q",
      "r",
      "s",
      "t",
      "u",
      "v",
      "w",
      "x",
      "y",
      "z",
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
    ];
  for (let i = 0; i < len; i++) {
    let pos = Math.round(Math.random() * (arr.length - 1));
    str += arr[pos];
  }
  return str;
}

/**
 * 分离文件扩展名
 * @param fileName 文件名称
 * @returns {string|string} 返回文件扩展名,没有返回null
 */
export function getFileSuffix(fileName) {
  let ext = null;
  if (
    fileName !== undefined &&
    fileName !== null &&
    fileName.toString().replace(/^\s+|\s+$/g, "") !== ""
  ) {
    let dotIndex = fileName.lastIndexOf(".");
    if (dotIndex > -1) {
      ext = fileName.substring(dotIndex).replace(".", "");
    }
  }
  return ext;
}

/**
 * 分离文件名称
 * @param fileName 文件名称
 * @returns {string|string} 返回无后缀文件名,没有返回原名称
 */
export function getNoSuffixFileName(fileName) {
  let ext = fileName;
  if (
    fileName !== undefined &&
    fileName !== null &&
    fileName.toString().replace(/^\s+|\s+$/g, "") !== ""
  ) {
    let dotIndex = fileName.lastIndexOf(".");
    if (dotIndex > -1) {
      ext = fileName.substring(0, dotIndex);
    }
  }
  return ext;
}

export function createCode(length) {
  //首先默认code为空字符串
  var code = "";
  //设置长度，这里看需求，我这里设置了4
  var codeLength = length || 4;
  //设置随机字符
  var random = new Array(
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z"
  );
  //循环codeLength 我设置的4就是循环4次
  for (var i = 0; i < codeLength; i++) {
    //设置随机数范围,这设置为0 ~ 36
    var index = Math.floor(Math.random() * 36);
    //字符串拼接 将每次随机的字符 进行拼接
    code += random[index];
  }
  //将拼接好的字符串赋值给展示的Value
  return code;
}

export function base64Encode(input) {
  const _keyStr =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  var output = "",
    chr1,
    chr2,
    chr3,
    enc1,
    enc2,
    enc3,
    enc4,
    i = 0;
  input = utf8Encode(input);
  while (i < input.length) {
    chr1 = input.charCodeAt(i++);
    chr2 = input.charCodeAt(i++);
    chr3 = input.charCodeAt(i++);
    enc1 = chr1 >> 2;
    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
    enc4 = chr3 & 63;
    if (isNaN(chr2)) {
      enc3 = enc4 = 64;
    } else if (isNaN(chr3)) {
      enc4 = 64;
    }
    output =
      output +
      _keyStr.charAt(enc1) +
      _keyStr.charAt(enc2) +
      _keyStr.charAt(enc3) +
      _keyStr.charAt(enc4);
  }
  return output;
}

export function base64Decode(input) {
  const _keyStr =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  var output = "",
    chr1,
    chr2,
    chr3,
    enc1,
    enc2,
    enc3,
    enc4,
    i = 0;
  input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
  while (i < input.length) {
    enc1 = _keyStr.indexOf(input.charAt(i++));
    enc2 = _keyStr.indexOf(input.charAt(i++));
    enc3 = _keyStr.indexOf(input.charAt(i++));
    enc4 = _keyStr.indexOf(input.charAt(i++));
    chr1 = (enc1 << 2) | (enc2 >> 4);
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    chr3 = ((enc3 & 3) << 6) | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  output = utf8Decode(output);
  return output;
}

// private method for UTF-8 encoding
export function utf8Encode(string) {
  string = string.replace(/\r\n/g, "\n");
  var utftext = "";
  for (var n = 0; n < string.length; n++) {
    var c = string.charCodeAt(n);
    if (c < 128) {
      utftext += String.fromCharCode(c);
    } else if (c > 127 && c < 2048) {
      utftext += String.fromCharCode((c >> 6) | 192);
      utftext += String.fromCharCode((c & 63) | 128);
    } else {
      utftext += String.fromCharCode((c >> 12) | 224);
      utftext += String.fromCharCode(((c >> 6) & 63) | 128);
      utftext += String.fromCharCode((c & 63) | 128);
    }
  }
  return utftext;
}

// private method for UTF-8 decoding
export function utf8Decode(utftext) {
  var string = "",
    i = 0,
    c = 0,
    c1 = 0,
    c2 = 0,
    c3 = 0;
  while (i < utftext.length) {
    c = utftext.charCodeAt(i);
    if (c < 128) {
      string += String.fromCharCode(c);
      i++;
    } else if (c > 191 && c < 224) {
      c2 = utftext.charCodeAt(i + 1);
      string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
      i += 2;
    } else {
      c2 = utftext.charCodeAt(i + 1);
      c3 = utftext.charCodeAt(i + 2);
      string += String.fromCharCode(
        ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
      );
      i += 3;
    }
  }
  return string;
}

//启用禁用状态
export const StateStatus = {
  FREEZE: 0,
  ENABLED: 1,
};
Object.freeze(StateStatus);

/**
 * 传入枚举集合和value,返回命中的枚举Label
 * @param e 枚举
 * @param value 值
 */
export function getEnumLabelByValue(e, value) {
  if (e && value) {
    let item = e.find((m) => m.value === value);
    if (item) {
      return item.label;
    }
  }
  return null;
}

/**
 * 传入枚举集合和key,返回命中的枚举Label
 * @param e 枚举
 * @param key 值
 */
export function getEnumLabel(e, key) {
  if (e && key) {
    let item = e.find((m) => m.key == key);
    if (item) {
      return item.label;
    }
  }
  return null;
}

/**
 * 传入枚举集合和key,返回命中的枚举Value
 * @param e 枚举
 * @param key 值
 */
export function getEnumValue(e, key) {
  if (e && key) {
    let item = e.find((m) => m.key == key);
    if (item) {
      return item.value;
    }
  }
  return null;
}

/**
 * 价格显示格式化 1231元
 * @param money 价格值
 * @param precision 精度
 * @returns {string}
 */
export function fmPrice(money, precision = 0) {
  if (
    money === undefined ||
    money === null ||
    money.toString().replace(/^\s+|\s+$/g, "") === ""
  )
    money = 0;
  let arr = (money + "").split(".");
  let isPrecision = arr.length > 1 && parseInt(arr[1]) > 0;
  if (typeof precision != "number" || precision < 0) {
    precision = 0;
  }
  if (isPrecision && precision > 0) {
    money = parseFloat(money).toFixed(precision);
  } else {
    money = parseInt(money);
  }
  return money;
}

export function getSequenceLetter(index) {
  const arr = [
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
  ];
  return arr[index];
}

/**
 * JS判断两个数组是否相等
 * @param {Array} arr1
 * @param {Array} arr2
 * @returns {boolean} 返回true 或 false
 */
export function arrayEqual(arr1, arr2) {
  if (arr1 === arr2) return true;
  if (arr1.length != arr2.length) return false;
  for (var i = 0; i < arr1.length; ++i) {
    if (arr1[i] !== arr2[i]) return false;
  }
  return true;
}

// 数字处理
export function numberTransform(value) {
  const newValue = ["", "", ""];
  let fr = 1000;
  let num = 3;
  let text1 = "";
  let fm = 1;
  while (value / fr >= 1) {
    fr *= 10;
    num += 1;
    // console.log('数字', value / fr, 'num:', num)
  }
  if (num <= 4) {
    // 千
    newValue[0] = value + "";
    newValue[1] = "";
    // newValue[0] = parseInt(value / 1000) + ''
    // newValue[1] = '千'
  } else if (num <= 8) {
    // 万
    text1 = parseInt(num - 4) / 3 > 1 ? "千万" : "万";
    // tslint:disable-next-line:no-shadowed-variable
    fm = text1 === "万" ? 10000 : 10000000;
    if (value % fm === 0) {
      newValue[0] = parseInt(value / fm) + "";
    } else {
      newValue[0] = parseFloat(value / fm).toFixed(2) + "";
    }
    newValue[1] = text1;
  } else if (num <= 16) {
    // 亿
    text1 = (num - 8) / 3 > 1 ? "千亿" : "亿";
    text1 = (num - 8) / 4 > 1 ? "万亿" : text1;
    text1 = (num - 8) / 7 > 1 ? "千万亿" : text1;
    // tslint:disable-next-line:no-shadowed-variable
    fm = 1;
    if (text1 === "亿") {
      fm = 100000000;
    } else if (text1 === "千亿") {
      fm = 100000000000;
    } else if (text1 === "万亿") {
      fm = 1000000000000;
    } else if (text1 === "千万亿") {
      fm = 1000000000000000;
    }
    if (value % fm === 0) {
      newValue[0] = parseInt(value / fm) + "";
    } else {
      newValue[0] = parseFloat(value / fm).toFixed(2) + "";
    }
    newValue[1] = text1;
  }
  if (value < 1000) {
    newValue[0] = value + "";
    newValue[1] = "";
  }
  return newValue.join("");
}

/**
 * 数据脱敏
 * @param phoneNumber
 * @returns {*}
 */
export function desensitizePhoneNumber(phoneNumber) {
  // 进行脱敏处理，例如将手机号码中的一部分数字替换为星号
  // 使用正则表达式匹配中国手机号码格式
  const regExp = /^(\d{3})\d{4}(\d{4})$/;
  // 对中间四位数字进行脱敏处理
  return phoneNumber.replace(regExp, "$1****$2");
}

// 辅助函数：获取年份范围
export function getYearRange(start, end) {
  const range = [];
  for (let i = end; i >= start; i--) {
    range.push(i.toString() + "年");
  }
  return range;
}
// 辅助函数：获取月份范围
export function getMonthRange(start, end) {
  const range = [];
  for (let i = end; i >= start; i--) {
    range.push((i > 9 ? i : "0" + i) + "月");
  }
  return range;
}

// 辅助函数：获取薪资范围
export function getSalaryRange(start, end, type) {
  const range = type === "init" ? ["面议"] : [];
  for (let i = start; i <= end; i++) {
    range.push(i.toString());
  }
  return range;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || "id",
    parentId: parentId || "parentId",
    childrenList: children || "children",
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}

/**
 * 将数字转换为每三位逗号分割的字符串
 * @param {number|string} num - 需要转换的数字或数字字符串
 * @returns {string} 格式化后的字符串
 */
export function formatNumberWithCommas(num) {
  // 处理字符串类型的数字输入
  if (typeof num === "string") {
    // 移除可能存在的逗号
    num = num.replace(/,/g, "");
    // 尝试转换为数字
    num = isNaN(num) ? 0 : Number(num);
  }

  // 处理非数字输入
  if (typeof num !== "number" || isNaN(num)) {
    return "0";
  }

  // 处理小数情况
  const parts = num.toString().split(".");
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  return parts.join(".");
}
