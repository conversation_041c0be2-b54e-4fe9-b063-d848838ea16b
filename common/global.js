const PROD_BASE_URL = "https://miniapi.yujian.chat";
const PROD_BASE_WSS_URL = "wss://miniapi.yujian.chat";
const PROD_API = PROD_BASE_URL + "/api";
const PROD_WSS_URL = PROD_BASE_WSS_URL + "/api/resource/websocket";
const PROD_STATIC_URL = "https://static.yujian.chat/";

// const TEST_BASE_URL = "https://testapi.yujian.chat";
const TEST_BASE_URL = "https://edu.yujian.chat";
const TEST_BASE_WSS_URL = "wss://testapi.yujian.chat";
const TEST_API = TEST_BASE_URL + "/app/api";
const TEST_WSS_URL = TEST_BASE_WSS_URL + "/app/api/resource/websocket";
const TEST_STATIC_URL = "https://testapi.yujian.chat/static/";

// const TEST_BASE_URL = "https://miniapi.yujian.chat";
// const TEST_BASE_WSS_URL = "wss://miniapi.yujian.chat";
// const TEST_API = TEST_BASE_URL + "/api";
// const TEST_WSS_URL = TEST_BASE_WSS_URL + "/api/resource/websocket";
// const TEST_STATIC_URL = "https://static.yujian.chat/";

// const DEV_BASE_URL = "https://miniapi.yujian.chat";
// const DEV_BASE_WSS_URL = "wss://miniapi.yujian.chat";
// const DEV_BASE_API = DEV_BASE_URL + "/api";
// const DEV_WSS_URL = DEV_BASE_WSS_URL + "/api/resource/websocket";
// const DEV_STATIC_URL = "https://static.yujian.chat/";

// const DEV_BASE_URL = "https://testapi.yujian.chat";
const DEV_BASE_URL = "https://edu.yujian.chat";
const DEV_BASE_WSS_URL = "wss://testapi.yujian.chat";
const DEV_BASE_API = DEV_BASE_URL + "/app/api";
const DEV_WSS_URL = DEV_BASE_WSS_URL + "/app/api/resource/websocket";
const DEV_STATIC_URL = "https://testapi.yujian.chat/static/";

// const DEV_BASE_URL = "http://*************:8095";
// const DEV_BASE_WSS_URL = "ws://*************:8095";
// const DEV_BASE_API = DEV_BASE_URL + "/api";
// const DEV_WSS_URL = DEV_BASE_WSS_URL + "/api/resource/websocket";
const EVALUATION_BASE_URL = "https://api.woceping.com";

let BASE_API, WSS_URL, BASE_URL, WSS_BASE_URL, STATIC_URL;
// #ifdef MP-WEIXIN
const version = uni.getAccountInfoSync().miniProgram.envVersion;
const APPID = uni.getAccountInfoSync().miniProgram.appId;
switch (version) {
  // 开发版
  case "develop":
    BASE_API = DEV_BASE_API;
    WSS_URL = DEV_WSS_URL;
    BASE_URL = DEV_BASE_URL;
    WSS_BASE_URL = DEV_BASE_WSS_URL;
    STATIC_URL = DEV_STATIC_URL;
    break;
  // 	体验版
  case "trial":
    BASE_API = TEST_API;
    WSS_URL = TEST_WSS_URL;
    BASE_URL = TEST_BASE_URL;
    WSS_BASE_URL = TEST_BASE_WSS_URL;
    STATIC_URL = TEST_STATIC_URL;
    break;
  // 正式版
  case "release":
    BASE_API = PROD_API;
    WSS_URL = PROD_WSS_URL;
    BASE_URL = PROD_BASE_URL;
    WSS_BASE_URL = PROD_BASE_WSS_URL;
    STATIC_URL = PROD_STATIC_URL;
    break;
}
// #endif

const CLIENT_ID = "eab5997c5484c03ade20474a2f73a5db";
const UPLOAD_API = `${BASE_API}/resource/oss/upload/member`;
// 讯飞
const API_KEY = "8abfb67391fa3a1ca502cc47eaceabad";
const API_SECRET = "N2UwOWE2ZDU2NmZkOTM3ZGFjYTMzNDlj";
export default {
  BASE_API,
  BASE_URL,
  WSS_BASE_URL,
  WSS_URL,
  UPLOAD_API,
  APPID,
  CLIENT_ID,
  API_KEY,
  API_SECRET,
  STATIC_URL,
  EVALUATION_BASE_URL,
};

export const wsUrl = "wss://miniapi.jobtap.com.cn/ws/audio/asr";
export const wsTtsUrl = "wss://miniapi.jobtap.com.cn/ws/tts";
export const apiBaseUrl = "https://miniapi.jobtap.com.cn";
