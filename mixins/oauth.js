import { mapActions } from "vuex";
import global from "@/common/global";

export default {
  data() {
    return {
      firstLoad: true,
      code: "",
      isAgree: false,
      invitePopupShow: false,
    };
  },
  onShow() {},
  onLoad() {},
  computed: {},
  methods: {
    ...mapActions(["login"]),
    loadCode() {
      uni.login({
        provider: "weixin",
        scopes: ["auth_base", "auth_user"],
        success: (loginRes) => {
          if (loginRes != null && loginRes.errMsg == "login:ok") {
            // 获取用户信息
            this.code = loginRes.code;
            if (this.firstLoad) {
              this.firstLoad = false;
            }
          }
        },
        fail: (e) => {
          console.error(e);
        },
      });
    },
    toIndex() {
      this.navigateUtil.goto("/pages/index/index");
    },
    navBack() {
      let redirectUrl = this.storageUtil.getItem("redirectUrl");
      if (redirectUrl) {
        this.navigateUtil.goto(redirectUrl);
      } else {
        this.navigateUtil.goto("/pages/index/index");
      }
    },
    authFail(msg) {
      uni.showModal({
        showCancel: false,
        title: "授权失败",
        content: msg || "您取消了授权",
      });
    },
    async authSuccess(checkIsVip = true) {
      try {
        // if (checkIsVip) {
        //   let userInfo = await this.$store.dispatch("refreshInfo");
        //   if (userInfo.isVip != 1) {
        //     this.invitePopupShow = true;
        //     return;
        //   }
        // }
        uni.showToast({
          title: "正在跳转...",
          icon: "none",
        });
        setTimeout(() => {
          let redirectUrl = this.storageUtil.getItem("redirectUrl");
          if (redirectUrl) {
            this.navigateUtil.goto(redirectUrl, true);
          } else {
            this.navigateUtil.goto("/pages/index/index", true);
          }
        }, 1400);
      } catch (error) {
        //
      }
    },
    async openIdLogin() {
      const form = {
        code: this.code,
        appid: global.APPID,
        clientId: global.CLIENT_ID,
        grantType: "oauth",
        needMobile: false,
      };
      this.login(form)
        .then((res) => {
          uni.hideLoading();
          this.authSuccess();
        })
        .catch((msg) => {
          uni.hideLoading();
          this.authFail(msg);
        });
    },
    async getPhoneNumber(e) {
      uni.checkSession({
        success: (c) => {
          //获取授权信息
          if (e.detail.errMsg == "getPhoneNumber:ok") {
            if (!this.isAgree) {
              uni.showToast({
                title: "请先勾选协议",
                icon: "none",
              });
              return;
            }
            uni.showLoading({
              title: "授权中",
            });

            // 获取用户信息
            let form = {
              code: this.code,
              appid: global.APPID,
              clientId: global.CLIENT_ID,
              grantType: "oauth",
              phoneNumberParam: e.detail,
            };
            this.login(form)
              .then((res) => {
                uni.hideLoading();

                this.authSuccess();
              })
              .catch((msg) => {
                uni.hideLoading();
                this.authFail(msg);
              });
          } else {
            this.authFail();
          }
        },
        fail: (e) => {
          this.loadCode();
        },
      });
    },
  },
};
