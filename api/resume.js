import request from "@/utils/request.js";
/**
 * @title  获取简历信息
 */
export const GET_RESUME_DATA = () => {
  return request.get("/member/resume/latest");
};
/**
 * @title  修改基本信息
 */
export const EDIT_INFO = (data) => {
  return request.put("/member/baseInfo", data);
};

/**
 * @title  新增简历信息信息
 */
export const ADD_RESUME_ITEM = (data) => {
  return request.post("/member/experience", data);
};

/**
 * @title   删除简历信息信息
 */
export const DEL_RESUME_ITEM = (ids) => {
  return request.deleted(`/member/experience/${ids}`);
};

/**
 * @title   编辑简历信息信息
 */
export const EDIT_RESUME_ITEM = (data) => {
  return request.put(`/member/experience`, data);
};

/**
 * @title   查看简历信息信息
 */
export const GET_RESUME_ITEM = (id) => {
  return request.get(`/member/experience/${id}`);
};

/**
 * @title  删除附件简历
 */
export const DEL_RESUME_FILE = (ossId) => {
  return request.deleted(`/member/resume/attach/${ossId}`);
};

/**
 * @title   调整简历模块
 */
export const SET_RESUME_SORT = (data) => {
  return request.put(`/member/resume/sort`, data);
};

/**
 * @title   获取目标职位
 */
export const GET_TARGET_DATA = (id) => {
  return request.get(`/member/jobTarget/${id}`);
};

/**
 * @title   修改目标职位
 */
export const EDIT_TARGET_DATA = (data) => {
  return request.put(`/member/jobTarget`, data);
};

/**
 * @title   删除目标职位
 */
export const DELETE_TARGET_DATA = (ids) => {
  return request.deleted(`/member/jobTarget/${ids}`);
};

/**
 * @title   增加目标职位
 */
export const ADD_TARGET_DATA = (data) => {
  return request.post(`/member/jobTarget`, data);
};

/**
 * @title   删除求职期望
 */
export const DELETE_JOBWANT_DATA = (ids) => {
  return request.deleted(`/member/jobWant/${ids}`);
};

/**
 * @title   一键优化简历
 */
export const OPTIMIZATION_OPT = (data) => {
  return request.post(`/member/resume/oneClick`, data, false);
};

/**
 * @title   在线简历生成PDF
 */
export const RESUME_GENERATE_PDF = () => {
  return request.post(`/member/resume/generate`);
};

/**
 * @title   问候与引导
 */
export const GET_HELLO_MSG = (data) => {
  return request.post(`/member/chat/hello`, data);
};

/**
 * @title   停止聊天
 */
export const STOP_MSG = (data) => {
  return request.put(`/member/chat/shutup`, data);
};

/**
 * @title   获取求职期望
 */
export const GET_JOB_WANT = (id) => {
  return request.get(`/member/jobWant/${id}`);
};

// 获取全网数据信息
export const GET_DATA_TOTAL = () => {
  return request.get(`/data/total`);
};

// 获取排行数据
export const GET_DATA_RANK = () => {
  return request.get(`/data/rank`);
};

// 意向匹配统计
export const GET_MATCH_COUNT = () => {
  return request.get(`/member/match/count`);
};

// 意向企业榜单
export const GET_MATCH_COMPANY = () => {
  return request.get(`/member/match/company`);
};

/**
 * @title  开始匹配
 */
export const START_MATCH = (data) => {
  return request.post(`/member/match/start`, data);
};
// 获取思维链
export const GET_MATCH_CHAIN = (id) => {
  return request.get(`/member/match/chain/${id}`);
};
/**
 * @title  开始测评
 */
export const START_EVAL = (data) => {
  return request.post(`/member/match/eval`, data);
};
/**
 * @title   创建简历模版
 */
export const CREATE_RESUME_TEMPLATE = () => {
  return request.get(`/member/resume/template`, {}, false);
};

/**
 * @title   调整简历模版
 */
export const EDIT_RESUME_TEMPLATE = (data) => {
  return request.put(`/member/resume/template`, data, false);
};

/**
 * @title   使用模板创建简历
 */
export const USE_RESUME_TEMPLATE = (data) => {
  return request.post(`/member/resume/create`, data);
};

/**
 * @title   查看模板创建简历
 */
export const GET_RESUME_TEMPLATE = (id) => {
  return request.get(`/member/resume/template/${id}`, {}, false);
};

/**
 * @title   首页引导数据
 */
export const INDEX_GUIDE_DATA = () => {
  return request.get(`/home/<USER>
};
export const INDEX_GUIDE_DATA_NO_TOKEN = () => {
  return request.get(`/home/<USER>
};

/**
 * @title   获取邮箱话术
 */
export const GET_EMAIL_PHRASING = (data) => {
  return request.post(`/member/position/emailPhrasing`, data, false);
};

/**
 * @title   一键匹配岗位
 */
export const POSITION_ONCLICK = (data) => {
  return request.post(`/member/oneClick/position`, data, false);
};

/**
 * @title   一键面试指导
 */
export const INTERVIEW_ONCLICK = (data) => {
  return request.post(`/member/oneClick/interview`, data, false);
};

/**
 * @title   一键offer分析
 */
export const OFFER_ONCLICK = (data) => {
  return request.post(`/member/oneClick/offer`, data, false);
};

/**
 * @title   一键行业分析
 */
export const INDUSTRY_ONCLICK = (data) => {
  return request.post(`/member/oneClick/industry`, data, false);
};

/**
 * @title   一键职业规划
 */
export const CAREER_ONCLICK = (data) => {
  return request.post(`/member/oneClick/career`, data, false);
};

/**
 * @title   一键笔试指导
 */
export const EXAM_ONCLICK = (data) => {
  return request.post(`/member/oneClick/exam`, data, false);
};

/**
 * @title   查询职位收藏分页
 */
export const COLLECT_LIST = (data) => {
  return request.get(`/member/jobPosition/collection/list`, data);
};

/**
 * @title   职位收藏/取消收藏
 */
export const COLLECT_OPT = (data) => {
  return request.post(`/member/jobPosition/collection`, data);
};

/**
 * @title   查询优化成功列表
 */
export const RESULT_LIST = (scene) => {
  return request.get(`/member/result/list?scene=${scene}`, {}, false);
};

/**
 * @title   优化结果详细信息
 */
export const GET_RESULT_DATA = (id) => {
  return request.get(`/member/result/${id}`);
};

/**
 * @title   查询最新结果
 */
export const GET_RESULT_LASTEST = (scene) => {
  return request.get(`/member/result/lastest?scene=${scene}`);
};

/**
 * @title   我的数据统计
 */
export const MY_COUNT = (scene) => {
  return request.get(`/my/count`);
};

/**
 * @title   我的已匹配列表
 */
export const MATCH_LIST = (data) => {
  return request.get(`/member/jobPosition/match/list`, data);
};

/**
 * @title talkId同步
 */
export const CHAT_SYNC = (data) => {
  return request.post(`/member/chat/sync`, data);
};

/**
 * @title   意向企业榜单
 */
export const GET_RESUME_LIST = (data) => {
  return request.get(`/member/match/company`, data);
};

/**
 * @title   企业职位分页
 * @params  companyId
 * @params  pageNum
 * @params  pageSize
 */
export const GET_COMPANY_JOB_LIST = (data) => {
  return request.get(`/member/match/company/jobs`, data);
};
/**
 * @title   获取职位详情
 * @params  id
 */
export const GET_JOB_DETAIL = (id) => {
  return request.get(`/member/jobPosition/${id}`);
};

/**
 * @title   查询邀请码列表
 */
export const GET_INVITE_CODE = () => {
  return request.get(`/member/code/list`);
};

/**
 * @title   使用邀请码
 * @params  code
 */
export const USE_INVITE_CODE = (data = {}) => {
  return request.post(`/member/code/use`, data);
};
/**
 * @title   兑换VIP
 * @params  code
 */
export const USE_VIP_CODE = (data = {}) => {
  return request.post(`/member/code/vip`, data);
};

/**
 * @title   获取等候信息
 */
export const GET_WAIT_INFO = () => {
  return request.get(`/member/code/waitInfo`);
};

/**
 * @title   查询学校
 */
export const GET_SCHOOL_LIST = (keyword = "") => {
  return request.get(`/member/code/school`, { keyword });
};

/**
 * @title   查询专业
 */
export const GET_MAJOR_LIST = (keyword = "") => {
  return request.get(`/member/code/major`, { keyword });
};

/**
 * @title   加入等候名单
 */
export const JOIN_WAIT_LIST = (data = {}) => {
  return request.post(`/member/code/join`, data);
};

/**
 * @title   获取用户报告
 */
export const GET_REPORT = (resultId) => {
  return request.get(`/member/report/${resultId}`);
};

/**
 * @title   申请复核报告
 */
export const REVIEW_REPORT = (resultId) => {
  return request.post(`/member/report/review/${resultId}`);
};

/**
 * @title   面试前检查
 */
export const CHECK_INTERVIEW = (data) => {
  return request.post(`/member/interview/v1/check`, data);
};

/**
 * @title   开始面试
 */
export const START_INTERVIEW = (data) => {
  return request.post(`/member/interview/v1/start`, data);
};
