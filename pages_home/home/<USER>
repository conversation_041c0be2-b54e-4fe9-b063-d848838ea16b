<template>
  <view class="all-net-data">
    <u-navbar leftText="全网数据" @leftClick="backPage" fixed></u-navbar>
    <image
      :src="setImg(`/images/public/topBg1.png`)"
      class="bg-img"
      mode="widthFix"
    ></image>
    <image
      :src="setImg(`/images/public/homePage.png`)"
      class="main-bg"
      mode="widthFix"
    ></image>
    <HomeDef from="allNetData" />
    <view class="flex justify-center mt-5">
      <button class="btn share-btn" open-type="share">
        赶快分享给需要的好友吧
      </button>
    </view>
  </view>
</template>

<script>
import { GET_DATA_TOTAL, GET_DATA_RANK } from "@/api/resume.js";
import HomeDef from "@/pages/components/homeDef/index.vue";
export default {
  components: {
    HomeDef,
  },
  data() {
    return {
      pageData: {},
    };
  },
  computed: {},
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    backPage() {
      uni.navigateBack();
    },
  },
  onLoad(option) {
    this.pageData = option || {};
    console.log(this.pageData.type);
  },
  onShareAppMessage() {},
  onShareTimeline() {},
};
</script>

<style lang="scss" scoped>
.all-net-data {
  position: relative;
  min-height: 100vh;
  padding: 60rpx 32rpx 32rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);

  .bg-img {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
  }

  .main-bg {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    z-index: 0;
  }

  .share-btn {
    position: relative;
    border-color: #fff;
    box-shadow: 0px 2px 8px #0000001a;
    background: linear-gradient(to right, #ffffff99, #ffffffe5);
    z-index: 2;
    color: #387668;
  }
}
</style>
