<template>
  <view class="more-company">
    <u-navbar leftText="更多企业" @leftClick="backPage" fixed></u-navbar>

    <view class="more-content">
      <view class="intention-data">
        <view class="data-item">
          <text>企业总数</text>
          <text>{{ formatNumberWithCommas(matchData.companyCount) }}</text>
          <text>家</text>
        </view>
        <view class="data-item">
          <text>岗位总数</text>
          <text>{{ formatNumberWithCommas(matchData.jobCount) }}</text>
          <text>个</text>
        </view>
        <view class="data-item">
          <text>近7日新增</text>
          <text>{{ formatNumberWithCommas(matchData.jobCount7D) }}</text>
          <text>个岗位</text>
        </view>
        <view class="data-item">
          <text>近30日新增</text>
          <text>{{ formatNumberWithCommas(matchData.jobCount30D) }}</text>
          <text>个岗位</text>
        </view>
      </view>
      <view class="company-list mt-4">
        <view
          v-for="(item, index) in list"
          :key="index"
          class="company-item"
          @click="navigateToDetail(item)"
        >
          <CompanyLogo :item="item"></CompanyLogo>
          <view class="info-col">
            <text class="company-name">{{ item.name }}</text>
            <text class="job-count">相关岗位{{ item.jobCount }}个</text>
          </view>
          <view class="arrow-col">
            <image :src="setImg('images/right_icon.png')" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import global from "@/common/global";
import { formatNumberWithCommas } from "@/utils";
import CompanyLogo from "@/pages/components/homeData/CompanyLogo.vue";
import { GET_MATCH_COUNT, GET_MATCH_COMPANY } from "@/api/resume";
export default {
  components: {
    CompanyLogo,
  },
  data() {
    return {
      pageData: {},
      matchData: {},
      matchCompanyData: {},
    };
  },
  //计算属性
  computed: {
    list() {
      return this.matchCompanyData["last90D"] || [];
    },
  },
  onShow() {
    this.getMatchCount();
    this.getMatchCompany();
  },
  methods: {
    formatNumberWithCommas,
    setImg(url) {
      return global.STATIC_URL + url;
    },
    backPage() {
      uni.navigateBack();
    },
    navigateToDetail(item) {
      uni.navigateTo({
        url: `/pages_home/jobList?companyId=${item.id}&companyName=${item.name}`,
      });
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
    // 意向匹配统计
    async getMatchCount() {
      const result = await GET_MATCH_COUNT();
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.matchData = result.data || {};
      }
    },
    // 意向企业榜单
    async getMatchCompany() {
      const result = await GET_MATCH_COMPANY();
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.matchCompanyData = result.data || {};
      }
    },
  },
  onLoad(option) {
    this.pageData = option || {};
    console.log(this.pageData.type);
  },
};
</script>

<style lang="scss" scoped>
.more-company {
  background: #fff;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);

  .more-content {
    padding: 30rpx 40rpx 30rpx;

    .intention-data {
      display: flex;
      flex-wrap: wrap;
      border-radius: 32rpx;
      background: #f7f8fa;
      box-sizing: border-box;
      padding: 32rpx 32rpx 0;

      .data-item {
        width: 50%;
        margin-bottom: 32rpx;

        text {
          font-size: 24rpx;
          color: #86909c;
          line-height: 1.4;
          &:nth-child(1) {
            display: block;
          }

          &:nth-child(2) {
            font-size: 32rpx;
            font-weight: 500;
            color: #1d2129;
          }

          &:nth-child(3) {
            margin-left: 10rpx;
          }
        }
      }
    }
  }
}
</style>
