<template>
  <view class="all-net-data">
    <u-navbar leftText="全网数据" @leftClick="backPage" fixed></u-navbar>

    <view class="rank-content">
      <image
        class="title-bg"
        :src="setImg(`/images/home/<USER>"
        mode="scaleToFill"
      ></image>
      <view class="title">
        <text class="gradient-text">{{ pageData.title }}</text>
      </view>
      <RankingList :list="list" :type="pageData.type" />
    </view>
  </view>
</template>

<script>
import RankingList from "@/components/rankingList.vue";
export default {
  components: {
    RankingList,
  },
  data() {
    return {
      pageData: {},
      list: [],
    };
  },
  methods: {
    getLogoName(item) {
      if (item.logoUrl) {
        return "";
      }
      let name = item.name.replace(
        /(中国|北京|上海|广州|深圳|厦门|杭州|苏州|南京|天津|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)(省|市)?/g,
        ""
      );
      return name.substr(0, 1);
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    backPage() {
      uni.navigateBack();
    },
  },
  onLoad(option) {
    this.pageData = option || {};
    this.list = this.$store.getters.getRankList || [];
  },
};
</script>

<style lang="scss" scoped>
.gradient-text {
  background-image: linear-gradient(to right, #1d2129 60%, #4b6757);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}
.all-net-data {
  background: #fff;
  min-height: 100vh;

  .rank-content {
    color: #1d2129;
    position: relative;
    padding: 50rpx 32rpx;
    box-sizing: border-box;

    .title-bg {
      width: 100%;
      height: 204rpx;
      left: 0;
      top: 0;
      position: absolute;
    }
  }

  .title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 36rpx;
    position: relative;
    margin-bottom: 48rpx;
  }
}
</style>
