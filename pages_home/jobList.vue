<template>
  <view class="p-4 flex flex-col gap-4">
    <u-navbar :leftText="companyName" @leftClick="backPage" fixed></u-navbar>
    <template v-if="(dataList.length === 0 && loaded) || !companyId">
      <Empty />
    </template>
    <template v-else>
      <JobCard v-for="(item, index) in dataList" :key="index" :item="item" />
      <u-loadmore :status="loading ? 'loading' : loaded ? 'nomore' : ''" />
    </template>
  </view>
</template>

<script>
import JobCard from "@/components/jobCard.vue";
import { GET_COMPANY_JOB_LIST } from "@/api/resume.js";
import Empty from "@/components/empty/index.vue";
export default {
  components: {
    JobCard,
    Empty,
  },
  data() {
    return {
      companyName: "",
      companyId: null,
      dataList: [],
      pageSize: 10,
      currentPage: 1,
      loaded: false,
      loading: false,
    };
  },
  onLoad(options) {
    this.companyName = options.companyName;
    this.companyId = options.companyId;
    if (options.companyId) {
      this.getJobList();
    }
  },
  onPullDownRefresh() {
    this.getJobList(1);
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.loaded || this.loading) {
      return;
    }
    this.getJobList(this.currentPage + 1);
  },
  methods: {
    backPage() {
      uni.navigateBack();
    },
    async getJobList(page = 1) {
      console.log("getJobList");
      if (!this.companyId || this.loading) {
        return;
      }
      try {
        if (page === 1) {
          this.dataList = [];
          this.loaded = false;
        }
        this.loading = true;
        const result = await GET_COMPANY_JOB_LIST({
          companyId: this.companyId,
          pageNum: page,
          pageSize: this.pageSize,
        });
        console.log(result);
        if (this.qUtil.validResp(result) && result.code === 200) {
          this.currentPage = page;
          const list = result.rows || [];
          this.dataList = [...this.dataList, ...list];
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },
  },
};
</script>

<style lang="scss"></style>
