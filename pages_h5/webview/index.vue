<template>
  <view class="web-view-container">
    <web-view
      :src="pdfUrl"
      :webview-styles="webviewStyles"
      @load="load"
      @error="error"
    >
      <top-tips
        v-if="type != 'noPDF' && this.platform !== 'android'"
        btnText="刷新"
        desc="如有模糊或空白，请刷新重试"
        @backFn="backFn"
      />
      <cover-view
        class="footer_temp"
        v-if="type === 'temp' && this.platform !== 'android'"
      >
        <cover-view
          class="button_temp button_temp_default"
          bindtap="nextStep1"
          @click="adjustTempFn"
          >调整模版</cover-view
        >
        <cover-view class="button_temp" bindtap="nextStep1" @click="useTempFn"
          >使用模版</cover-view
        >
      </cover-view>
    </web-view>
  </view>
</template>

<script>
import { USE_RESUME_TEMPLATE } from "@/api/resume.js";
import TopTips from "@/components/coverTopTips.vue";
export default {
  data() {
    return {
      pdfUrl: "", // 替换为实际的PDF文件地址
      webviewStyles: {
        width: "100%",
        height: "500px",
        background: '#fff'
      },
      type: "",
      templateId: "",
      platform: "",
    };
  },
  components: {
    TopTips,
  },
  onLoad(options) {
    this.platform = this.systemInfo.platform;
    if (options?.url) {
      this.init(options);
    }

    // 在页面加载时获取当前的网络状态
    // uni.getNetworkType({
    //   success: (res) => {
    //     console.log(res.networkType);
    //   },
    // });
  },
  onShow() {
    uni.$on("updateWebView", (options) => {
      console.log("updateWebView", options);
      uni.redirectTo({
        url: `/pages_h5/webview/index?url=${options.url}&type=${options.type}&id=${options.id}`,
      });
    });
  },
  methods: {
    init(options) {
      this.type = options?.type || "";
      this.templateId = options?.id || "";
      this.pdfUrl = decodeURIComponent(options.url);
    },
    backFn() {
      uni.redirectTo({
        url: `/pages_h5/webview/index?url=${this.pdfUrl}&type=${this.type}`,
      });
    },
    //调整
    adjustTempFn() {
      uni.navigateTo({
        url: `/pages_user/resume/adjustTemplate?url=${this.pdfUrl}&type=${this.type}&id=${this.templateId}`,
      });
    },
    //使用
    async useTempFn() {
      const result = await USE_RESUME_TEMPLATE({
        templateId: this.templateId * 1,
      });
      if (this.qUtil.validResp(result) && result.code === 200) {
        uni.redirectTo({
          url: `/pages_user/resume/index?url=${this.pdfUrl}&type=${this.type}&id=${this.templateId}&navTo=webView`,
        });
      }
    },
    optFn(type = "") {
      if (type === "download") {
        wx.downloadFile({
          // 示例 url，并非真实存在
          url: this.pdfUrl,
          success: function (res) {
            const filePath = res.tempFilePath;
            wx.openDocument({
              filePath: filePath,
              showMenu: true,
              success: function (res) {
                console.log("打开文档成功");
              },
            });
          },
        });
      } else {
        uni.navigateBack({
          delta: 1,
        });
      }
    },
    error() {
      console.log("加载失败", this.pdfUrl);
    },
    // 监听页面加载的生命周期函数
    load() {
      console.log("加载完成", this.pdfUrl);
    },
  },
};
</script>

<style lang="scss" scoped>
.web-view-container {
  width: 100%;
  height: 100vh;
  background: #fff !important;
}

.pdf-view {
  width: 100%;
  height: 100%;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 50%;
  width: 100%;
  height: 20%;
  z-index: 9;
  transform: translateX(-50%);
}
.footer_temp {
  position: fixed;
  bottom: 10%;
  display: flex;
  width: 100%;
}
.button_temp {
  height: 106rpx;
  line-height: 106rpx;
  text-align: center;
  width: 40%;
  border-radius: 64rpx;
  color: #ffffff;
  margin: 20rpx auto;
  background-color: #18C2A5;
  z-index: 99999;
}
.button_temp_default {
  box-sizing: border-box;
  border: 2rpx solid #18C2A5;
  color: #18C2A5;
  background-color: #ffffff;
}
.button_box {
  height: 106rpx;
  line-height: 106rpx;
  text-align: center;
  width: 406rpx;
  text-align: center;
  border-radius: 64rpx;
  color: #ffffff;
  margin: 20rpx auto;
  background-color: #18C2A5;
  z-index: 9999;
}
.footer_text {
  font-size: 12px;
  color: rgba(153, 153, 153, 1);
  padding-top: 16rpx;
  text-align: center;
  line-height: 36rpx;
}
</style>
