const getIntroduction = (name) => {
  return `我是今天的面试官${name}，\n本次面试的目的是对你的整体能力、特点和潜力做一个综合评估，以便后续为你做出精准岗位推荐。`;
};

const tempList = [
  {
    name: "唐晶",
    sex: "女",
    tags: "适合综合岗位方向",
    voiceType: "zh_female_tianmeixiaoyuan_moon_bigtts",
  },
  {
    name: "苏筱",
    sex: "女",
    tags: "适合央国企方向",
    voiceType: "zh_female_zhixingnvsheng_mars_bigtts",
  },
  {
    name: "<PERSON>",
    sex: "女",
    tags: "适合外企方向",
    voiceType: "zh_female_qingchezizi_moon_bigtts",
  },
  {
    name: "罗槟",
    sex: "男",
    tags: "适合金融精英方向",
    voiceType: "zh_male_yuanboxiaoshu_moon_bigtts",
  },
  {
    name: "陆远",
    sex: "男",
    tags: "适合互联网大厂方向",
    voiceType: "zh_male_wennuanahu_moon_bigtts",
  },
  {
    name: "<PERSON>",
    sex: "男",
    tags: "适合管培生方向",
    voiceType: "zh_male_yangguangqingnian_moon_bigtts",
  },
];

export const interviewers = tempList.map((item, index) => {
  return {
    ...item,
    id: index + 1,
    avatar: `/static/images/interviewer${index + 1}.png`,
    introduction: getIntroduction(item.name),
  };
});
console.log("interviewers", interviewers);
export const currentInterviewer = interviewers[0];
