<template>
  <view class="container">
    <u-navbar leftText="AI面试官" @leftClick="backPage" fixed></u-navbar>

    <!-- 主页面内容 - 始终加载且显示 -->
    <view class="main-content">
      <!-- 用户摄像头视频 -->
      <view
        class="camera-wrapper"
        style="position: relative"
        @touchmove.stop.prevent
      >
        <camera
          v-if="shouldShowCamera"
          class="camera"
          device-position="front"
          flash="off"
          mode="normal"
          resolution="low"
          @error="onCameraError"
          @initdone="onCameraInitDone"
        ></camera>
        <view v-else class="camera-placeholder">
          <text>等待摄像头启动...</text>
        </view>
        <view v-if="!showAuthModal && isInterviewStarted" class="ai-info">
          <image
            class="ai-avatar"
            :src="currentInterviewer.avatar"
            mode="aspectFill"
            @tap="!isInterviewStarted && openInterviewerModal()"
            :style="
              isInterviewStarted ? 'cursor: default;' : 'cursor: pointer;'
            "
          />
          <text style="font-size: 24rpx; color: #333; margin-top: 8rpx">{{
            currentInterviewer.name
          }}</text>
        </view>
      </view>

      <!-- 进度条和问题 - 仅在面试开始后显示 -->
      <view v-if="isInterviewStarted" class="progress-question-box">
        <view class="progress-row">
          <text class="progress-index">{{ progressText }}</text>
          <view class="progress-bar-bg segmented">
            <view
              v-for="n in totalQuestions"
              :key="n"
              class="progress-bar-segment"
              :class="{ active: n + 1 <= currentIndex }"
            ></view>
          </view>
        </view>
        <text class="question-text">{{ currentQuestion }}</text>
      </view>

      <!-- 语音输入按钮和录音状态 - 仅在面试开始后显示 -->
      <view v-if="isInterviewStarted" class="voice-container">
        <view v-if="isInterviewEnded" class="action-btn-box">
          <button class="action-btn" @touchstart="handleEnded">完成</button>
        </view>
        <template v-else>
          <view v-if="!showTimer" class="action-btn-box">
            <button class="action-btn" @touchstart="startRecording">
              开始作答
            </button>
          </view>
          <view v-else class="recording-box">
            <text class="recording-timer">
              {{ timer < 10 ? "00:0" + timer : "00:" + timer }}
            </text>
            <view class="audio-wave">
              <image :src="audioWavImg" mode="aspectFit" />
            </view>
            <view class="record-tip">超过3分钟后系将自动结束作答</view>
            <button class="action-btn recording" @touchend="stopRecording">
              结束作答
            </button>
          </view>
        </template>
      </view>

      <!-- 授权弹窗 -->
      <view v-if="showAuthModal" class="auth-modal">
        <view class="auth-modal-content">
          <view class="auth-modal-tip">
            请确保网络稳定、环境安静、光线充足，以保证最佳面试效果；请授权开启摄像头和麦克风，以便为您提供精准分析与反馈。
          </view>
          <image
            class="auth-modal-feature-img"
            :src="authImg"
            mode="aspectFit"
          />
          <view class="auth-modal-btn-box">
            <button class="auth-modal-btn" @tap="onAuthConfirm">
              我已知晓
            </button>
          </view>
          <view class="auth-modal-desc">音频和视频信息已加密且不会外泄</view>
        </view>
      </view>

      <!-- 面试官介绍底部弹窗 - 仅在未开始面试且已授权时显示 -->
      <view
        v-if="!showAuthModal && !isInterviewStarted"
        class="interviewer-bottom-sheet"
      >
        <view class="interviewer-card">
          <view class="interviewer-header">
            <view class="interviewer-info">
              <image
                class="interviewer-avatar"
                :src="currentInterviewer.avatar"
                mode="aspectFill"
                @tap="openInterviewerModal"
              />
              <text class="interviewer-name">{{
                currentInterviewer.name
              }}</text>
            </view>
            <view class="interviewer-content">
              <view class="interviewer-message">
                {{ currentInterviewer.introduction }}
              </view>
            </view>
          </view>
          <text class="interviewer-tip"
            >小贴士：点击左上方头像可切换其他的面试官。</text
          >
          <button class="start-btn" @tap="handleStartInterview">
            开始面试
          </button>
        </view>
      </view>

      <!-- 面试官选择弹窗 -->
      <view
        v-if="showInterviewerModal"
        class="interviewer-select-modal"
        :class="{ closing: isInterviewerModalClosing }"
      >
        <view class="interviewer-select-content">
          <view class="interviewer-select-header">
            <text class="interviewer-select-title">切换面试官</text>
            <text class="interviewer-select-close" @tap="closeInterviewerModal"
              >关闭</text
            >
          </view>
          <scroll-view class="interviewer-list" scroll-y>
            <view
              v-for="item in interviewers"
              :key="item.id"
              class="interviewer-item"
            >
              <image
                class="interviewer-item-avatar"
                :src="item.avatar"
                mode="aspectFill"
              />
              <view class="interviewer-item-info">
                <view class="interviewer-item-name">{{ item.name }}</view>
                <view class="interviewer-item-tags">
                  <view class="tag-plain org">{{ item.sex }}</view>
                  <view class="tag-plain blue">{{ item.tags }}</view>
                </view>
              </view>
              <button
                :class="[
                  'select-btn',
                  currentInterviewer.id === item.id ? 'selected' : '',
                ]"
                @tap="selectInterviewer(item)"
              >
                {{ currentInterviewer.id === item.id ? "已选择" : "选择" }}
              </button>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- Loading Indicator -->
      <view v-if="isLoading" class="loading-overlay">
        <view class="loading-spinner"></view>
      </view>
    </view>
  </view>
</template>

<script>
// 引入必要的API
// import uniRequest from '@/utils/uniRequest';
import authImg from "@/pages_ai/images/auth.png";
import audioWavImg from "@/pages_ai/images/audio_wav.png";
import { currentInterviewer, interviewers } from "./config.js";
import { wsUrl, apiBaseUrl, wsTtsUrl } from "@/common/global.js";
import { START_INTERVIEW } from "@/api/resume.js";

export default {
  data() {
    return {
      authImg,
      audioWavImg,
      conversationId: "", // Track the conversation ID
      chatId: "",
      totalQuestions: 0, // Total number of questions
      currentIndex: 0, // Current question index
      currentQuestion: "",
      recorderManager: null,
      showTimer: false,
      timer: 0,
      timerId: null,
      audioChunks: [], // Store audio chunks
      isProcessing: false, // Flag to track if we're currently processing audio
      socketTask: null, // WebSocket connection
      // wsUrl: 'ws://*************:8079/ws/audio/asr', // WebSocket server URL
      // wsUrl: 'wss://miniapi.jobtap.com.cn/ws/audio/asr',
      wsUrl: wsUrl,
      audioBuffer: new Uint8Array(0), // 用于累积音频数据
      bufferSize: 10 * 1024, // 10KB 缓冲区大小
      // apiBaseUrl: 'http://*************:8079', // 添加API基础URL
      // apiBaseUrl: 'https://miniapi.jobtap.com.cn',
      apiBaseUrl,
      hasSentEndMessage: false, // 标记是否已发送结束标记
      showAuthModal: true, // 新增：控制授权弹窗显示
      showInterviewerModal: false,
      isInterviewerModalClosing: false, // 新增：控制面试官选择弹窗的关闭动画
      currentInterviewer: { ...currentInterviewer },
      interviewers: [...interviewers],
      currentAudioContext: null, // Track current audio player
      isInterviewStarted: false,
      isInterviewEnded: false,
      currentQuestionIndex: 0,
      isLoading: false, // Loading state indicator
      isCameraEnabled: false,
      shouldShowCamera: false, // 新增：控制摄像头组件的显示
      isAppReady: true, // 修改默认值为 true
      cameraContext: null,
      maxZoom: 1,
      menuButtonInfo: null, // 胶囊按钮信息
      statusBarHeight: 0, // 状态栏高度
      recordStartTime: 0, // 记录开始录音的时间
      isFirstQuestionLoading: false, // 新增
      ttsWebSocket: null, // 用于存储 TTS WebSocket 实例
    };
  },
  computed: {
    // 计算进度条百分比
    progressPercentage() {
      if (this.totalQuestions === 0) return 0;
      return (this.currentIndex / this.totalQuestions) * 100 + "%";
    },
    // 计算进度显示文本
    progressText() {
      return `${this.currentIndex} / ${this.totalQuestions}`;
    },
  },
  onLoad() {
    // 初始化录音管理器
    this.initRecorder();
    this.checkPermissions();
    this.getMenuButtonInfo(); // 获取胶囊按钮信息
  },
  onShow() {
    // 在页面显示时检查并初始化摄像头
    this.checkAndInitCamera();
    // 强制停止正在播放的音频
    this.stopCurrentAudio();
  },
  methods: {
    handleEnded() {
      this.closeWebSocket();
      this.stopCurrentAudio();
      uni.navigateBack();
    },
    backPage() {
      uni.navigateBack();
    },
    // 初始化录音管理器
    initRecorder() {
      try {
        this.recorderManager = uni.getRecorderManager();
        if (this.recorderManager == null) {
          console.log("获取录音管理器失败");
        }

        // 监听录音开始事件
        this.recorderManager.onStart(() => {
          console.log(`[${new Date().toISOString()}] 录音开始`);
          this.audioChunks = []; // Clear previous chunks
          this.connectWebSocket();
        });

        // 监听录音结束事件
        this.recorderManager.onStop((res) => {
          console.log(
            `[${new Date().toISOString()}] 录音结束`,
            res.tempFilePath
          );
          // 如果还没有发送结束标记，则发送结束标记
          if (!this.hasSentEndMessage) {
            console.log("在onStop中发送结束标记");
            this.sendEndMessage();
          }
        });

        // 监听录音帧数据
        this.recorderManager.onFrameRecorded((res) => {
          const { frameBuffer, isLastFrame } = res;
          console.log(
            `[${new Date().toISOString()}] onFrameRecord frameBuffer is null`,
            frameBuffer == null,
            isLastFrame
          );
          if (frameBuffer != null) {
            console.log(
              `[${new Date().toISOString()}] onFrameRecord frameBuffer length:`,
              frameBuffer.byteLength
            );
          }

          // 如果有音频数据，发送音频数据
          if (frameBuffer != null && frameBuffer.byteLength > 0) {
            this.sendAudioChunk(frameBuffer, isLastFrame);
          }
          // 如果是最后一帧，发送结束标记
          if (isLastFrame) {
            console.log(`[${new Date().toISOString()}] 收到最后一帧`);
            this.sendEndMessage();
          }
        });

        // 监听录音错误
        this.recorderManager.onError((error) => {
          console.error("录音错误:", error);
          uni.showToast({
            title: "录音出错",
            icon: "none",
          });
        });

        console.log(`[${new Date().toISOString()}] 录音管理器初始化成功`);
      } catch (error) {
        console.error("录音管理器初始化失败:", error);
        uni.showToast({
          title: "录音初始化失败",
          icon: "none",
        });
      }
    },
    // 摄像头初始化完成事件
    onCameraInitDone(e) {
      console.log(`[${new Date().toISOString()}] 摄像头初始化完成`, e);
      this.isCameraEnabled = true;
      this.cameraContext = uni.createCameraContext();
      this.maxZoom = e.detail && e.detail.maxZoom ? e.detail.maxZoom : 1;
      // 自动设置 zoom 为 1（最小，显示最多内容）
      this.cameraContext.setZoom({
        zoom: 1,
      });
      this.$nextTick(() => {
        console.log(
          `[${new Date().toISOString()}] Camera enabled and should be visible now`
        );
      });
    },
    // 摄像头错误处理
    onCameraError(e) {
      console.error("摄像头错误:", e);
      this.isCameraEnabled = false;
      this.shouldShowCamera = false;

      // 延迟重试
      setTimeout(() => {
        console.log(`[${new Date().toISOString()}] 尝试重新初始化摄像头`);
        this.shouldShowCamera = true;
      }, 1000);
    },
    // 修改 startRecording 方法，记录开始时间
    async startRecording() {
      // 先停止当前正在播放的音频
      this.stopCurrentAudio();

      if (!this.recorderManager) {
        console.error("录音管理器未初始化");
        this.initRecorder();
        return;
      }

      // 重置结束标记标志
      this.hasSentEndMessage = false;

      // 确保关闭之前的WebSocket连接
      this.closeWebSocket();

      try {
        this.recordStartTime = Date.now(); // 记录开始时间
        this.recorderManager.start({
          format: "pcm",
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 32000,
          frameSize: 50,
          duration: 180000,
        });
        this.showTimer = true;
        this.timer = 0;
        this.timerId = setInterval(() => {
          this.timer++;
        }, 1000);
      } catch (error) {
        console.error("录音启动失败:", error);
        uni.showToast({
          title: "录音启动失败，请稍后重试",
          icon: "none",
        });
      }
    },
    // 修改 stopRecording 方法，添加时长判断
    async stopRecording() {
      const minDuration = 1000; // 最小录音时长（毫秒）
      const currentDuration = Date.now() - this.recordStartTime;

      if (currentDuration < minDuration) {
        // 如果录音时间不足1秒，等待剩余时间
        const remainingTime = minDuration - currentDuration;
        await new Promise((resolve) => setTimeout(resolve, remainingTime));
      }

      this.isLoading = true; // 在停止录音时开启loading
      this.recorderManager.stop();
      this.showTimer = false;
      clearInterval(this.timerId);
    },
    // 连接WebSocket
    connectWebSocket() {
      // 确保关闭之前的连接
      this.closeWebSocket();

      try {
        this.socketTask = uni.connectSocket({
          url: this.wsUrl,
          success: () => {
            console.log(`[${new Date().toISOString()}] WebSocket连接成功`);
          },
          fail: (error) => {
            console.error("WebSocket连接失败:", error);
          },
        });

        // 监听WebSocket连接打开
        this.socketTask.onOpen(() => {
          console.log(`[${new Date().toISOString()}] WebSocket连接已打开`);
          // 发送一个初始化消息，告诉服务器我们准备好了
          this.sendInitMessage();
        });

        // 监听WebSocket消息
        this.socketTask.onMessage((res) => {
          try {
            const message = JSON.parse(res.data);
            console.info(
              `[${new Date().toISOString()}] WebSocket response: `,
              message.type
            );
            if (message.type === "response") {
              // 更新进度信息
              if (message.total !== undefined && message.index !== undefined) {
                this.totalQuestions = message.total;
                this.currentIndex = message.index;
                if (this.currentIndex > this.totalQuestions) {
                  this.isInterviewEnded = true;
                }
              }
              // 更新问题文本
              if (message.text) {
                this.currentQuestion = message.text;
              }
              // 新增：判断是否有audio字段
              if (message.audio) {
                this.handleMessageContent(message); // 处理音频
              } else if (message.text) {
                // 新增逻辑：用text请求TTS
                this.playTTSFromText(message.text);
              }
              this.closeWebSocket();
            } else if (message.type === "empty_transcription") {
              // 处理空语音转写的情况
              this.isLoading = false; // 停止loading状态
              this.showTimer = false; // 隐藏计时器
              uni.showToast({
                title: "未能识别到有效语音，请重新回答",
                icon: "none",
                duration: 2000,
              });
              this.closeWebSocket();
            } else if (message.type === "error") {
              console.error("服务器错误:", message.message);
              uni.showToast({
                title: "服务器错误",
                icon: "none",
              });
            }
          } catch (e) {
            console.error("消息解析错误:", e);
          }
        });

        // 监听WebSocket错误
        this.socketTask.onError((error) => {
          console.error("WebSocket错误:", error);
          uni.showToast({
            title: "连接错误",
            icon: "none",
          });
          // 发生错误时关闭连接
          this.closeWebSocket();
        });

        // 监听WebSocket关闭
        this.socketTask.onClose(() => {
          console.log(`[${new Date().toISOString()}] WebSocket连接已关闭`);
          this.socketTask = null;
        });
      } catch (error) {
        console.error("WebSocket连接错误:", error);
        uni.showToast({
          title: "连接失败",
          icon: "none",
        });
      }
    },
    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.socketTask) {
        try {
          this.socketTask.close({
            success: () => {
              console.log("WebSocket连接已关闭");
            },
            fail: (error) => {
              console.error("关闭WebSocket连接失败:", error);
            },
          });
        } catch (error) {
          console.error("关闭WebSocket连接时发生错误:", error);
        }
        this.socketTask = null;
      }
    },
    // 发送初始化消息
    sendInitMessage() {
      if (this.socketTask) {
        console.info(
          `[${new Date().toISOString()}] ready to send Init `,
          this.conversationId,
          this.chatId
        );
        this.socketTask.send({
          data: JSON.stringify({
            type: "init",
            conversationId: this.conversationId,
            chatId: this.chatId,
          }),
          success: () => {
            console.log(`[${new Date().toISOString()}] 初始化消息发送成功`);
          },
          fail: (error) => {
            console.error("初始化消息发送失败:", error);
          },
        });
      }
    },
    // 发送结束标记
    sendEndMessage() {
      if (this.socketTask && !this.hasSentEndMessage) {
        this.hasSentEndMessage = true; // 标记已发送结束标记
        this.socketTask.send({
          data: JSON.stringify({
            type: "end",
            conversationId: this.conversationId,
            chatId: this.chatId,
          }),
          success: () => {
            console.log(`[${new Date().toISOString()}] 发送结束标记成功`);
          },
          fail: (error) => {
            console.error("发送结束标记失败:", error);
            this.hasSentEndMessage = false; // 发送失败时重置标记
          },
        });
      }
    },
    // 发送音频数据块
    sendAudioChunk(chunk, isLastFrame) {
      if (this.socketTask) {
        try {
          // 将新的音频数据添加到缓冲区
          const newBuffer = new Uint8Array(
            this.audioBuffer.length + chunk.byteLength
          );
          newBuffer.set(this.audioBuffer);
          newBuffer.set(new Uint8Array(chunk), this.audioBuffer.length);
          this.audioBuffer = newBuffer;

          console.log(
            `[${new Date().toISOString()}] 缓冲区大小:`,
            this.audioBuffer.length
          );

          // 如果缓冲区达到指定大小或者是最后一帧，则发送数据
          if (this.audioBuffer.length >= this.bufferSize || isLastFrame) {
            // 发送缓冲区中的数据
            this.socketTask.send({
              data: this.audioBuffer.buffer,
              success: () => {
                console.log(`[${new Date().toISOString()}] 音频数据发送成功`, {
                  size: this.audioBuffer.length,
                  isLast: isLastFrame,
                });

                // 清空缓冲区
                this.audioBuffer = new Uint8Array(0);
              },
              fail: (error) => {
                console.error("发送音频数据失败:", error);
              },
            });
          }
        } catch (error) {
          console.error("发送音频数据错误:", error);
        }
      }
    },
    // 处理消息文本和音频
    handleMessageContent(message) {
      // 解码并播放音频
      if (message.audio) {
        // 将base64转换为ArrayBuffer
        const base64Data = message.audio;
        const bytes = uni.base64ToArrayBuffer(base64Data);

        // 创建临时文件路径
        const fs = uni.getFileSystemManager();
        const tempFilePath = `${
          uni.env.USER_DATA_PATH
        }/temp_audio_${Date.now()}.wav`;
        console.log(
          `[${new Date().toISOString()}] 接收到返回的语音流数据`,
          tempFilePath
        );

        // 写入文件
        fs.writeFile({
          filePath: tempFilePath,
          data: bytes,
          success: () => {
            console.log(
              `[${new Date().toISOString()}] 写入语音数据成功`,
              tempFilePath
            );
            // 停止当前正在播放的音频
            this.stopCurrentAudio();

            // 创建新的音频实例
            this.currentAudioContext = uni.createInnerAudioContext({});
            uni.setInnerAudioOption({
              obeyMuteSwitch: false,
            });
            const audioContext = this.currentAudioContext;
            audioContext.autoplay = true;

            // 设置音频源并播放
            audioContext.src = tempFilePath;

            // 监听音频准备就绪事件
            audioContext.onCanplay(() => {
              console.log(`[${new Date().toISOString()}] 音频准备就绪`);
              this.isLoading = false; // 音频准备就绪时关闭loading
            });

            // 监听播放结束事件
            audioContext.onEnded(() => {
              console.log(
                `[${new Date().toISOString()}] 音频播放完成，准备清理资源:`,
                tempFilePath
              );
              // 播放结束后销毁实例
              audioContext.destroy();
              this.currentAudioContext = null;
              console.log(`[${new Date().toISOString()}] 音频实例已销毁`);
              // 删除临时文件
              fs.unlink({
                filePath: tempFilePath,
                success: () => {
                  console.log(
                    `[${new Date().toISOString()}] 临时音频文件已删除:`,
                    tempFilePath
                  );
                },
                fail: (error) => {
                  console.error(
                    "删除临时音频文件失败:",
                    error,
                    "文件路径:",
                    tempFilePath
                  );
                },
              });
            });

            // 监听播放错误
            audioContext.onError((error) => {
              console.error("音频播放错误:", error, "文件路径:", tempFilePath);
              audioContext.destroy();
              this.currentAudioContext = null;
              console.log(
                `[${new Date().toISOString()}] 音频实例已销毁（错误处理）`
              );
              fs.unlink({
                filePath: tempFilePath,
                success: () => {
                  console.log(
                    `[${new Date().toISOString()}] 临时音频文件已删除（错误处理）:`,
                    tempFilePath
                  );
                },
                fail: (error) => {
                  console.error(
                    "删除临时音频文件失败（错误处理）:",
                    error,
                    "文件路径:",
                    tempFilePath
                  );
                },
              });
            });

            // 开始播放
            audioContext.play();
          },
          fail: (error) => {
            console.error("音频文件写入失败:", error);
          },
        });
      }
      this.isLoading = false; // 在处理完消息后关闭loading
    },
    // 检查权限并初始化摄像头
    async checkAndInitCamera() {
      uni.getSetting({
        success: (settingRes) => {
          console.log(
            `[${new Date().toISOString()}] 检查权限状态:`,
            settingRes.authSetting
          );
          // 如果已获得所需权限，初始化摄像头
          if (
            settingRes.authSetting["scope.camera"] &&
            settingRes.authSetting["scope.record"]
          ) {
            console.log(
              `[${new Date().toISOString()}] 权限已获得，准备启动摄像头`
            );
            this.showAuthModal = false;
            this.shouldShowCamera = true; // 直接设置为 true，不需要等待下一个时序

            console.log(
              `[${new Date().toISOString()}] 权限已获得，启动摄像头完成`
            );
          } else {
            console.log(
              `[${new Date().toISOString()}] 未获得完整权限，显示授权弹窗`
            );
            this.shouldShowCamera = false;
            this.showAuthModal = true;
          }
        },
        fail: (error) => {
          console.error("检查权限状态失败:", error);
          this.shouldShowCamera = false;
        },
      });
      // try {
      //   const settingRes = await uni.getSetting();
      //   console.log(
      //     `[${new Date().toISOString()}] 检查权限状态:`,
      //     settingRes.authSetting
      //   );

      //   // 如果已获得所需权限，初始化摄像头
      //   if (
      //     settingRes.authSetting["scope.camera"] &&
      //     settingRes.authSetting["scope.record"]
      //   ) {
      //     console.log(
      //       `[${new Date().toISOString()}] 权限已获得，准备启动摄像头`
      //     );
      //     this.showAuthModal = false;
      //     this.shouldShowCamera = true; // 直接设置为 true，不需要等待下一个时序

      //     console.log(
      //       `[${new Date().toISOString()}] 权限已获得，启动摄像头完成`
      //     );
      //   } else {
      //     console.log(
      //       `[${new Date().toISOString()}] 未获得完整权限，显示授权弹窗`
      //     );
      //     this.shouldShowCamera = false;
      //     this.showAuthModal = true;
      //   }
      // } catch (error) {
      //   console.error("检查权限状态失败:", error);
      //   this.shouldShowCamera = false;
      // }
    },
    // 授权弹窗确认
    async onAuthConfirm() {
      try {
        this.isLoading = true; // 开启 loading
        // 请求摄像头权限
        await uni.authorize({
          scope: "scope.camera",
        });

        // 请求录音权限
        await uni.authorize({
          scope: "scope.record",
        });

        // 如果都授权成功，关闭弹窗并初始化摄像头
        this.showAuthModal = false;
        await this.checkAndInitCamera();
        await this.fetchWelcomeMessage();
      } catch (error) {
        console.error("权限请求失败:", error);

        // 判断是否是用户拒绝了权限
        if (error.errMsg && error.errMsg.includes("deny")) {
          uni.showModal({
            title: "授权提示",
            content:
              "需要您授权摄像头和麦克风权限才能进行面试，是否去设置页面重新授权？",
            success: (res) => {
              if (res.confirm) {
                // 打开设置页面
                uni.openSetting({
                  success: async (settingRes) => {
                    if (
                      settingRes.authSetting["scope.camera"] &&
                      settingRes.authSetting["scope.record"]
                    ) {
                      this.showAuthModal = false;
                      await this.checkAndInitCamera();
                      await this.fetchWelcomeMessage();
                    } else {
                      uni.showToast({
                        title: "请授权相关权限以继续使用",
                        icon: "none",
                      });
                    }
                  },
                });
              } else {
                uni.showToast({
                  title: "需要相关权限才能继续使用",
                  icon: "none",
                });
              }
            },
          });
        } else {
          uni.showToast({
            title: "权限请求失败，请重试",
            icon: "none",
          });
        }
      } finally {
        this.isLoading = false; // 关闭 loading
      }
    },
    // 处理开始面试的点击事件
    async handleStartInterview() {
      if (this.isFirstQuestionLoading) return; // 防止重复点击
      this.isFirstQuestionLoading = true;
      this.isLoading = true;
      this.stopCurrentAudio();
      try {
        await this.sendEmptyAudioForFirstQuestion();
        this.isInterviewStarted = true;
        this.currentQuestionIndex = 1;
      } catch (error) {
        console.error("开始面试失败:", error);
        // uni.showToast({
        //   title: error || "开始面试失败，请重试",
        //   icon: "none",
        //   duration: 2000,
        // });
      } finally {
        this.isLoading = false;
        this.isFirstQuestionLoading = false;
      }
    },
    // 获取第一个问题
    async fetchFirstQuestion() {
      try {
        const response = await uni.request({
          url: `${this.apiBaseUrl}/api/audio/question`,
          method: "GET",
        });

        if (response.statusCode === 200 && response.data) {
          const message = response.data;
          if (message.type === "question") {
            this.handleMessageContent(message);
          } else {
            throw new Error("获取问题失败");
          }
        } else {
          throw new Error("请求失败");
        }
      } catch (error) {
        console.error("获取第一个问题失败:", error);
        throw error;
      }
    },
    // 修改音频播放初始化代码
    async sendEmptyAudioForFirstQuestion() {
      try {
        // 1. 先调用 /text/dify 获取 Dify 答案
        console.log(`[${new Date().toISOString()}] 开始请求Dify答案`);
        const difyRes = await START_INTERVIEW({
          text: "你好",
          conversationId: this.conversationId,
        });
        // uni.request({
        //   url: `${this.apiBaseUrl}/api/member/interview/v1/start`,
        //   method: "GET",
        //   data: {
        //     text: "你好",
        //     conversationId: this.conversationId,
        //   },
        // });

        console.log(`[${new Date().toISOString()}] Dify响应:`, difyRes);
        // 检查响应状态和数据完整性
        if (
          !this.qUtil.validResp(difyRes) ||
          !difyRes.data ||
          !difyRes.data.question
        ) {
          console.error(
            `[${new Date().toISOString()}] Dify响应格式错误:`,
            difyRes
          );
          throw new Error(difyRes.msg);
        }

        const responseData = difyRes.data;

        // 更新进度条和问题文本
        if (
          responseData.total !== undefined &&
          responseData.index !== undefined
        ) {
          this.totalQuestions = responseData.total;
          this.currentIndex = responseData.index;
        }

        // 更新问题文本
        if (responseData.question) {
          this.currentQuestion = responseData.question;
        }

        // 保存新的 conversationId（如果有的话）
        if (responseData.conversationId) {
          this.conversationId = responseData.conversationId;
          console.log(
            `[${new Date().toISOString()}] 更新 conversationId:`,
            this.conversationId
          );
        }
        // 保存新的 chatId
        if (responseData.chatId) {
          this.chatId = responseData.chatId;
          console.log(
            `[${new Date().toISOString()}] 更新 chatId:`,
            this.chatId
          );
        }

        // 2. 使用 WebSocket 方式播放 TTS
        this.stopCurrentAudio();

        // 创建新的音频上下文
        this.currentAudioContext = uni.createInnerAudioContext();
        uni.setInnerAudioOption &&
          uni.setInnerAudioOption({ obeyMuteSwitch: false });

        // 使用 WebSocket 方式播放 TTS
        await this.initTTSWebSocket(responseData.question, () => {
          console.log(`[${new Date().toISOString()}] 首题TTS播放完成`);
        });
      } catch (error) {
        console.error("获取首题音频出错:", error);
        uni.showModal({
          title: "错误提示",
          content: error.message || "获取音频失败，请检查网络连接后重试",
          showCancel: false,
        });
        throw error;
      }
    },
    // 获取欢迎消息
    async fetchWelcomeMessage() {
      this.isLoading = true;
      try {
        this.stopCurrentAudio();

        // 创建音频上下文
        this.currentAudioContext = uni.createInnerAudioContext();
        uni.setInnerAudioOption &&
          uni.setInnerAudioOption({ obeyMuteSwitch: false });

        // 建立WebSocket连接
        await this.initTTSWebSocket(
          this.currentInterviewer.introduction,
          () => {
            this.isLoading = false;
          }
        );
      } catch (error) {
        console.error("获取欢迎消息出错:", error);
        this.isLoading = false;
        uni.showModal({
          title: "错误提示",
          content: error.message || "获取欢迎消息失败",
          showCancel: false,
        });
      }
    },
    // 打开面试官选择弹窗
    openInterviewerModal() {
      this.stopCurrentAudio();
      this.showInterviewerModal = true;
    },
    // 关闭面试官选择弹窗
    closeInterviewerModal() {
      this.isInterviewerModalClosing = true;
      setTimeout(() => {
        this.showInterviewerModal = false;
        this.isInterviewerModalClosing = false;
      }, 300); // 动画持续时间为300ms
    },
    // 选择面试官
    async selectInterviewer(interviewer) {
      this.isLoading = true; // 立即显示loading
      try {
        this.currentInterviewer = interviewer;
        await this.fetchWelcomeMessage();
      } catch (error) {
        console.error("选择面试官失败:", error);
        uni.showToast({
          title: "切换面试官失败，请重试",
          icon: "none",
        });
      } finally {
        this.closeInterviewerModal();
        this.isLoading = false; // 确保在所有操作完成后关闭loading
      }
    },
    // 停止当前正在播放的音频
    stopCurrentAudio() {
      if (this.currentAudioContext) {
        try {
          // 先暂停播放
          this.currentAudioContext.pause();

          // 等待一小段时间再停止和销毁
          setTimeout(() => {
            try {
              this.currentAudioContext.stop();
              this.currentAudioContext.destroy();
            } catch (error) {
              console.error("停止音频时发生错误:", error);
            }
            this.currentAudioContext = null;
          }, 100);
        } catch (error) {
          console.error("暂停音频时发生错误:", error);
          // 如果暂停失败，直接尝试销毁
          try {
            this.currentAudioContext.destroy();
          } catch (e) {
            console.error("销毁音频实例时发生错误:", e);
          }
          this.currentAudioContext = null;
        }
      }
    },
    // 检查权限状态
    checkPermissions() {
      uni.getSetting({
        success: (settingRes) => {
          // 如果没有相关权限，显示授权弹窗
          console.log("settingRes", settingRes);
          if (
            !settingRes.authSetting["scope.camera"] ||
            !settingRes.authSetting["scope.record"]
          ) {
            this.showAuthModal = true;
            this.shouldShowCamera = false;
          } else {
            // 已有权限，直接获取欢迎消息
            this.showAuthModal = false;
            this.shouldShowCamera = true;
            this.fetchWelcomeMessage();
          }
        },
        fail: (error) => {
          console.error("获取权限状态失败:", error);
          this.showAuthModal = true;
          this.shouldShowCamera = false;
        },
      });
      // try {
      //   const settingRes = await uni.getSetting();
      //   console.log('settingRes',settingRes)

      //   // 如果没有相关权限，显示授权弹窗
      //   if (
      //     !settingRes.authSetting["scope.camera"] ||
      //     !settingRes.authSetting["scope.record"]
      //   ) {
      //     this.showAuthModal = true;
      //     this.shouldShowCamera = false;
      //   } else {
      //     // 已有权限，直接获取欢迎消息
      //     this.showAuthModal = false;
      //     this.shouldShowCamera = true;
      //     this.fetchWelcomeMessage();
      //   }
      // } catch (error) {
      //   console.error("获取权限状态失败:", error);
      //   this.showAuthModal = true;
      //   this.shouldShowCamera = false;
      // }
    },
    // 初始化摄像头
    initCamera() {
      this.shouldShowCamera = true;
    },
    // 获取胶囊按钮信息
    getMenuButtonInfo() {
      // #ifdef MP-WEIXIN
      try {
        const systemInfo = uni.getSystemInfoSync();
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.menuButtonInfo = menuButtonInfo;
        this.statusBarHeight = systemInfo.statusBarHeight;
      } catch (error) {
        console.error("获取胶囊按钮信息失败:", error);
      }
      // #endif
    },
    goToTest() {
      uni.navigateTo({
        url: "/pages/test/test",
      });
    },
    async playTTSFromText(text) {
      try {
        await this.initTTSWebSocket(text, () => {
          console.log("TTS播放完成");
        });
      } catch (error) {
        console.error("TTS播放失败:", error);
        uni.showModal({
          title: "错误提示",
          content: error.message || "TTS播放失败",
          showCancel: false,
        });
      }
    },
    // 初始化 TTS WebSocket 连接
    initTTSWebSocket(text, onComplete) {
      this.closeTTSWebSocket(); // 确保关闭之前的连接

      try {
        this.ttsWebSocket = uni.connectSocket({
          url: wsTtsUrl,
          success: () => {
            console.log(`[${new Date().toISOString()}] WebSocket TTS连接成功`);
          },
          fail: (error) => {
            console.error("WebSocket TTS连接失败:", error);
            this.isLoading = false; // 连接失败时关闭loading
            throw new Error("WebSocket连接失败");
          },
        });

        // 创建音频上下文（如果还没有创建）
        if (!this.currentAudioContext) {
          this.currentAudioContext = uni.createInnerAudioContext();
          uni.setInnerAudioOption &&
            uni.setInnerAudioOption({ obeyMuteSwitch: false });
        }

        this.ttsWebSocket.onOpen(() => {
          console.log(`[${new Date().toISOString()}] WebSocket TTS连接已打开`);
          this.ttsWebSocket.send({
            data: JSON.stringify({
              text,
              voiceType: this.currentInterviewer.voiceType,
            }),
          });
        });

        this.ttsWebSocket.onMessage((res) => {
          try {
            if (res.data instanceof ArrayBuffer) {
              console.log(`[${new Date().toISOString()}] 收到音频数据片段`);

              // 创建临时文件路径
              const fs = uni.getFileSystemManager();
              const tempFilePath = `${
                uni.env.USER_DATA_PATH
              }/temp_audio_${Date.now()}.mp3`;

              // 写入文件
              fs.writeFile({
                filePath: tempFilePath,
                data: res.data,
                success: () => {
                  console.log(
                    `[${new Date().toISOString()}] 写入音频数据成功`,
                    tempFilePath
                  );

                  if (this.currentAudioContext) {
                    // 设置音频源并播放
                    this.currentAudioContext.src = tempFilePath;

                    // 监听音频准备就绪事件
                    this.currentAudioContext.onCanplay(() => {
                      console.log(`[${new Date().toISOString()}] 音频准备就绪`);
                      this.isLoading = false; // 音频准备就绪时关闭loading
                    });

                    this.currentAudioContext.play();

                    // 播放完成后删除临时文件
                    this.currentAudioContext.onEnded(() => {
                      console.log(
                        `[${new Date().toISOString()}] 音频片段播放完成`
                      );
                      fs.unlink({
                        filePath: tempFilePath,
                        success: () => {
                          console.log(
                            `[${new Date().toISOString()}] 临时音频文件已删除:`,
                            tempFilePath
                          );
                        },
                        fail: (error) => {
                          console.error("删除临时音频文件失败:", error);
                        },
                      });
                    });

                    // 播放错误时也要删除临时文件
                    this.currentAudioContext.onError((error) => {
                      console.error(
                        `[${new Date().toISOString()}] 音频播放错误:`,
                        error
                      );
                      this.isLoading = false; // 播放错误时关闭loading
                      fs.unlink({
                        filePath: tempFilePath,
                        fail: (error) => {
                          console.error("删除临时音频文件失败:", error);
                        },
                      });
                    });
                  }
                },
                fail: (error) => {
                  console.error("写入音频数据失败:", error);
                  this.isLoading = false; // 写入失败时关闭loading
                },
              });
            } else {
              const message = JSON.parse(res.data);
              console.log(
                `[${new Date().toISOString()}] 收到状态消息:`,
                message
              );

              if (message.status === "completed") {
                console.log(`[${new Date().toISOString()}] TTS转换完成`);
                this.closeTTSWebSocket();
                onComplete && onComplete();
              } else if (message.error) {
                console.error("TTS错误:", message.error);
                this.isLoading = false; // TTS错误时关闭loading
                throw new Error(message.error);
              }
            }
          } catch (error) {
            console.error("处理WebSocket消息时出错:", error);
            this.isLoading = false; // 处理消息错误时关闭loading
          }
        });

        this.ttsWebSocket.onError((error) => {
          console.error("WebSocket TTS错误:", error);
          this.isLoading = false; // WebSocket错误时关闭loading
          uni.showToast({
            title: "TTS服务连接失败",
            icon: "none",
          });
        });

        this.ttsWebSocket.onClose(() => {
          console.log(`[${new Date().toISOString()}] WebSocket TTS连接已关闭`);
          this.ttsWebSocket = null;
        });
      } catch (error) {
        console.error("初始化TTS WebSocket失败:", error);
        this.isLoading = false; // 初始化失败时关闭loading
        throw error;
      }
    },
    // 关闭 TTS WebSocket 连接
    closeTTSWebSocket() {
      if (this.ttsWebSocket) {
        try {
          this.ttsWebSocket.close();
          this.ttsWebSocket = null;
        } catch (error) {
          console.error("关闭TTS WebSocket失败:", error);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.record-tip {
  font-size: 24rpx;
  color: #86909c;
  margin: 20rpx 0;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #ffffff;
}

.header-bar {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  position: relative;
  height: v-bind(
    'menuButtonInfo ? (menuButtonInfo.height + 16) + "px" : "90rpx"'
  );
  margin-top: v-bind('statusBarHeight + "px"');
  box-sizing: content-box;
}
.back-arrow {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  margin-top: v-bind(
    'menuButtonInfo ? ((menuButtonInfo.top - statusBarHeight) / 2) + "px" : "0"'
  );
}
.back-arrow svg {
  display: block;
  width: 100%;
  height: 100%;
}
.header-title {
  flex: 1;
  text-align: left;
  font-size: 36rpx;
  font-weight: 400;
  color: #222;
  margin-left: 0;
  margin-top: v-bind(
    'menuButtonInfo ? ((menuButtonInfo.top - statusBarHeight) / 2) + "px" : "0"'
  );
}

.ai-info {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  z-index: 10;
  background: #fff;
  padding: 8rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: opacity 0.3s ease;
}

.ai-avatar {
  width: 180rpx;
  height: 180rpx;
  border-radius: 36rpx;
  transition: transform 0.3s ease;
}

/* 只在未开始面试时显示hover效果 */
.ai-info:not(.ai-info-disabled) .ai-avatar:active {
  transform: scale(0.95);
}

.ai-label {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.camera-wrapper {
  width: 100%;
  height: 540rpx;
  background: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.camera {
  width: 100%;
  height: 100%;
  display: block;
}

.progress-question-box {
  margin: 40rpx 0 0 0;
  padding: 0 40rpx;
  opacity: 1;
  transition: opacity 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}
.progress-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  width: 100%;
}
.progress-index {
  font-size: 26rpx;
  color: #888;
  margin-right: 18rpx;
  white-space: nowrap;
}
.progress-bar-bg.segmented {
  display: flex;
  align-items: center;
  height: 12px;
  background: transparent;
  padding: 0;
  flex: 1;
  width: 100%;
}
.progress-bar-segment {
  flex: 1;
  height: 4px;
  margin-right: 8px;
  background: #ececec;
  border-radius: 8px;
  transition: background 0.3s;
}
.progress-bar-segment:last-child {
  margin-right: 0;
}
.progress-bar-segment.active {
  background: #18c2a5;
}
.question-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  text-align: left;
  margin-top: 10rpx;
}

.voice-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* 从flex-end改为center */
  margin-bottom: 60rpx;
  opacity: 1;
  transition: opacity 0.3s ease;
}
.action-btn-box {
  width: 100%;
  display: flex;
  justify-content: center;
}
.action-btn {
  width: 476rpx;
  height: 84rpx;
  background: #18c2a5;
  color: #fff;
  font-size: 30rpx;
  border-radius: 84rpx;
  font-weight: normal;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.action-btn.recording {
  background: #fff;
  color: #222;
  border: 2rpx solid #bfc2c7;
}
.action-btn:active {
  opacity: 0.9;
}
.recording-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}
.recording-timer {
  font-size: 32rpx;
  color: #222;
  margin-bottom: 10rpx;
  margin-top: 10rpx;
}
.mic-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 18rpx;
}
.mic-icon image {
  width: 100%;
  height: 100%;
}
.audio-wave {
  width: 400rpx;
  height: 120rpx;
  margin-bottom: 18rpx;
}
.audio-wave image {
  width: 100%;
  height: 100%;
}

.auth-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.65);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.auth-modal-content {
  width: 90vw;
  max-width: 375px;
  background: #fff;
  border-radius: 16px;
  position: relative;
  z-index: 10000;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  padding: 32rpx 20rpx 24rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.auth-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #18c2a5;
  margin-bottom: 18rpx;
}
.auth-modal-tip {
  font-size: 28rpx;
  color: #222;
  text-align: center;
  margin: 24rpx 24rpx;
}
.auth-modal-img {
  width: 305rpx;
  height: 75rpx;
  margin-bottom: 32rpx;
}
.auth-modal-feature-img {
  width: 100vw;
  height: 140rpx;
  margin: 48rpx 0;
}
.auth-modal-btn-box {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 18rpx;
}
.auth-modal-btn {
  width: 70vw;
  height: 80rpx;
  background: #18c2a5;
  color: #fff;
  font-size: 28rpx;
  border-radius: 50rpx;
  border: none;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}
.auth-modal-desc {
  font-size: 24rpx;
  color: #86909c;
  text-align: center;
  margin-top: 8rpx;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

.interviewer-bottom-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60vh;
  z-index: 100;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.interviewer-card {
  margin: 0;
  height: 100%;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.interviewer-header {
  display: flex;
  width: 100%;
  flex: 0.7; /* 减小头部占比 */
  margin-bottom: 40rpx; /* 增加底部间距 */
}

.interviewer-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  margin-right: 24rpx;
  padding-top: 32rpx;
}

.interviewer-avatar {
  width: 112rpx;
  height: 112rpx;
  border-radius: 86.4rpx;
  border: 2rpx solid #18c2a5;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
}

.interviewer-name {
  font-size: 24rpx;
  color: #86909c;
}

.interviewer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 32rpx;
}

.interviewer-message {
  font-size: 32rpx;
  color: #1d2129;
  line-height: 1.4;
  text-align: left;
  margin-bottom: 20rpx;
  white-space: pre-line;
}

.interviewer-tip {
  font-size: 24rpx;
  color: #86909c;
  text-align: center;
  margin-bottom: 24rpx; /* 增加与按钮的间距 */
}

.start-btn {
  width: 476rpx;
  height: 84rpx;
  background: #18c2a5;
  color: #fff;
  font-size: 30rpx;
  border-radius: 84rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  margin: 0 auto;
  margin-bottom: 0rpx; /* 增加底部间距 */
}

.start-btn:active {
  opacity: 0.9;
}

.interviewer-select-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 60vh;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-out;
  border-radius: 32rpx 32rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
}

@keyframes fadeIn {
  from {
    background: #ffffff;
  }
  to {
    background: #ffffff;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

@keyframes fadeOut {
  from {
    background: #ffffff;
  }
  to {
    background: #ffffff;
  }
}

.interviewer-select-modal.closing {
  animation: fadeOut 0.3s ease-out;
}

.interviewer-select-modal.closing .interviewer-select-content {
  animation: slideDown 0.3s ease-out;
}

.interviewer-select-content {
  background: #fff;
  max-height: 65vh;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  animation: slideUp 0.3s ease-out;
  transform-origin: bottom;
}

.interviewer-select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.interviewer-select-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #1d2129;
}

.interviewer-select-close {
  font-size: 30rpx;
  color: #86909c;
}

.interviewer-list {
  padding: 32rpx;
  max-height: 55vh;
  width: 100%;
  box-sizing: border-box;
}

.interviewer-item {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
  padding-right: 32rpx;
}

.interviewer-item-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 74rpx;
  margin-right: 20rpx;
}

.interviewer-item-info {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
}

.interviewer-item-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 8px;
}

.interviewer-item-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  .tag-plain {
    padding: 4rpx 8rpx;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #fff;
  border-top-color: #18c2a5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.select-btn {
  width: 90px;
  height: auto;
  padding: 4px 16px;
  font-size: 14px;
  border-radius: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #18c2a5;
  color: #fff;
  border: 1px solid #18c2a5;
  line-height: 1.4;
  outline: none;
  box-shadow: none;
}

.select-btn.selected {
  background: #e3fffb;
  color: #18c2a5;
  border: 1px solid #e3fffb;
  outline: none;
  box-shadow: none;
}

.select-btn::after {
  border: none;
  outline: none;
}

.test-btn {
  padding: 4rpx 20rpx;
  font-size: 24rpx;
  background: #18c2a5;
  color: #fff;
  border-radius: 30rpx;
  border: none;
  margin-right: 20rpx;
  height: 48rpx;
  line-height: 48rpx;
  margin-top: v-bind(
    'menuButtonInfo ? ((menuButtonInfo.top - statusBarHeight) / 2) + "px" : "0"'
  );
}

.test-btn::after {
  border: none;
}
</style>
