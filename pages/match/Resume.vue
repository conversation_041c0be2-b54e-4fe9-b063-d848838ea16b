<template>
  <view class="job-intention">
    <view class="job-intention-title">
      <view class="title-intention">简历上传</view>
      <view class="btn-change" v-if="isNow == 1" @click="toUpload">
        <image
          class="btn-change-img"
          :src="setImg(`/images/home/<USER>"
        ></image>
        <text>更新简历</text>
      </view>
    </view>
    <view class="intention-tags" v-if="isNow == 1">
      <view class="intention-tag">{{ resumeName }}</view>
    </view>
    <view class="tag-box" v-else>
      <view class="intention-tags">
        <view class="intention-tag"> 提供 PDF，自动解析硬技能 / 实习经历 </view>
      </view>
      <view class="fbtn0 mt-4" @click="toUpload">去上传</view>
    </view>
    <resume-popup
      :show="resumeShow"
      @close="resumeShow = false"
      @getDataOrUrl="getDataOrUrl"
    />
  </view>
</template>

<script>
import { checkLogin } from "@/utils/auth";
import ResumePopup from "@/components/resumePopup.vue";
import { mapGetters } from "vuex";
export default {
  props: {
    isNow: {
      type: Boolean,
      default: false,
    },
    resumeName: {
      type: String,
      default: "",
    },
  },
  components: {
    ResumePopup,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      resumeShow: false,
    };
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    toUpload() {
      if (!checkLogin(true)) {
        return;
      }
      if (this.userInfo.isVip != 1) {
        this.$emit("showInvitePopup");
        return;
      }
      this.resumeShow = true;
    },
    // 上传文件后回调函数
    getDataOrUrl(data) {
      if (data) {
        this.resumeShow = false;
        this.$emit("change", data);
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
