<template>
  <view :class="['p-4']">
    <u-navbar leftText="文献&算法" @leftClick="backPage" fixed></u-navbar>
    <view class="white-card rule-list">
      <view class="rule-item" v-for="(item, index) in list" :key="index">
        {{ item }}
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      list: [
        "Handel (2016) The O*NET Content Model: Strengths and Limitations",
        "Schmidt & Oh (2016) Validity & Utility of Selection Methods",
        "<PERSON> Meuse, Dai & <PERSON>nbeck (2024) Past, Present, and Future of Learning Agility",
        "<PERSON><PERSON> et al. (2012) Vocational Interests and Performance",
        "Barrick & Mount (1991) Big 5 & Job Performance",
        "<PERSON><PERSON><PERSON><PERSON> et al. (2005) Meta-Analysis of Person–Fit",
        "<PERSON> et al. (2023) Early-Career Turnover Model & Career Path for Self-realization",
        "ConFit: Improving Resume-Job Matching using Data Augmentation and Contrastive Learning",
        "Graph-based adaptive feature fusion neural network model for person-job fit",
        "Person-Job Fit: Adapting the Right Talent for the Right Job with Joint Representation Learning",
      ],
    };
  },
  methods: {
    backPage() {
      uni.navigateBack();
    },
  },
};
</script>
<style lang="scss" scoped>
.rule-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  .rule-item {
    padding: 12rpx 0;
    border-bottom: 1px solid #e5e6eb;
    font-size: 24rpx;
    color: #0c0c0d;
  }
}
</style>
