<template>
  <view class="fast-match">
    <image
      :src="setImg(`/images/public/topBg1.png`)"
      class="bgimg"
      mode="scaleToFill"
    ></image>

    <!-- 匹配中状态显示内容 -->
    <MatchStatus
      :matchInfo="{
        status: matchStatus,
        time: matchResult.startTime,
        id: matchId,
      }"
      :scene="scene"
    />

    <view class="content-container">
      <view class="title">请先填写以下内容</view>
      <view class="job-contention">
        <view
          class="job-container-item"
          v-for="(item, index) in stepData"
          v-bind:key="index"
        >
          <view class="s_r">
            <view
              class="line"
              :style="{
                backgroundColor: index == 0 ? 'rgba(0,0,0,0)' : '#E5E6EB',
              }"
            ></view>
            <view
              class="index"
              :style="{
                backgroundColor: item.isNow == 0 ? '#C9CDD4' : '#00B42A',
                color: '#fff',
              }"
            >
              <text class="iconfont icon-check"></text>
            </view>
            <view
              class="line"
              :style="{
                backgroundColor:
                  index != stepData.length - 1 ? '#E5E6EB' : 'rgba(0,0,0,0)',
              }"
            ></view>
          </view>
          <!-- 求职意向 -->
          <view class="flex-1" v-if="index == 0">
            <JobIntention
              :isNow="item.isNow"
              :jobWant="jobWant"
              @showInvitePopup="handleShowInvitePopup"
              v-if="index == 0"
            />
          </view>
          <!-- 求职简历 -->
          <view class="flex-1" v-if="index == 1">
            <Resume
              :isNow="item.isNow"
              :resumeName="resumeName"
              @showInvitePopup="handleShowInvitePopup"
              @change="handleResumeChange"
            />
          </view>
        </view>
      </view>

      <!-- 一键快速匹配按钮 -->
      <view
        :class="{ 'match-btn': true, disabled: fastMatchDisabled }"
        @click="startMatch"
      >
        <text>一键快速推荐</text>
        <text>
          本周剩余次数：{{ remainingTimes }}/{{
            remainingTimes > 3 ? remainingTimes : 3
          }}
        </text>
      </view>
    </view>

    <!-- 匹配维度表格 -->
    <MatchDimension />

    <view class="match-doc mt-4">
      <view class="match-doc-title">匹配逻辑</view>
      <text>
        {{
          `DeepSeek 解析简历与求职意向，将学历、专业、技能与经历等关键词经 Qwen3 向量比对招聘要求，快速筛出硬条件吻合、可直接投递的岗位，并同步展示完整思维链。`
        }}
      </text>
      <view class="flex flex-col gap-1">
        <navigator
          class="match-doc-link"
          url="/pages/match/rule"
          hover-class="none"
        >
          查看参考文献 & 算法 >>
        </navigator>
        <!-- <text class="match-doc-link">SHRM 校招模型：硬条件初筛的成功率</text>
        <text class="match-doc-link">LinkedIn《硬技能匹配报告 2024》</text> -->
      </view>
    </view>
  </view>
</template>

<script>
import { checkLogin } from "@/utils/auth";
import { START_MATCH, GET_RESULT_LASTEST } from "@/api/resume.js";
import JobIntention from "./JobIntention.vue";
import Resume from "./Resume.vue";
import MatchStatus from "./MatchStatus.vue";
import MatchDimension from "./MatchDimension.vue";
import { mapGetters } from "vuex";

export default {
  props: {
    resumeData: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    Resume,
    JobIntention,
    MatchStatus,
    MatchDimension,
  },
  data() {
    return {
      scene: "QuickMatch",
      matchStatus: 1,
      matchId: null,
      remainingTimes: 0,
      resumeName: "",
      resumeStatus: 1, // 新增：简历状态，2已上传，1未上传
      interviewStatus: 1, // 新增：面试状态 1未面试2面试中3已面试4面试异常
      evaluateStatus: 1, // 新增：评估状态 1未测评2测评中3已测评4测评异常
      hasJobWant: false,
      jobWant: {},
      matchResult: {},
      matchingItems: [
        {
          dimension: "求职意向",
          quickDisabled: false,
          preciseDisabled: false,
        },
        {
          dimension: "学历层级",
          quickDisabled: false,
          preciseDisabled: false,
        },
        {
          dimension: "专业对口度",
          quickDisabled: false,
          preciseDisabled: false,
        },
        {
          dimension: "核心硬技能",
          quickDisabled: false,
          preciseDisabled: false,
        },
        {
          dimension: "实习 / 项目经历",
          quickDisabled: false,
          preciseDisabled: false,
        },
        {
          dimension: "证书 / 竞赛奖励",
          quickDisabled: false,
          preciseDisabled: false,
        },
        {
          dimension: "沟通表达",
          quickDisabled: true,
          preciseDisabled: false,
        },
        {
          dimension: "团队协作",
          quickDisabled: true,
          preciseDisabled: false,
        },
        {
          dimension: "抗压 & 学习敏捷度",
          quickDisabled: true,
          preciseDisabled: false,
        },
        {
          dimension: "性格兴趣类型",
          quickDisabled: true,
          preciseDisabled: false,
        },
        {
          dimension: "职业价值观",
          quickDisabled: true,
          preciseDisabled: false,
        },
        {
          dimension: "企业文化契合度",
          quickDisabled: true,
          preciseDisabled: false,
        },
        {
          dimension: "岗位发展匹配",
          quickDisabled: true,
          preciseDisabled: false,
        },
      ],
      stepData: [
        {
          title: "求职意向",
          isNow: 0,
        },
        {
          title: "简历上传",
          isNow: 0,
        },
      ],
    };
  },
  watch: {
    resumeData: {
      handler() {
        // console.log("resumeData update", this.resumeData);
        this.initJobWant();
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
    fastMatchDisabled() {
      return !(this.hasJobWant && this.resumeStatus > 1);
    },
  },
  created() {
    if (checkLogin(false)) {
      this.getMatchResult();
    }
    uni.$on("updateResume", () => {
      this.getMatchResult();
    });
  },
  destroyed() {
    uni.$off("updateResume");
  },
  methods: {
    handleShowInvitePopup() {
      console.log(111);
      this.$emit("showInvitePopup");
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    handleResumeChange(data) {
      if (data) {
        this.resumeName = data.fileName;
        uni.$emit("updateResume");
        this.getMatchResult();
      }
    },
    async startMatch() {
      if (this.fastMatchDisabled) {
        uni.showToast({
          title: "请先完成求职意向和简历上传",
          icon: "none",
        });
        return;
      }
      if (this.remainingTimes <= 0) {
        uni.showToast({
          title: "本周匹配次数已用完",
          icon: "none",
        });
        return;
      }
      if (!checkLogin(true)) {
        return;
      }
      if (this.userInfo.isVip != 1) {
        this.$emit("showInvitePopup");
        return;
      }
      this.matchStatus = 2;
      START_MATCH({ scene: this.scene, test: true }).then((res) => {
        if (this.qUtil.validResp(res)) {
          this.matchId = res.data.id;
          this.toResult();
        }
      });
    },
    toResult() {
      this.navTo(
        `/pages_user/job/matchting?matchId=${this.matchId}&scene=${this.scene}`
      );
    },
    async getMatchResult() {
      let res = await GET_RESULT_LASTEST(this.scene);
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.matchResult = res?.data || {};
        this.matchStatus = this.matchResult.status;
        this.matchId = this.matchResult.id;
        this.remainingTimes = this.matchResult.remainingTimes || 0;
      }
    },
    initJobWant() {
      this.jobWant = this.resumeData.jobWant || {};
      this.hasJobWant = !!this.jobWant.id;
      this.stepData[0].isNow = this.hasJobWant ? 1 : 0;
      this.resumeStatus = this.resumeData.resumeStatus || 1;
      this.stepData[1].isNow = this.resumeStatus > 1 ? 1 : 0;
      this.resumeName = this.resumeData.upload?.name || "";
      this.interviewStatus = this.resumeData.interviewStatus || 1;
      this.evaluateStatus = this.resumeData.evaluateStatus || 1;
    },
    navTo(url, check) {
      if (check) {
        checkLogin(true);
      }
      this.navigateUtil.goto(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.fast-match {
  position: relative;
  padding: 30rpx;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  .bgimg {
    position: absolute;
    width: 100%;
    height: 620rpx;
    left: 0;
    top: 0;
    z-index: -1;
  }
}
</style>
