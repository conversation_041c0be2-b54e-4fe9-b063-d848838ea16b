<template>
  <view class="job-intention">
    <view class="job-intention-title">
      <view class="title-intention">求职意向</view>
      <view
        class="btn-change"
        v-if="isNow == 1"
        @click="editResume(jobWant.id)"
      >
        <image
          class="btn-change-img"
          :src="setImg(`/images/home/<USER>"
        ></image>
        <text>更新意向</text>
      </view>
    </view>
    <view class="intention-tags" v-if="isNow == 1">
      <view class="intention-tag" v-if="jobWant.industryLabel">{{
        jobWant.industryLabel
      }}</view>
      <view class="intention-tag" v-if="jobWant.city">{{
        jobWant.cityLabel || jobWant.city
      }}</view>
      <view class="intention-tag" v-if="jobWant.jobTypeLabel">{{
        jobWant.jobTypeLabel
      }}</view>
      <!-- <view class="intention-tag" v-if="jobWant.startSalary">{{
        jobWant.salary ||
        (jobWant.startSalary == "面议"
          ? "面议"
          : `${jobWant.startSalary}k-${jobWant.endSalary}k`)
      }}</view> -->
      <view class="intention-tag">
        {{ jobWant.natureLabel || "不限" }}
      </view>
    </view>
    <view class="tag-box" v-else>
      <view class="intention-tags">
        <view class="intention-tag">目标行业</view>
        <view class="intention-tag">意向城市</view>
        <view class="intention-tag">意向岗位</view>
        <view class="intention-tag">期望薪资</view>
      </view>
      <view class="fbtn0 mt-4" @click="editResume"> 去填写 </view>
    </view>
  </view>
</template>

<script>
import { checkLogin } from "@/utils/auth";
import { mapGetters } from "vuex";
export default {
  props: {
    isNow: {
      type: Boolean,
      default: false,
    },
    jobWant: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {};
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    editResume(id) {
      if (!checkLogin(true)) {
        return;
      }
      if (this.userInfo.isVip != 1) {
        this.$emit("showInvitePopup");
        return;
      }
      let url = `/pages_user/resume/expectation`;
      if (id) {
        url = `/pages_user/resume/expectation?id=${id}`;
      }
      this.navigateUtil.goto(url);
    },
  },
};
</script>

<style lang="scss" scoped></style>
