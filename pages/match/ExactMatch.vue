<template>
  <view class="exact-match">
    <image
      :src="setImg(`/images/public/topBg2.png`)"
      class="bgimg"
      mode="scaleToFill"
    ></image>
    <match-status
      :scene="scene"
      :matchInfo="{
        status: matchStatus,
        time: matchResult.startTime,
        id: matchId,
      }"
    />

    <view class="content-container">
      <view class="title">请先填写以下内容</view>
      <view class="job-contention">
        <view class="job-container-item" v-for="(item, index) in stepData">
          <view class="s_r">
            <view
              class="line"
              :style="{
                backgroundColor: index == 0 ? 'rgba(0,0,0,0)' : '#E5E6EB',
              }"
            ></view>
            <view
              class="index"
              :style="{
                backgroundColor: item.isNow == 0 ? '#C9CDD4' : '#00B42A',
                color: '#fff',
              }"
            >
              <text class="iconfont icon-check"></text>
            </view>
            <view
              class="line"
              :style="{
                backgroundColor:
                  index != stepData.length - 1 ? '#E5E6EB' : 'rgba(0,0,0,0)',
              }"
            ></view>
          </view>
          <!-- 求职意向 -->
          <view class="flex-1" v-if="index == 0">
            <JobIntention
              :isNow="item.isNow"
              :jobWant="jobWant"
              @showInvitePopup="$emit('showInvitePopup')"
              v-if="index == 0"
          /></view>
          <!-- 求职简历 -->
          <view class="flex-1" v-if="index == 1">
            <Resume
              v-if="index == 1"
              :isNow="item.isNow"
              :resumeName="resumeName"
              @showInvitePopup="$emit('showInvitePopup')"
              @change="handleResumeChange"
          /></view>
          <!-- 职业测评 -->
          <view class="job-intention" v-if="index == 2">
            <view class="job-intention-title custom">
              <view class="title-intention custom">职业测评</view>
              <view class="btn-change">
                <text>约10分钟</text>
              </view>
            </view>
            <view class="tag-box">
              <view class="intention-tags">
                <view class="intention-tag">发现你的职场潜能</view>
              </view>
              <view class="fbtn0 mt-4" @click="startEvaluate">开始测评</view>
            </view>
          </view>
          <!-- AI面试 -->
          <view class="job-intention" v-if="index == 3">
            <view class="job-intention-title custom">
              <view class="title-intention">AI面试</view>
              <view class="btn-change">
                <text>约20分钟</text>
              </view>
            </view>
            <view class="tag-box">
              <view class="intention-tags">
                <view class="intention-tag">让你的求职匹配更立体</view>
              </view>
              <view class="fbtn0 mt-4" @click="startInterview">开始AI面试</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 一键快速匹配按钮 -->
      <view
        :class="{ 'match-btn': true, disabled: exactMatchDisabled }"
        @click="startMatch"
      >
        <text>一键精准推荐</text>
        <text
          >本周次数：{{ remainingTimes }}/{{
            remainingTimes > 3 ? remainingTimes : 3
          }}</text
        >
      </view>
    </view>

    <!-- 匹配维度表格 -->
    <MatchDimension />

    <!-- 匹配逻辑 -->
    <view class="match-doc mt-4">
      <view class="match-doc-title">匹配逻辑</view>
      <text>
        {{
          `在快速筛选基础上，增加 职业测评 和 AI面试 进一步抽取「兴趣 × 性格 × 成长驱动力 × 学习敏捷 × 问题解决方式」等潜在特征，利用 Qwen3 向量框架计算多维契合度，再由 DeepSeek 逐个分析每个岗位，全程思维链透明可视。`
        }}
      </text>
      <view class="flex flex-col gap-1">
        <navigator
          class="match-doc-link"
          url="/pages/match/rule"
          hover-class="none"
        >
          查看参考文献 & 算法 >>
        </navigator>
        <!-- <text class="match-doc-link">
          大五人格 (Big Five) / 霍兰德职业兴趣模型
        </text>
        <text class="match-doc-link">HBR：文化契合对员工留存的影响 (2024)</text> -->
      </view>
    </view>
    <EvaluationPopup :show.sync="evaluationPopupShow" />
    <InterviewPopup :show.sync="interviewPopupShow" />
  </view>
</template>

<script>
import { checkLogin } from "@/utils/auth";
import { START_MATCH, GET_RESULT_LASTEST } from "@/api/resume.js";
import JobIntention from "./JobIntention.vue";
import Resume from "./Resume.vue";
import EvaluationPopup from "../components/evaluationPopup.vue";
import InterviewPopup from "../components/interviewPopup.vue";
import MatchStatus from "./MatchStatus.vue";
import MatchDimension from "./MatchDimension.vue";
import { mapGetters } from "vuex";

export default {
  props: {
    resumeData: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    JobIntention,
    Resume,
    EvaluationPopup,
    InterviewPopup,
    MatchStatus,
    MatchDimension,
  },
  watch: {
    resumeData: {
      handler(newVal) {
        // console.log("resumeData exact update", this.resumeData);
        this.initJobWant();
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
    exactMatchDisabled() {
      return !(
        this.hasJobWant &&
        this.resumeStatus > 1 &&
        this.interviewStatus == 3 &&
        this.evaluateStatus == 3
      );
    },
  },
  data() {
    return {
      scene: "ExactMatch",
      matchStatus: 1,
      matchId: null,
      remainingTimes: 0,
      resumeName: "",
      resumeStatus: 1, // 新增：简历状态，2已上传，1未上传
      interviewStatus: 1, // 新增：面试状态 1未面试2面试中3已面试4面试异常
      evaluateStatus: 1, // 新增：评估状态 1未测评2测评中3已测评4测评异常
      hasJobWant: false,
      jobWant: {},
      matchResult: {},

      stepData: [
        {
          title: "求职意向",
          isNow: 0,
        },
        {
          title: "简历上传",
          isNow: 0,
        },
        {
          title: "职业测评",
          isNow: 1,
        },
        {
          title: "AI面试",
          isNow: 1,
        },
      ],
      evaluationPopupShow: false,
      interviewPopupShow: false,
    };
  },
  created() {
    if (checkLogin(false)) {
      this.getMatchResult();
    }
    uni.$on("updateResume", () => {
      this.getMatchResult();
    });
  },
  methods: {
    startEvaluate() {
      if (!checkLogin(true)) {
        return;
      }
      if (this.userInfo.isVip != 1) {
        this.$emit("showInvitePopup");
        return;
      }
      this.evaluationPopupShow = true;
    },
    startInterview() {
      if (!checkLogin(true)) {
        return;
      }
      if (this.userInfo.isVip != 1) {
        this.$emit("showInvitePopup");
        return;
      }
      this.interviewPopupShow = true;
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    async getMatchResult() {
      let res = await GET_RESULT_LASTEST(this.scene);
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.matchResult = res?.data || {};
        this.matchStatus = this.matchResult.status;
        this.matchId = this.matchResult.id;
        this.remainingTimes = this.matchResult.remainingTimes || 0;
      }
    },
    initJobWant() {
      // console.log("initJobWant", this.resumeData);
      this.jobWant = this.resumeData.jobWant || {};
      this.hasJobWant = !!this.jobWant.id;
      this.stepData[0].isNow = this.hasJobWant ? 1 : 0;
      this.resumeStatus = this.resumeData.resumeStatus || 1;
      this.stepData[1].isNow = this.resumeStatus > 1 ? 1 : 0;
      this.resumeName = this.resumeData.upload?.name || "";
      this.interviewStatus = this.resumeData.interviewStatus || 1;
      this.evaluateStatus = this.resumeData.evaluateStatus || 1;
      this.stepData[2].isNow = this.evaluateStatus > 1 ? 1 : 0;
      this.stepData[3].isNow = this.interviewStatus > 1 ? 1 : 0;
    },
    async startMatch() {
      if (this.exactMatchDisabled) {
        uni.showToast({
          title: "请先完成求职意向、简历上传、职业测评和AI面试",
          icon: "none",
        });
        return;
      }
      if (this.remainingTimes <= 0) {
        uni.showToast({
          title: "本周推荐次数已用完，升级为VIP可立即解锁无限次精准推荐！",
          icon: "none",
        });
        return;
      }
      if (!checkLogin(true)) {
        return;
      }
      if (this.userInfo.isVip != 1) {
        this.$emit("showInvitePopup");
        return;
      }
      this.matchStatus = 2;
      START_MATCH({ scene: this.scene, test: true }).then((res) => {
        if (this.qUtil.validResp(res)) {
          this.matchId = res.data.id;
          this.toResult();
        }
      });
    },
    toResult() {
      this.navTo(
        `/pages_user/job/matchting?matchId=${this.matchId}&scene=${this.scene}`
      );
    },
    navTo(url, check) {
      if (check) {
        checkLogin(true);
      }
      this.navigateUtil.goto(url);
    },
  },
};
</script>

<style lang="scss">
.exact-match {
  position: relative;
  padding: 30rpx;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  .bgimg {
    position: absolute;
    width: 100%;
    height: 620rpx;
    top: 0;
    left: 0;
    z-index: -1;
  }
}
</style>
