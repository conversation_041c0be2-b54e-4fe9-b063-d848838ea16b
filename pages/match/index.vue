<template>
  <view class="page-match">
    <u-navbar :autoBack="false" leftText="一箭职达" fixed></u-navbar>
    <view class="tab-wrapper">
      <u-tabs
        :list="tabList"
        :activeStyle="{
          color: '#18C2A5',
          fontSize: '32rpx',
          fontWeight: 500,
        }"
        :inactiveStyle="{
          color: '#1D2129',
          fontSize: '32rpx',
          fontWeight: 400,
        }"
        lineColor="#18C2A5"
        :current="current"
        :scrollable="false"
        @change="tabChange"
      ></u-tabs>
    </view>
    <view class="match-main">
      <!-- <template v-if="resumeData"> -->
      <swiper
        class="main-swiper"
        :indicator-dots="false"
        :autoplay="false"
        :duration="300"
        :current="current"
        @change="mainSwiperChange"
      >
        <swiper-item>
          <FastMatch
            :resumeData="resumeData"
            ref="fastMatchRef"
            @showInvitePopup="invitePopupShow = true"
          />
        </swiper-item>
        <swiper-item>
          <ExactMatch
            ref="exactMatchRef"
            :resumeData="resumeData"
            @showInvitePopup="invitePopupShow = true"
          />
        </swiper-item>
      </swiper>
    </view>
    <h-tabbar :tabbarValue.sync="tabbarValue" :fixed="true"></h-tabbar>
    <InvitPopup
      :show.sync="invitePopupShow"
      @success="handleInviteSuccess"
    ></InvitPopup>
    <Modal />
  </view>
</template>

<script>
import hTabbar from "@/components/hTabbar.vue";
import FastMatch from "./FastMatch.vue";
import ExactMatch from "./ExactMatch.vue";
import { GET_RESUME_DATA } from "@/api/resume.js";
import InvitPopup from "../components/invitPopup.vue";

export default {
  components: {
    hTabbar,
    FastMatch,
    ExactMatch,
    InvitPopup,
  },
  data() {
    return {
      options: {},
      userInfo: {},
      current: 0,
      tabbarValue: 1,
      tabList: [
        {
          name: "快速推荐",
        },
        {
          name: "精准推荐",
        },
      ],
      show: false,
      windowInfo: {},
      resumeData: {},
      invitePopupShow: false,
    };
  },
  onLoad(options) {
    this.options = options;
    if (options.scene === "ExactMatch") {
      this.current = 1;
    }
    this.windowInfo = uni.getWindowInfo();
    uni.$on("updateResume", () => {
      this.getResumeData();
    });
  },
  onShow() {
    this.getResumeData();
  },
  onUnload() {
    uni.$off("updateResume");
  },
  onPullDownRefresh() {
    this.getResumeData();
  },
  methods: {
    handleInviteSuccess() {
      this.getResumeData();
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    mainSwiperChange(e) {
      console.log("mainSwiperChange", e.detail.current);
      this.current = e.detail.current;
    },
    tabChange(item) {
      this.current = item.index;
    },
    home() {
      uni.navigateTo({
        url: "/pages/index/index",
      });
    },

    async getResumeData() {
      try {
        uni.showLoading();
        let res = await GET_RESUME_DATA();
        if (this.qUtil.validResp(res) && res.code === 200) {
          this.resumeData = res?.data || {};
          // this.$refs.fastMatchRef.initJobWant();
          // this.$refs.exactMatchRef.initJobWant();
        }
      } catch (error) {
      } finally {
        uni.hideLoading();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-match {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.tab-wrapper {
  background: #fff;
}
.match-main {
  flex: 1;
  overflow: hidden;
  .main-swiper {
    height: 100%;
  }
}

::v-deep .u-navbar__content__left {
  image {
    display: none;
  }
}
::v-deep .content-container {
  padding: 32rpx;
  background: #fff;
  border-radius: 32rpx;
  color: #1d2129;
  .title {
    font-size: 36rpx;
    font-weight: 500;
    margin-bottom: 20rpx;
  }

  .job-contention {
    display: flex;
    flex-direction: column;
    .job-container-item {
      display: flex;
      .s_r {
        padding-right: 20rpx;
        display: flex;
        flex-direction: column;
        align-items: center;

        .line {
          flex: 1;
          width: 2rpx;
          background-color: #e5e6eb;
          &:first-child {
            flex: 0.3;
          }
        }

        .index {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36rpx;
          height: 36rpx;
          border-radius: 50%;
          .iconfont {
            font-size: 16rpx;
          }
        }
      }
    }
    // 求职意向样式
    .job-intention {
      width: 100%;
      min-height: 164rpx;
      padding: 32rpx;
      border: 2rpx solid #e5e6eb;
      border-radius: 32rpx;
      margin-bottom: 30rpx;
      color: #1d2129;
      box-sizing: border-box;
      .job-intention-title {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .title-intention {
          font-size: 28rpx;
          font-weight: 500;
        }
        .btn-change {
          font-size: 24rpx;
          color: #86909c;
          display: flex;
          align-items: center;
          .btn-change-img {
            width: 28rpx;
            height: 28rpx;
            margin-right: 5rpx;
          }
        }
      }
    }
  }

  .fbtn0 {
    width: 148rpx;
    height: 64rpx;
    line-height: 64rpx;
    border-radius: 32rpx;
    background: #e3fffb;
    font-size: 28rpx;
    color: #18c2a5;
    padding: 0 20rpx;
    text-align: center;
  }

  .intention-tags {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20rpx;
    gap: 16rpx;
    .intention-tag {
      font-size: 24rpx;
      line-height: 1.4;
      text-align: center;
      background: #f2f3f5;
      border-radius: 8rpx;
      padding: 3rpx 8rpx;
      color: #4e5969;
    }
  }
}

::v-deep .match-doc {
  padding: 32rpx;
  background: #fff;
  border-radius: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  font-size: 24rpx;
  color: #86909c;
  word-break: break-all;
  .match-doc-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #1d2129;
  }
  .match-doc-link {
    font-size: 24rpx;
    color: #18c2a5;
  }
}

::v-deep .evaluationPopup {
  height: auto;
  padding: 0 32rpx 32rpx 32rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  position: relative;
  background: linear-gradient(180deg, #ffd4c7 -3.51%, #fff 98.79%);
  color: #1d2129;
  line-height: 1.4;
  .close-btn {
    position: absolute;
    right: 32rpx;
    top: 32rpx;
    width: 40rpx;
    height: 40rpx;
    z-index: 3;
  }
  .top_img {
    width: 100%;
    height: 256rpx;
    position: absolute;
    top: -88rpx;
    left: 0;
    z-index: 1;
  }

  .pop_top {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    padding: 48rpx 0 32rpx 200rpx;
    z-index: 2;
    .title {
      width: 192rpx;
    }
    .desc {
      color: #d47e65;
      font-size: 32rpx;
      z-index: 2;
    }
  }

  .pop_box {
    height: auto;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    margin-top: 20rpx;
    .tips-item {
      font-size: 28rpx;
      color: #1d2129;
      display: flex;
      align-items: flex-start;
      .doc {
        width: 12rpx;
        height: 12rpx;
        background: #9e5540;
        border-radius: 50%;
        margin-right: 10rpx;
        margin-top: 10rpx;
      }
      .text {
        flex: 1;
      }
    }
  }

  .qa_box {
    margin-top: 32rpx;
    font-size: 24rpx;
    .qa_title {
      color: #1d2129;
      margin-bottom: 8rpx;
    }
    .qa_item {
      color: #86909c;
    }
  }
  .more {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f7f8fa;
    height: 50rpx;
    border-radius: 30rpx;
    padding: 0 32rpx;
    color: #1d2129;
    font-size: 24rpx;
    .m_r {
      display: flex;
      align-items: center;
      color: #86909c;
      .right_icon {
        width: 32rpx;
        height: 32rpx;
        margin-left: 6rpx;
      }
    }
  }
  &.interviewPopup {
    background: linear-gradient(180deg, #c7f3ff -3.51%, #fff 98.79%);
    .top_img {
      top: -96rpx;
    }
    .pop_top {
      .title {
        width: 144rpx;
      }
      .desc {
        color: #6591d4;
      }
    }
  }
}
</style>
