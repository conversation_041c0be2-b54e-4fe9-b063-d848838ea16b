<template>
  <view
    class="container home"
    :style="{
      backgroundImage: hasJobWant
        ? 'none'
        : `url(${setImg(`/images/public/homePage.png`)})`,
      backgroundColor: hasJobWant ? '#fff' : 'transparent',
    }"
  >
    <image
      :src="setImg(`/images/public/topBg1.png`)"
      class="bg-img"
      mode="scaleToFill"
    ></image>
    <view class="home-top">
      <view
        :class="['topbar', { active: isTopActive }]"
        :style="{ paddingTop: statusBarHeight + 'px' }"
      >
        <view class="navbar">
          <view class="navbar_title">
            {{ hasJobWant ? title[0] : title[1] }}
          </view>
          <view class="navbar_tip">
            {{ hasJobWant ? defaultHint[0] : defaultHint[1] }}
          </view>
          <view
            class="navbar_btn"
            v-if="!hasJobWant"
            @click="navTo(`/pages_user/resume/expectation`, true)"
          >
            设置意向，精准查看
          </view>
        </view>
        <image
          :src="setImg(`/images/home/<USER>"
          mode="widthFix"
          class="boxIcon"
        />
      </view>
    </view>
    <HomeData v-if="hasJobWant" :jobWantId="jobWantInfo.id" />
    <HomeDef v-else />
    <h-tabbar :tabbarValue.sync="tabbarValue"></h-tabbar>
    <InvitPopup
      :show.sync="invitePopupShow"
      @success="handleInviteSuccess"
    ></InvitPopup>
    <Modal />
  </view>
</template>

<script>
import hTabbar from "@/components/hTabbar.vue";
import HomeDef from "../components/homeDef/index.vue";
import HomeData from "../components/homeData/index.vue";
import { checkLogin } from "@/utils/auth";
import { GET_RESUME_DATA } from "@/api/resume.js";
import InvitPopup from "../components/invitPopup.vue";
import chatScene from "@/model/enums/ChatScene";
import global from "@/common/global";
import { mapGetters } from "vuex";
export default {
  components: {
    hTabbar,
    HomeDef,
    HomeData,
    InvitPopup,
  },
  data() {
    return {
      statusBarHeight: 0,
      dzlist: chatScene
        .filter((m) => !m.hidden && m.label != "一键优化")
        .map((m) => m.label),
      dztxt: "",
      tabbarValue: 0,
      isTopActive: false,
      isEmpty: false,
      loading: true,
      // userInfo: {},
      // 更多服务 用于存放用户存在服务,但是不需要此集合中的服务
      moreService: [],
      title: ["意向职位数据", "全网职位数据"],
      defaultHint: [
        "基于你的求职意向和全网职位数据，为你汇总出以下精选岗位～",
        "还没告诉我们你的求职意向，无法获得更精准的推荐哦~",
      ],
      rankList: [],
      jobWantInfo: null,
      hasJobWant: false,
      totalData: null,
      invitePopupShow: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    showSkeleton() {
      return this.loading
        ? {
            padding: "0rpx 60rpx 70rpx;",
          }
        : "";
    },
    existService() {
      return this.userInfo.matchingService?.length > 0;
    },
    isLogin() {
      return checkLogin(false);
    },
  },
  onShow() {
    // console.log("userInfo", this.userInfo);
    if (this.isLogin) {
      this.getResumeData();
    }
  },
  async created() {
    this.statusBarHeight = uni.getSystemInfoSync()["statusBarHeight"];
  },
  onPageScroll(res) {
    if (res.scrollTop > 50) {
      this.isTopActive = true;
    } else {
      this.isTopActive = false;
    }
  },
  onShareAppMessage() {},
  onShareTimeline() {},
  methods: {
    handleInviteSuccess() {
      this.getResumeData();
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    // async loadUserInfo() {
    //   if (this.isLogin) {
    //     const hintListDefault = this.storageUtil.getItem("hintListDefault");
    //     if (hintListDefault.length > 0) {
    //       this.hintList = hintListDefault;
    //     } else {
    //       this.getNoTokenGuideData();
    //     }
    //     this.loading = false;
    //     this.$nextTick(() => {
    //       this.$refs.refSwiper.getElementInfo();
    //     });
    //   }

    //   // this.getGuideData();
    //   if (checkLogin(false)) {
    //     uni.showLoading({
    //       title: "加载中",
    //     });
    //     let userInfo = await this.$store.dispatch("refreshInfo");
    //     if (userInfo.matchingService?.length > 0) {
    //       userInfo.matchingService.map((m) => {
    //         m.sceneKey = chatScene.find((s) =>
    //           s.label.includes(m.service)
    //         )?.key;
    //       });
    //       this.moreService = chatScene.filter(
    //         (s) =>
    //           !s.hidden &&
    //           !userInfo.matchingService.some((m) => m.sceneKey === s.key)
    //       );
    //     }

    //     this.userInfo = userInfo;
    //     uni.hideLoading();
    //   }
    // },
    // 获取简历信息
    async getResumeData() {
      let res = await GET_RESUME_DATA();
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.jobWantInfo = res?.data?.jobWant || {};
        this.hasJobWant = !!this.jobWantInfo.id;
      }
    },
    navTo(url, check) {
      if (check) {
        if (!checkLogin(true)) {
          return;
        } else if (this.userInfo.isVip != 1) {
          this.invitePopupShow = true;
          return;
        }
      }
      this.navigateUtil.goto(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  padding: 0 32rpx;
  background-size: 100% 400rpx;
  background-position: left bottom;
  background-repeat: no-repeat;
}
.bg-img {
  position: absolute;
  width: 100%;
  height: 620rpx;
  top: 0;
  left: 0;
  z-index: 0;
}
.home-top {
  margin-bottom: 20rpx;
  .topbar {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 296rpx;
    width: 100%;
    transition: all 1s ease;

    .navbar {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      font-size: 48rpx;
      color: #333;
      text-align: left;
      font-weight: bold;
      transition: all 1s ease;

      .navbar_tip {
        margin-top: 20rpx;
        width: 400rpx;
        font-size: 24rpx;
        color: #4e5969;
        font-weight: normal;
      }

      .navbar_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20rpx;
        height: 64rpx;
        box-sizing: border-box;
        background: linear-gradient(
          113.26deg,
          rgba(255, 255, 255, 0.6) 1.11%,
          rgba(255, 255, 255, 0.9) 70.57%
        );
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
        border-radius: 40rpx;
        padding: 0 32rpx;
        color: #387668;
        font-size: 28rpx;
        font-weight: normal;
        text-align: center;
      }
    }

    .boxIcon {
      position: absolute;
      right: 0;
      bottom: -54rpx;
      width: 283rpx;
    }

    // &.active {
    //   background: rgba($color: #18C2A5, $alpha: 1);

    //   .navbar {
    //     color: #fff;
    //   }
    // }
  }
}

// keyframes 可根据展示的文本长度，自行添加，我的格式为： @keyframes  ‘type’+'文本长度'
@keyframes typing8 {
  from {
    width: 0;
  }

  50% {
    width: 7.5ch;
  }

  100% {
    width: 0;
  }
}

@keyframes caret {
  50% {
    border-color: transparent;
  }
}

.dzbox {
  display: inline-block;
  // width: 0;
  // animation: typing8 8s steps(8) infinite, caret 1s steps(1) infinite;
  animation: caret 1s steps(1) infinite;
  white-space: nowrap;
  overflow: hidden;
  border-right: 0.05em solid;
  vertical-align: top;
}

.home_main {
  height: auto;
  overflow: hidden;
  z-index: 1;
}

.footspace {
  height: 60rpx;
  overflow: hidden;
}
</style>
