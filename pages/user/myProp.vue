<template>
  <view class="container myProp">
    <view class="prop_top">
      <view class="tl">
        <view class="lt">拥有元宝：</view>
        <view class="lc"
          ><image :src="setImg(`images/coin.png`)" mode="widthFix"></image
          ><text>0</text></view
        >
      </view>
      <!-- <view class="tr">
        <text>兑换记录</text>
        <image :src="setImg(`images/right_icon.png`)" />
      </view> -->
    </view>
    <view class="prop_tab">
      <u-tabs
        :list="tabList"
        lineColor="#18C2A5"
        lineHeight="5"
        :scrollable="false"
        :current="tabCurrent"
        @click="tabClick"
      ></u-tabs>
    </view>
    <view class="prop_list" v-if="tabCurrent == 0">
      <view class="item" v-for="(item, index) in list" :key="index">
        <view class="tl">
          <image :src="setImg('images/avatar.png')" mode="aspectFill"></image>
        </view>
        <view class="tc">
          <view class="ct">{{ item.title }}</view>
          <view class="cc">
            <image :src="setImg('images/coin.png')" mode="widthFix"></image>
            <text>{{ item.amount }}</text>
          </view>
        </view>
        <view class="tr">
          <button @click="exchange">立即兑换</button>
        </view>
      </view>
      <Empty v-if="list.length <= 0" text="暂无道具" />
    </view>
    <view class="prop_list" v-else>
      <view class="item" v-for="(item, index) in list1" :key="index">
        <view class="tl">
          <image :src="setImg(item.cover)" mode="aspectFill"></image>
        </view>
        <view class="tc">
          <view class="ct">{{ item.title }}</view>
          <view class="cc">
            <text>x {{ item.num }}</text>
          </view>
        </view>
        <view class="tr"> </view>
      </view>
      <Empty v-if="list1.length <= 0" text="暂无道具" />
    </view>
    <!--<u-loadmore :status="status" />-->
    <view class="footspace"></view>
    <buy-vip
      ref="buyVip"
      :vipShow="vipShow"
      @closePop="vipShow = false"
    ></buy-vip>
    <u-popup
      :show="JobMatchShow"
      :round="20"
      bgColor="#ECEDF6"
      :closeable="true"
      @close="JobMatchShow = false"
    >
      <view class="jmPopup">
        <view class="pop_top">
          <image :src="setImg('images/icon_mate.png')" mode="widthFix"></image>
          <view class="ptit">职位匹配</view>
        </view>
        <image
          :src="setImg('images/icon_mate.png')"
          class="bgimg"
          mode="widthFix"
        ></image>
        <view class="pop_box">
          <view class="btit">什么是职位匹配：</view>
          <view class="bcon">
            职位匹配可以根据求职者的基础能力、当前薪资水平、个人期望等数据与招聘者的情况进行匹配，从而推荐更符合求职者要求的岗位
          </view>
          <view class="bimg">
            <image :src="setImg('images/mate.png')" mode="widthFix"></image>
          </view>
        </view>
        <view class="pop_bottom">
          <view class="bt">立即兑换</view>
          <view class="bc"
            ><image :src="setImg('images/coin.png')" mode="widthFix"></image
            ><text>300</text></view
          >
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import Empty from "@/components/empty/index.vue";
import { checkLogin } from "@/utils/auth";
import uniRequest from "../../utils/request";
import qUtil from "../../utils/queryUtil.js";
import list from "../../uni_modules/uview-ui/libs/config/props/list";
import buyVip from "@/components/buyVip.vue";
export default {
  components: { buyVip, Empty },
  data() {
    return {
      status: "loadmore",
      teamPopShow: false,
      tabCurrent: 0,
      vipShow: false,
      JobMatchShow: false,
      tabList: [
        {
          name: "商城",
        },
        {
          name: "我的",
        },
      ],
      list: [
        {
          id: 0,
          cover: "images/avatar.png",
          title: "职位匹配1次",
          amount: 200,
        },
        {
          id: 1,
          cover: "images/avatar.png",
          title: "VIP 7天体验卡",
          amount: 200,
        },
      ],
      list1: [],
      isEmpty: false,
    };
  },
  onShow() {
    // if(checkLogin(false)){
    // 	uni.redirectTo({
    // 		url:'/pages/public/login'
    // 	})
    // }
    // this.loadData();
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    exchange() {
      // this.JobMatchShow = true;
      uni.$u.toast("兑换服务升级中，敬请期待！");
      // uni.$u.toast("元宝不足");
    },
    tabClick(e) {
      console.log(e);
      this.tabCurrent = e.index;
    },
    apply(id) {
      uniRequest.post("team/apply/add", {
        teamId: id,
      });
      console.log("id", id);
    },
    showTeam(id) {
      this.teamPopShow = true;
      // this.teamList = this.list[id].teamList
    },
    publish() {
      uni.navigateTo({
        url: "/pages/publish/index",
      });
    },
    user() {
      uni.navigateTo({
        url: "/pages/user/index",
      });
    },
    async loadData() {
      uni.showLoading({
        title: "加载中",
      });
      // const indexResp = await this.uniRequest.get('/getIndex');
      await uniRequest.get("/app/getIndex").then((Response) => {
        console.log("get index page info");
        if (qUtil.validResp(Response) && Response.data) {
          console.log(Response);
          this.bonusList = Response.data.bonusList || [];
          this.showSummaryEx = Response.data.showSummaryEx;
          this.showSecretaryEx = Response.data.showSecretaryEx;
          this.showPlus = Response.data.showPlus;
          this.showMinus = Response.data.showMinus;
          this.showComment = Response.data.showComment;
        } else {
        }
      });
      this.isEmpty = this.bonusList.length == 0 ? true : false;
      uni.hideLoading();
      //数据查询完毕关闭下拉刷新
      uni.stopPullDownRefresh();
    },
  },
};
</script>

<style lang="scss">
page {
}
.myProp {
}
.prop_top {
  height: auto;
  overflow: hidden;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  .tl {
    font-size: 24rpx;
    color: #333;
    .lt {
      margin-bottom: 10rpx;
    }
    .lc {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      image {
        width: 40rpx;
        margin-right: 10rpx;
      }
      text {
        font-size: 30rpx;
        font-weight: bold;
      }
    }
  }
  .tr {
    font-size: 24rpx;
    color: #999;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    text {
      margin-right: 10rpx;
    }
    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}
.prop_tab {
  background: #fff;
  padding: 10rpx 30rpx;
}
.prop_list {
  height: auto;
  overflow: hidden;
  padding: 0rpx 30rpx 0rpx 30rpx;
  position: relative;
  z-index: 1;
  .item {
    height: auto;
    overflow: hidden;
    background: rgba($color: #fff, $alpha: 1);
    margin: 30rpx 0;
    border-radius: 16rpx;
    // box-shadow: 5rpx 5rpx 10rpx rgba($color: #000, $alpha: 0.2);
    padding: 25rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tl {
      width: 100rpx;
      height: 100rpx;
      overflow: hidden;
      border-radius: 8rpx;
      margin-right: 25rpx;
      image {
        width: 100%;
        height: 100%;
        display: block;
      }
    }
    .tc {
      flex: 1;
      color: #333;
      .ct {
        font-size: 28rpx;
        margin-bottom: 20rpx;
      }
      .cc {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        image {
          width: 40rpx;
          margin-right: 10rpx;
        }
        text {
          font-size: 26rpx;
        }
      }
    }
    .tr {
      button {
        height: 65rpx;
        line-height: 65rpx;
        background: #18C2A5;
        font-size: 28rpx;
        color: #fff;
        border-radius: 8rpx;
        padding: 0 30rpx;
      }
    }
  }
}
.jmPopup {
  height: auto;
  // overflow:hidden;
  // background:#ECEDF6;
  padding: 0 30rpx 30rpx 30rpx;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  position: relative;
  .bgimg {
    width: 350rpx;
    position: absolute;
    right: -50px;
    top: -80rpx;
    opacity: 0.1;
  }
  .pop_top {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    image {
      width: 160rpx;
      margin: -30rpx 20rpx 0 0;
    }
    .ptit {
      font-size: 40rpx;
      background: -webkit-linear-gradient(top, #919cff, #6674f1);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: 30rpx;
    }
  }
  .pop_box {
    height: auto;
    overflow: hidden;
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin: 0rpx 0 40rpx 0;
    position: relative;
    .btit {
      border-left: 10rpx #ffd055 solid;
      font-size: 30rpx;
      color: #333;
      padding-left: 20rpx;
    }
    .bcon {
      margin: 20rpx 0;
      font-size: 28rpx;
      line-height: 1.8;
      color: #333;
    }

    .bimg {
      image {
        width: 100%;
        display: block;
      }
    }
  }
  .pop_bottom {
    height: 80rpx;
    overflow: hidden;
    text-align: center;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #18C2A5;
    color: #fff;
    .bt {
      font-size: 30rpx;
    }
    .bc {
      display: flex;
      justify-content: center;
      align-items: center;
      image {
        width: 25rpx;
        margin-right: 10rpx;
      }
      text {
        font-size: 20rpx;
      }
    }
  }
}
</style>
