<template>
  <view class="container user">
    <view class="user_cell">
      <view class="cl" @click="logout" style="color: red">退出登录</view>
    </view>
    <view class="footspace"></view>
  </view>
</template>

<script>
import { checkLogin } from "@/utils/auth";

export default {
  components: {},
  data() {
    return {};
  },
  onShow() {
    if (checkLogin(true)) {
    }
  },
  methods: {
    logout() {
      uni.showModal({
        title: "温馨提示",
        content: "退出登录不会删除聊天记录,是否继续?",
        showCancel: true,
        confirmText: "退出登录",
        success: (res) => {
          if (res.confirm) {
            this.$store.dispatch("logout");
            this.$store.dispatch("closeSocket");
            this.navigateUtil.goto("/pages/index/index");
          }
        },
      });
    },
  },
};
</script>

<style lang="scss">
page {
  height: 100%;
  background: #f5f6f7;
  backdrop-filter: blur(0px);
}
.user {
  ::v-deep .u-cell-group {
    height: auto;
    overflow: hidden;
    background: #fff;
    margin: 50rpx 30rpx;
    position: relative;
    z-index: 1;
    border-radius: 20rpx;
    .u-cell__title-text {
      line-height: 40px;
    }
  }
  .user_cell {
    margin: 25rpx 25rpx;
    height: auto;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25rpx 30rpx;
    background: #fff;
    border-radius: 10rpx;
    .cl {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
      flex: 1;
    }
  }
}
</style>
