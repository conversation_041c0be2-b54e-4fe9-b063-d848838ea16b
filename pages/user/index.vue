<template>
  <view class="container page-user">
    <image
      :src="setImg(`/images/myUser/userBg.png`)"
      class="bg"
      mode="widthFix"
    />
    <view
      :class="['topbar', { active: isTopActive }]"
      :style="{ paddingTop: statusBarHeight + 'px' }"
    >
    </view>
    <view class="user_top" :style="{ paddingTop: statusBarHeight + 60 + 'px' }">
      <view class="top_info" v-if="isLogin">
        <u-avatar
          :src="userInfo.avatar"
          :defaultUrl="setImg('images/myUser/defaultUser.png')"
          size="112rpx"
          customStyle="border: 4rpx solid #fff;"
        ></u-avatar>
        <view class="ltit">
          <view class="name ellipsis">
            {{ userInfo.realName || userInfo.nickName || "一箭职达" }}
          </view>
          <template v-if="userInfo.isVip == 0">
            <view :class="['info', 'waiting']" v-if="userInfo.levelId == 10">
              等候名单中...
            </view>
            <!-- <view v-else>暂未开通VIP</view> -->
            <!-- <view :class="['info', 'no_vip']" v-else>
              <image
                :src="setImg(`/images/myUser/vip.png`)"
                mode="widthFix"
              ></image>
              <view>暂未开通VIP</view>
            </view> -->
          </template>
          <template v-else>
            <view :class="['info', 'inner-test']" v-if="userInfo.levelId == 10">
              内测用户
            </view>
          </template>
          <!-- <view :class="['info', !vipShow ? 'no_vip' : '']">
            <image
              :src="setImg(`/images/myUser/vip.png`)"
              mode="widthFix"
            ></image>
            <view v-if="vipShow">
              {{ userInfo.point || "2025-05-97" }}到期
              <text>续费＞</text>
            </view>
            <view v-else>暂未开通VIP</view>
          </view> -->
        </view>
      </view>
      <view class="top_login" v-else>
        <view class="defaultBox">
          <view class="defaultBox_img">
            <image
              :src="setImg(`/images/myUser/defaultUser.png`)"
              mode="widthFix"
            ></image>
          </view>
          <view class="defaultBox_text">
            <text>请先登录帐号</text>
          </view>
        </view>
      </view>
      <view
        class="user_login_bin"
        v-if="!isLogin"
        @click="navTo('/pages/public/oauth')"
      >
        微信授权
      </view>
      <view
        class="user_toVip_bin flex items-center gap-21 justify-center"
        v-else-if="userInfo.isVip == 1 && userInfo.levelId == 10"
        @click="vipFn"
      >
        VIP开通
        <text class="iconfont icon-tri" style="font-size: 14rpx"></text>
      </view>
    </view>
    <view class="user-card">
      <view class="user-card__title">精选服务</view>
      <view class="user-card__content">
        <view class="user_resumes">
          <view
            class="item"
            @click="navTo('/pages_user/resume/attachmentResume', true)"
          >
            <image :src="setImg(`/images/myUser/myMake.png`)" />
            <view class="txt">我的简历</view>
          </view>
          <view
            class="item"
            @click="
              navTo(
                `/pages_user/resume/expectation?id=${jobWant.id || ''}`,
                true
              )
            "
          >
            <image :src="setImg(`/images/myUser/intension.png`)" />
            <view class="txt">求职意向</view>
          </view>
          <view class="item" @click="navTo(`/pages_user/job/matched`, true)">
            <image :src="setImg(`/images/myUser/myMatch.png`)" />
            <view class="txt">我的匹配</view>
          </view>
        </view>
        <view class="user_resumes">
          <view
            class="item"
            @click="navTo(`/pages_user/resume/collectPage`, true)"
          >
            <image :src="setImg(`/images/myUser/favorites.png`)" />
            <view class="txt">我的收藏</view>
          </view>
          <view
            class="item"
            @click="
              navTo(`/pages_user/resume/collectPage?scene=delivery`, true)
            "
          >
            <image :src="setImg(`/images/myUser/delivery.png`)" />
            <view class="txt">我的投递</view>
          </view>
          <view class="item" @click="goInvit">
            <image :src="setImg(`/images/myUser/referralCode.png`)" />
            <view class="txt">邀请码</view>
          </view>
        </view>
      </view>
    </view>
    <view class="user-card mt-4">
      <view class="user-card__title"> 其他功能 </view>
      <view class="user-card__content user_cells">
        <view class="user_cell" @click="gotoAboutUs">
          <view class="cl">
            <image :src="setImg(`/images/myUser/aboutUs.png`)" />
            <text>关于我们</text>
          </view>
          <view class="cr">
            <image :src="setImg('images/right_icon.png')" />
          </view>
        </view>
        <view class="user_cell" @click="navTo('/pages/user/more', true)">
          <view class="cl">
            <image :src="setImg(`/images/myUser/setting.png`)" />
            <text>设置</text>
          </view>
          <view class="cr">
            <image :src="setImg('images/right_icon.png')" />
          </view>
        </view>
      </view>
    </view>
    <view class="footspace"></view>
    <h-tabbar :tabbarValue.sync="tabbarValue"></h-tabbar>
    <buy-vip
      ref="buyVip"
      :point="userInfo.point"
      :vipShow="vipShow"
      @closePop="vipShow = false"
    ></buy-vip>

    <InvitPopup :show.sync="showPopup" @success="updateData"></InvitPopup>
    <VIPPopup :show.sync="vipPopupShow" @success="updateData"></VIPPopup>
    <Modal />
  </view>
</template>

<script>
import { checkLogin } from "@/utils/auth";
import { GET_RESUME_DATA, MY_COUNT } from "@/api/resume.js";
import hTabbar from "@/components/hTabbar.vue";
import buyVip from "@/components/buyVip.vue";
import InvitPopup from "../components/invitPopup.vue";
import VIPPopup from "../components/vipPopup.vue";
import Level from "@/model/enums/level";
export default {
  components: { hTabbar, buyVip, InvitPopup, VIPPopup },
  data() {
    return {
      Level,
      statusBarHeight: 0,
      isTopActive: false,
      userInfo: {},
      tabbarValue: 2,
      vipShow: false,
      showPopup: false,
      jobWant: {},
      collectNum: 0,
      matchCount: 0,
      itemCount: 0,
      isVip: 0,
      vipPopupShow: false,
    };
  },
  onShow() {
    if (checkLogin(false)) {
      this.loadUserInfo();
    }
  },
  computed: {
    isLogin() {
      return checkLogin(false);
    },
  },
  async created() {
    this.statusBarHeight = uni.getSystemInfoSync()["statusBarHeight"];
  },
  onPageScroll(res) {
    if (res.scrollTop > 50) {
      this.isTopActive = true;
    } else {
      this.isTopActive = false;
    }
  },
  methods: {
    updateData() {
      this.loadUserInfo();
    },
    closePop() {
      this.showPopup = false;
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    home() {
      uni.navigateTo({
        url: "/pages/index/index",
      });
    },
    publish() {
      uni.navigateTo({
        url: "/pages/publish/index",
      });
    },
    user() {
      uni.navigateTo({
        url: "/pages/user/index",
      });
    },
    navTo(url, loginFlag = false) {
      console.log("loginFlag");

      if (!this.isLogin && loginFlag) {
        uni.navigateTo({
          url: `/pages/public/oauth`,
        });
        return;
      }
      this.navigateUtil.goto(url);
    },
    message() {
      uni.navigateTo({
        url: "/pages/message/index",
      });
    },
    vipFn() {
      this.vipPopupShow = true;
      // this.openModal({
      //   title: "会员服务升级中",
      //   content: "为您的求职之路量身打造王者待遇!",
      //   icon: "none",
      // });
      // uni.showModal({
      //   title: "",
      //   content: "会员服务升级中 - 为您的求职之路量身打造王者待遇!",
      //   confirmText: "关闭",
      //   showCancel: false,
      // });
    },
    logout() {
      this.$store.dispatch("logout");
      this.userInfo = {};
      uni.navigateTo({
        url: "/pages/public/login",
      });
    },
    async loadUserInfo() {
      this.userInfo = await this.$store.dispatch("refreshInfo");
      // 获取简历
      const result = await GET_RESUME_DATA();
      if (this.qUtil.validResp(result)) {
        this.jobWant = result?.data?.jobWant || {};
      }

      if (this.userInfo.resume) {
        const userResp = await this.uniRequest.get("/member/resume");
        if (this.qUtil.validResp(userResp)) {
          const userInfo = userResp.data;
          const jobWantList = userInfo.jobWantList || [];
          const industryLabel = jobWantList.map((m) => m.industryLabel);
          this.$set(this.userInfo, "industryLabel", industryLabel.join(","));
        }
      }

      const count = await MY_COUNT();
      if (this.qUtil.validResp(count) && count.code === 200) {
        this.collectNum = count.data.collectCount;
        this.matchCount = count.data.matchCount;
        this.itemCount = count.data.itemCount;
      }

      uni.stopPullDownRefresh(); //关闭下拉刷新
    },
    goInvit() {
      // this.showPopup = true;
      this.navTo("/pages_user/resume/myInvitCode");
    },
    gotoAboutUs() {
      const url = "https://jobtap.com.cn/";
      this.navigateUtil.goto(
        `/pages/public/web?url=${encodeURIComponent(url)}`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.page-user {
  position: relative;
  padding: 0 32rpx 32rpx;
  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: -1;
  }
  ::v-deep .u-cell-group {
    height: auto;
    overflow: hidden;
    background: #fff;
    margin: 50rpx 30rpx;
    position: relative;
    z-index: 1;
    border-radius: 20rpx;

    .u-cell__title-text {
      line-height: 40px;
    }
  }
}

.topbar {
  width: 100%;
  height: auto;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  padding-bottom: 100rpx;
  background: rgba($color: #18c2a5, $alpha: 0);
  text-align: center;
  padding-bottom: 30rpx;
  transition: all 1s ease;

  .navbar {
    padding: 20rpx 40rpx 0rpx 40rpx;
    font-size: 34rpx;
    color: #000;
    text-align: left;
    font-weight: bold;
    transition: all 1s ease;
  }

  &.active {
    background: rgba($color: #18c2a5, $alpha: 1);

    .navbar {
      color: #fff;
    }
  }
}

.user_top {
  height: auto;
  overflow: hidden;
  padding: 0rpx 0 48rpx 0;
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top_info {
    display: flex;
    align-items: center;
    gap: 38rpx;
    flex: 1;

    .ltit {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 16rpx;

      .name {
        font-size: 32rpx;
        font-weight: 600;
        line-height: 1;
      }

      .info {
        display: inline-flex;
        align-items: center;
        padding: 0 16rpx;
        height: 52rpx;
        font-size: 24rpx;
        color: #86909c;
        background: linear-gradient(90deg, #fff6e3 0%, #e4fffa 100%);
        border-radius: 30rpx;

        image {
          width: 40rpx;
          margin-right: 4rpx;
        }

        text {
          font-size: 12px;
          color: #18c2a5;
          margin-left: 10rpx;
        }

        &.no_vip {
          background: #f7f8fa;
          color: #86909c;

          // 图片变成灰色
          image {
            filter: grayscale(1);
          }
        }
        &.waiting {
          background: #e8f3ff;
          color: #165dff;
        }
        &.inner-test {
          background: #e3fffb;
          color: $uv-primary;
        }
      }
    }
  }

  .top_login {
    padding: 20rpx 0;

    .defaultBox {
      display: flex;
      align-items: center;

      &_img {
        width: 120rpx;
        height: 120rpx;
        margin-right: 25rpx;
        margin-left: 10rpx;
        // border: 2rpx solid #fff;
        border-radius: 50%;
        overflow: hidden;
      }

      image {
        width: 100%;
      }

      &_text {
        text:nth-child(1) {
          display: block;
          font-size: 20px;
          font-weight: 600;
          padding-bottom: 8rpx;
        }

        text:nth-child(2) {
          display: block;
          font-size: 12px;
          color: #999;
        }
      }
    }

    button {
      width: 300rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 8rpx;
      background: #18c2a5;
      font-size: 26rpx;
      color: #fff;
      margin: 0 auto;
    }
  }

  .user_login_bin {
    width: 176rpx;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 28rpx;
    font-weight: 600;
    border-radius: 84rpx;
    color: #fff;
    text-align: center;
    background: #18c2a5;
  }

  .user_toVip_bin {
    width: 150rpx;
    height: 58rpx;
    line-height: 58rpx;
    font-size: 24rpx;
    font-weight: 600;
    border-radius: 56rpx;
    color: #f4d8a1;
    text-align: center;
    background: linear-gradient(90deg, #6f778e -22%, #172748 100%);
  }
}
.user-card {
  background: #fff;
  border-radius: 32rpx;
  &__title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 84rpx;
    padding: 0 32rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
  &__content {
    padding: 8rpx 32rpx 32rpx;
  }
}

.user_resumes {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    margin-top: 20rpx;
  }

  .item {
    width: 30%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 64rpx;
      height: 64rpx;
    }

    .txt {
      font-size: 24rpx;
      color: #1d2129;
      margin-top: 15rpx;
    }
  }
}

.user_resume {
  height: 230rpx;
  overflow: hidden;
  position: relative;
  z-index: 1;
  margin: -15rpx 25rpx 25rpx 25rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  .bg {
    width: 100%;
    display: block;
    position: absolute;
    left: 0;
    top: 0;
  }

  .res_top {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;
    margin-bottom: 20rpx;

    .tl {
      margin-right: 20rpx;
    }

    .tr {
      flex: 1;

      .rt {
        font-size: 32rpx;
        color: #6f89ff;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      .rc {
        font-size: 24rpx;
        color: #bdc9ff;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        width: 280rpx;
      }
    }
  }

  .res_bt {
    position: relative;
    z-index: 1;

    button {
      height: 60rpx;
      line-height: 60rpx;
      font-size: 28rpx;
      color: #fff;
      font-weight: bold;
      border-radius: 8rpx;
      background: #18c2a5;
      padding: 0 30rpx;
    }
  }
}
.user_cells {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.user_cell {
  height: 56rpx;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .cl {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #1d2129;
    flex: 1;

    image {
      margin-right: 20rpx;
      width: 40rpx;
      height: 40rpx;
    }
  }

  .cr image {
    width: 43rpx;
    height: 43rpx;
  }
}
</style>
