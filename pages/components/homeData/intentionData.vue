<template>
  <view class="intention-data">
    <view class="data-item">
      <text>企业总数</text>
      <view class="num">
        <text class="count">
          <u-count-to
            :start-val="0"
            :end-val="info.companyCount"
            :duration="2000"
            :autoplay="true"
            :useEasing="true"
            separator=","
            color="#1D2129"
            bold="true"
            fontSize="40rpx"
          />
        </text>
        <text>家</text>
      </view>
    </view>
    <view class="data-item">
      <text>岗位总数</text>
      <view class="num">
        <text class="count">
          <u-count-to
            :start-val="0"
            :end-val="info.jobCount"
            :duration="2000"
            :autoplay="true"
            :useEasing="true"
            separator=","
            color="#1D2129"
            bold="true"
            fontSize="40rpx"
          />
        </text>
        <text>个</text></view
      >
    </view>
    <view class="data-item">
      <text>近7日新增</text>
      <view class="num">
        <text class="count">
          <u-count-to
            :start-val="0"
            :end-val="info.jobCount7D"
            :duration="2000"
            :autoplay="true"
            :useEasing="true"
            separator=","
            color="#1D2129"
            bold="true"
            fontSize="40rpx"
          />
        </text>
        <text>个岗位</text>
      </view>
    </view>
    <view class="data-item">
      <text>近30日新增</text>
      <view class="num">
        <text class="count">
          <u-count-to
            :start-val="0"
            :end-val="info.jobCount30D"
            :duration="2000"
            :autoplay="true"
            :useEasing="true"
            separator=","
            color="#1D2129"
            bold="true"
            fontSize="40rpx"
          />
        </text>
        <text>个岗位</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => ({
        companyCount: 0,
        jobCount: 0,
        jobCount7D: 0,
        jobCount30D: 0,
      }),
    },
  },
  methods: {
    changeNum(num) {
      if (num < 10000) {
        return num;
      }
      return (num / 10000).toFixed(2);
    },
    numUnit(num) {
      return num < 10000 ? "" : "万";
    },
  },
};
</script>

<style lang="scss" scoped>
.intention-data {
  display: flex;
  flex-wrap: wrap;
  padding-top: 40rpx;

  .data-item {
    width: 50%;
    font-size: 24rpx;
    color: #86909c;
    line-height: 1.4;
    margin-bottom: 32rpx;
    .num {
      display: flex;
      align-items: baseline;
      gap: 10rpx;
    }
    .count {
      color: #1d2129;
      font-size: 40rpx;
      font-weight: 600;
    }
  }
}
</style>
