<template>
  <view class="company-list mt-4">
    <view
      v-for="(item, index) in list"
      :key="index"
      class="company-item"
      @click="navigateToDetail(item)"
    >
      <CompanyLogo :item="item"></CompanyLogo>
      <view class="info-col">
        <text class="company-name">{{ item.name }}</text>
        <text class="job-count">相关岗位{{ item.jobCount }}个</text>
      </view>
      <view class="arrow-col">
        <image :src="setImg('images/right_icon.png')" />
      </view>
    </view>
    <view class="flex gap-4">
      <button class="btn flex-1" open-type="share">分享给好友</button>
      <view class="btn flex-1" @click="navTo(`/pages_home/home/<USER>">
        查看更多企业
      </view>
    </view>
  </view>
</template>
<script>
import global from "@/common/global";
import CompanyLogo from "./CompanyLogo.vue";
export default {
  name: "CompanyList",
  components: {
    CompanyLogo,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    cunt: {
      type: Number,
      default: 5,
    },
  },
  computed: {
    list() {
      if (this.info["last90D"]) {
        return this.info["last90D"].slice(0, this.cunt);
      }
      return [];
    },
  },
  data() {
    return {
      companyList: [
        {
          logo: "path/to/mcdonalds-logo.png",
          name: "McDonald's",
          jobCount: 236,
        },
        { logo: "path/to/jnj-logo.png", name: "J&J", jobCount: 432 },
        { logo: "path/to/byd-logo.png", name: "BYD", jobCount: 50 },
        { logo: "path/to/tesla-logo.png", name: "Tesla", jobCount: 36 },
        { logo: "path/to/nike-logo.png", name: "Nike", jobCount: 30 },
      ],
    };
  },
  methods: {
    setImg(url) {
      return global.STATIC_URL + url;
    },
    navigateToDetail(item) {
      uni.navigateTo({
        url: `/pages_home/jobList?companyId=${item.id}&companyName=${item.name}`,
      });
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
  },
};
</script>
<style lang="scss" scoped></style>
