<template>
  <view class="intention-data">
    <view class="data-item">
      <text>目标城市</text>
      <view class="con">{{ jobWantInfo.city }}</view>
    </view>
    <view class="data-item">
      <text>目标行业</text>
      <view class="con">{{ jobWantInfo.industryLabel }}</view>
    </view>
    <view class="data-item">
      <text>意向岗位</text>
      <view class="con">{{ jobWantInfo.jobTypeLabel }}</view>
    </view>
    <view class="data-item">
      <text>企业偏好</text>
      <view class="con">{{ `${jobWantInfo.natureLabel || "不限"}` }}</view>
    </view>
  </view>
</template>

<script>
import { GET_JOB_WANT } from "@/api/resume.js";

export default {
  props: {
    jobWantId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      jobWantInfo: {},
      showTips: false,
    };
  },
  created() {
    this.getJobWantData();
    uni.$on("updateResume", () => {
      this.getJobWantData();
    });
  },
  destroyed() {
    uni.$off("updateResume");
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    // 获取求职意向信息
    async getJobWantData() {
      const res = await GET_JOB_WANT(this.jobWantId);
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.jobWantInfo = res.data;
      }
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.intention-data {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 8rpx;
  .data-item {
    width: 50%;
    margin-bottom: 32rpx;
    font-size: 24rpx;
    color: #86909c;
    line-height: 1.4;

    .con {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 32rpx;
      color: #1d2129;
    }
  }
}
</style>
