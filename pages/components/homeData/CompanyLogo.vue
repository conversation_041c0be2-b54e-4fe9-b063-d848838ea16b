<template>
  <view class="company-logo">
    <u-avatar
      :src="item.logoUrl || ''"
      fontSize="14"
      mode="aspectFit"
      :text="logoName(item)"
      randomBgColor
      size="80rpx"
    ></u-avatar>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    logoName(item) {
      if (item.logoUrl) {
        return "";
      }
      let name = item.name.replace(
        /(中国|北京|上海|广州|深圳|厦门|杭州|苏州|南京|天津|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)(省|市)?/g,
        ""
      );
      return name.substr(0, 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.company-logo {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  border-radius: 50%;
  background: #fff;
}
</style>
