<template>
  <view class="home-data-container">
    <view class="my-intention-card">
      <CardTitle size="sm">我的求职意向</CardTitle>
      <view
        class="refresh-btn"
        @click="navTo(`/pages_user/resume/expectation?id=${jobWantId}`)"
      >
        <image class="icon" :src="setImg(`/images/home/<USER>"></image>
        <text>更新意向</text>
      </view>
      <MyIntentDatalData :jobWantId="jobWantId" />
      <view class="splitter"></view>
      <IntentionData :info="matchData" />
      <view class="flex justify-center">
        <view
          class="btn mx-auto"
          @click="navTo('/pages_home/home/<USER>')"
        >
          查看全网数据
        </view>
      </view>
    </view>

    <!-- 意向数据概览 -->
    <RankingCard
      type="companies"
      title="热门企业"
      enTitle="Hot Company"
      color="blue"
    >
      <CompanyList :info="matchCompanyData" />
    </RankingCard>
  </view>
</template>

<script>
import IntentionData from "./intentionData.vue";
import CompanyList from "./companyList.vue";
import myIntentDatalData from "./myIntentDatalData.vue";
import RankingCard from "@/components/rankingCard.vue";
import CardTitle from "@/components/cardTitle.vue";
import { GET_MATCH_COUNT, GET_MATCH_COMPANY } from "@/api/resume";
export default {
  props: {
    jobWantId: {
      type: Number,
      default: 0,
    },
  },
  components: {
    CardTitle,
    RankingCard,
    IntentionData,
    CompanyList,
    myIntentDatalData,
  },
  data() {
    return {
      matchData: {},
      matchCompanyData: {},
    };
  },
  created() {
    this.getMatchCount();
    this.getMatchCompany();
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    navTo(url) {
      this.navigateUtil.goto(url);
      // uni.navigateTo({
      //   url,
      // });
    },
    // 意向匹配统计
    async getMatchCount() {
      const result = await GET_MATCH_COUNT();
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.matchData = result.data || {};
      }
    },
    // 意向企业榜单
    async getMatchCompany() {
      const result = await GET_MATCH_COMPANY();
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.matchCompanyData = result.data || {};
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.my-intention-card {
  position: relative;
  margin-bottom: 60rpx;
  background: #fff;
  border-radius: 32rpx;
  padding: 80rpx 40rpx 40rpx;
  box-shadow: 0 2px 8px #0000001a;
  background: linear-gradient(to bottom, #eefefb, #fff);
  &:after,
  &:before {
    content: "";
    position: absolute;
    top: 290rpx;
    width: 10rpx;
    height: 20rpx;
    background: #fff;
    // box-shadow: 0 0 8px #0000001a inset;
  }
  &:before {
    border-radius: 0 20rpx 20rpx 0;
    left: 0rpx;
    box-shadow: 0px 0 16px #0000000f inset;
  }
  &:after {
    border-radius: 20rpx 0 0 20rpx;
    right: 0rpx;
    box-shadow: 0 0 16px #0000000f inset;
  }
  .splitter {
    position: absolute;
    top: 300rpx;
    left: 3%;
    right: 4%;
    height: 0;
    border-bottom: 0.5px dashed #e5e6eb;
  }
  .refresh-btn {
    position: absolute;
    top: 20rpx;
    right: 40rpx;
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #86909c;
    .icon {
      width: 28rpx;
      height: 28rpx;
      margin-right: 4rpx;
    }
  }
}
.home-data-container {
  .home-data-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30rpx 0;
    color: #1d2129;
    view {
      &:nth-child(1) {
        font-size: 32rpx;
        font-weight: 500;
      }
      &:nth-child(2) {
        height: 34rpx;
        line-height: 34rpx;
        text-align: center;
        font-size: 24rpx;
        border-radius: 20rpx;
        border: 1px solid #e5e6eb;
        padding: 0 20rpx;
      }
    }
  }
}
</style>
