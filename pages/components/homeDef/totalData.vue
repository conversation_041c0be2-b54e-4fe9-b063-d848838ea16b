<template>
  <DataCard>
    <view class="flex items-center" slot="title">
      <text>全网数据概览</text>
      <image
        class="icon-frame"
        :src="setImg(`/images/public/frame.png?v=2`)"
        mode="aspectFill"
        @click="showTips = true"
      ></image>
    </view>

    <view class="total-top">
      <view class="top-item">
        <text class="label">企业总数</text>
        <text class="num">
          <u-count-to
            :start-val="0"
            :end-val="changeNum(totalData.companyCount)"
            :duration="2000"
            :autoplay="true"
            :useEasing="false"
            color="#1D2129"
            bold="true"
            fontSize="32rpx"
            :decimals="totalData.companyCount > 10000 ? 2 : 0"
          />
        </text>
        <text>{{ numUnit(totalData.companyCount) }}家</text>
      </view>
      <view class="top-item">
        <text class="label">岗位总数</text>
        <text class="num">
          <u-count-to
            :start-val="0"
            :end-val="changeNum(totalData.jobCount)"
            :duration="2000"
            :autoplay="true"
            :useEasing="false"
            color="#1D2129"
            bold="true"
            fontSize="32rpx"
            :decimals="totalData.jobCount > 10000 ? 2 : 0"
          />
        </text>
        <text>{{ numUnit(totalData.jobCount) }}个</text>
      </view>
    </view>
    <view class="total-content">
      <view class="data-item">
        <view class="label">近7日新增</view>
        <view class="num">
          <u-count-to
            :start-val="0"
            :end-val="totalData.jobCount7D"
            :duration="2000"
            :autoplay="true"
            :useEasing="true"
            separator=","
            color="#1D2129"
            bold="true"
            fontSize="48rpx"
          />
          <text> 个岗位</text>
        </view>
        <view v-if="totalData.jobCount7DChange">
          <image
            class="icon-up"
            :src="
              setImg(
                `/images/home/<USER>
                  totalData.jobCount7DChange == -1 ? 'decrease' : 'rise'
                }.png`
              )
            "
          ></image>
          <text
            :style="{
              color: totalData.jobCount7DChange == -1 ? '#00B42A' : '#F53F3F',
            }"
          >
            <u-count-to
              :start-val="0"
              :end-val="percentage(totalData.jobCount7DPercentage)"
              :duration="2000"
              :autoplay="true"
              :useEasing="false"
              decimals="2"
              :color="totalData.jobCount7DChange == -1 ? '#00B42A' : '#F53F3F'"
              fontSize="24rpx"
            />%
          </text>
        </view>
      </view>
      <view class="data-item">
        <view class="label">近30日新增</view>
        <view class="num">
          <u-count-to
            :start-val="0"
            :end-val="totalData.jobCount30D"
            :duration="2000"
            :autoplay="true"
            :useEasing="true"
            separator=","
            color="#1D2129"
            bold="true"
            fontSize="48rpx"
          />
          <text>个岗位</text>
        </view>
        <view v-if="totalData.jobCount30DChange">
          <image
            class="icon-up"
            :src="
              setImg(
                `/images/home/<USER>
                  totalData.jobCount30DChange == -1 ? 'decrease' : 'rise'
                }.png`
              )
            "
          ></image>
          <text
            :style="{
              color: totalData.jobCount30DChange == -1 ? '#00B42A' : '#F53F3F',
            }"
          >
            <u-count-to
              :start-val="0"
              :end-val="percentage(totalData.jobCount30DPercentage)"
              :duration="2000"
              :autoplay="true"
              :useEasing="false"
              decimals="2"
              :color="totalData.jobCount30DChange == -1 ? '#00B42A' : '#F53F3F'"
              fontSize="24rpx"
            />%
          </text>
        </view>
      </view>
    </view>

    <!-- 提示弹出框 -->
    <u-popup
      :show="showTips"
      mode="center"
      :closeOnClickOverlay="true"
      @close="showTips = false"
      :round="20"
      :safeAreaInsetBottom="false"
      :customStyle="{ width: '80%' }"
    >
      <view class="tips-popup">
        <view class="tips-content">
          <view class="tips-title"> 什么是全网职位数据 </view>
          <view class="tips">
            <view class="tips_text">
              我们整合了超过2300个公开渠道的权威职位信息，主要来源于全国1800余所高校官方就业网站。
            </view>
            <view class="tips_text">
              与主流招聘平台数据对比，我们的数据覆盖更广、更及时，几乎实现了校园招聘和实习岗位的全网覆盖。
            </view>
            <view class="tips_text">
              你所看到的岗位数据，已经经过系统实时更新、企业验证和信息完善，确保信息丰富、及时和可靠。
            </view>
          </view>
        </view>
        <view class="tips-btn" @click="showTips = false"> 知道了 </view>
      </view>
    </u-popup>
  </DataCard>
</template>

<script>
import DataCard from "@/components/dataCard.vue";
export default {
  props: {
    totalData: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    DataCard,
  },
  data() {
    return {
      showTips: false,
    };
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    changeNum(num) {
      if (!num) return 0;
      if (num < 10000) {
        return num;
      }
      return (num / 10000).toFixed(2);
    },
    numUnit(num) {
      if (!num) return "";
      return num < 10000 ? "" : "万";
    },
    percentage(str) {
      if (!str) return 0;
      return parseFloat(str.replace("%", ""));
    },
  },
};
</script>

<style lang="scss" scoped>
.icon-frame {
  width: 28rpx;
  height: 28rpx;
  padding: 10rpx; /* 增加内边距扩大可点击区域 */
}
.total-top {
  height: 84rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  background: #a8cec926;
  border-radius: 16rpx;

  .top-item {
    display: flex;
    align-items: center;
    flex: 1;
    color: #86909c;
    font-size: 24rpx;
    text {
      white-space: nowrap;
      &.label {
        margin-right: 20rpx;
        font-weight: 600;
        color: #1d2129;
      }

      &.num {
        margin-right: 8rpx;
        font-size: 32rpx;
        font-weight: 600;
        color: #1d2129;
      }
    }
  }
}

.total-content {
  display: flex;
  padding: 20rpx 20rpx 0;

  .data-item {
    display: flex;
    flex-direction: column;
    flex: 1;

    view {
      &.label {
        color: #1d2129;
        font-size: 28rpx;
        font-weight: 600;
        line-height: 40rpx;
      }

      &.num {
        display: flex;
        align-items: baseline;
        gap: 10rpx;
        color: #86909c;
        font-size: 24rpx;
      }

      &:nth-child(3) {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        margin-top: -4rpx;
        .icon-up {
          margin-right: 6rpx;
          width: 20rpx;
          height: 20rpx;
        }
      }
    }
  }
}

.bgimg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.tips-popup {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  .tips-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    color: $uv-primary;
    font-size: 32rpx;
    border-top: 1px solid #e5e5e5;
  }
}
.tips-content {
  padding: 40rpx 32rpx;
  .tips-title {
    margin-bottom: 10rpx;
    font-size: 36rpx;
    font-weight: 500;
    color: #1d2129;
    text-align: center;
  }
  .tips {
    font-size: 30rpx;
    color: #4e5969;
    line-height: 1.4;
    .tips_text {
      text-indent: 32rpx;
    }
  }
}
</style>
