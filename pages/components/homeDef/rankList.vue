<template>
  <view class="rank-list">
    <view class="hot-title">
      <image
        class="hot-title-icon"
        :src="setImg(`/images/home/<USER>"
        mode="aspectFill"
      ></image>
      <text class="hot-title-text gradient-text">{{
        from == "allNetData" ? "热门榜单区" : "热门榜单"
      }}</text>
    </view>
    <view class="hot-lists" v-if="list.length">
      <RankingCard
        :type="item.icon"
        :title="item.title"
        :enTitle="item.enTitle"
        v-for="(item, index) in list"
        :key="index"
        :color="['cities', 'positions'].includes(item.icon) ? 'org' : 'blue'"
      >
        <RankingList
          :list="sliceList(item.data['last90D'])"
          :type="item.icon"
        />

        <button class="btn w-full mt-4" @click="seeMore(item)">
          {{ item.btn }}
        </button>
      </RankingCard>
    </view>
  </view>
</template>

<script>
import RankingList from "@/components/rankingList.vue";
import RankingCard from "@/components/rankingCard.vue";
import { checkLogin } from "@/utils/auth";
export default {
  components: { RankingCard, RankingList },
  props: {
    info: {
      type: Object,
      default: () => [],
    },
    from: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      hotItems: [
        {
          title: "热门行业",
          enTitle: "Hot Industries",
          icon: "industries",
          color: "#D0DBED",
          btn: "查看更多行业排行",
          key: "industryRanking",
        },
        {
          title: "热门城市",
          enTitle: "Hot City",
          icon: "cities",
          color: "#EDD2CC",
          btn: "查看更多城市排行",
          key: "cityRanking",
        },
        {
          title: "热门企业",
          enTitle: "Hot Company",
          icon: "companies",
          color: "#D3DDEE",
          btn: "查看更多企业榜单",
          key: "companyRanking",
        },
        {
          title: "热门岗位",
          enTitle: "Hot Positions",
          icon: "positions",
          color: "#EDD2CC",
          btn: "查看更多岗位榜单",
          key: "jobTypeRanking",
        },
      ],
    };
  },
  computed: {
    list() {
      const rankList = this.hotItems
        .map((item) => {
          if (this.info && this.info[item.key]) {
            return {
              ...item,
              data: this.info[item.key],
            };
          } else {
            return null; // 如果不存在，则返回 null
          }
        })
        .filter((item) => item !== null);
      return rankList;
    },
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    sliceList(list) {
      return list.slice(0, 5);
    },
    seeMore(item) {
      if (!checkLogin(true)) {
        return;
      }

      const url = `/pages_home/home/<USER>
      this.$store.commit("SET_RANK_LIST", item.data["last90D"]);
      this.navigateUtil.goto(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.gradient-text {
  background-image: linear-gradient(to right, #1d2129 60%, #4b6757);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}
.rank-list {
  padding-top: 40rpx;
  position: relative;
  z-index: 1;

  .hot-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #1d2129;
    .hot-title-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 10rpx;
    }
  }

  .hot-lists {
    display: flex;
    flex-direction: column;
    gap: 40rpx;
  }
}
</style>
