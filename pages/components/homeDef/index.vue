<template>
  <view
    class="home-main-container"
    :style="{ paddingBottom: from === 'home' ? '40rpx' : '0' }"
  >
    <!-- 全网数据概览 -->
    <TotalData :totalData="totalData" :from="from" />
    <!-- 排行榜单 -->
    <RankList :info="rankInfo" :from="from" />
  </view>
</template>

<script>
import TotalData from "./totalData.vue";
import RankList from "./rankList.vue";
import { GET_DATA_TOTAL, GET_DATA_RANK } from "@/api/resume.js";
export default {
  props: {
    from: {
      type: String,
      default: "home",
    },
  },
  components: {
    TotalData,
    RankList,
  },
  data() {
    return {
      totalData: {},
      rankInfo: {},
    };
  },
  mounted() {
    this.getDataTotal();
    this.getDataRank();
  },
  methods: {
    setImg(url) {
      return this.qUtil.setImg(url);
    },
    // 获取全网数据信息
    async getDataTotal() {
      const res = await GET_DATA_TOTAL();
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.totalData = res.data || {};
      }
    },
    // 获取热门榜单
    async getDataRank() {
      const res = await GET_DATA_RANK();
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.rankInfo = res.data || {};
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
