<template>
  <view class="uni-margin-wrap">
    <swiper
      class="swiper"
      :style="{ height: HintHeight }"
      circular
      :indicator-dots="true"
      :autoplay="false"
      indicator-color="#CECECE"
      indicator-active-color="#18C2A5"
      :interval="2000"
      :duration="500"
      @change="intervalChange"
    >
      <!-- @click="goDetail('S10', el)" -->
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in swiperData"
        :key="index"
      >
        <view class="hintList" :id="'hintList' + index">
          <view
            :class="['hintBox', { active: el.length > 24 }]"
            v-for="(el, ind) in item"
            :key="ind"
            @click="$emit('goChat', 'S10', el)"
          >
            <image :src="setImg()" mode="widthFix" />
            <text>{{ el }}</text>
          </view>
        </view>
        <!-- <view class="swiper-item uni-bg-red">A</view> -->
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import global from "@/common/global";
export default {
  props: {
    // 轮播图数据
    swiperData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      HintHeight: "200px",
    };
  },
  mounted() {},
  onReady() {
    setTimeout(() => {
      this.getElementInfo();
    }, 5000);
  },
  methods: {
    intervalChange(e) {
      this.getElementInfo(e.target.current);
    },
    getElementInfo(ind = 0) {
      // 创建选择器对象
      // 选择我们想要的元素
      uni
        .createSelectorQuery()
        .in(this)
        .select("#hintList" + ind)
        .boundingClientRect((data) => {
          if (data) {
            this.HintHeight = data.height + 35 + "px";
            // data包含元素的尺寸信息，如宽度（data.width）和高度（data.height）
          }
        })
        .exec(); // 执行选择器查询
    },
    setImg() {
      return this.staticBaseUrl + "images/index/problem.png";
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
