.uni-margin-wrap {
  position: relative;
  box-sizing: border-box;
  z-index: 2;
}
.swiper {
  // min-height: 350rpx;
  height: 350rpx;
}
.swiper-item {
  //   height: 500rpx;
}
.hintList {
  position: relative;
  padding: 0 29rpx;
  box-sizing: border-box;
  z-index: 2;
  .hintBox {
    display: flex;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 40rpx;
    padding: 26rpx 26rpx 26rpx 22rpx;
    margin-bottom: 22rpx;
    overflow-x: auto;
    image {
      min-width: 29rpx;
      width: 29rpx;
    }
    text {
      // white-space: nowrap;
      font-size: 14px;
      color: #333;
      line-height: 1.5;
      padding-left: 14rpx;
    }
  }
  .active {
    padding: 20rpx 26rpx 20rpx 22rpx;
  }
}
