<template>
  <u-popup
    :show="show"
    round="32rpx"
    bgColor="#fff"
    :closeable="false"
    :safeAreaInsetBottom="false"
    @close="closePop"
  >
    <view class="evaluate-log-popup">
      <view class="title">{{ title }}</view>
      <view class="evaluate-log mt-2">
        <view
          class="evaluate-log-item"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <view class="flex-1">{{ item.startTime }}</view>
          <view class="flex-1" :class="{ uncomplete: item.status != 3 }">
            {{ status[item.status - 1] }}
          </view>
        </view>
      </view>
      <button class="btn w-full big mt-4" @click="closePop">关闭</button>
    </view>
  </u-popup>
</template>

<script>
import { RESULT_LIST } from "@/api/resume.js";
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "S1",
    },
  },
  data() {
    return {
      dataList: [],
      status: ["未开始", "进行中", "已成功", "已失败"],
    };
  },
  computed: {
    title() {
      return this.type == "S1" ? "测评记录" : "面试记录";
    },
  },
  methods: {
    async getData() {
      const res = await RESULT_LIST(this.type);
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.dataList = res.data || [];
      }
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    closePop() {
      this.$emit("update:show", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.evaluate-log-popup {
  padding: 32rpx;
  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #1d2129;
  }
  .evaluate-log {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    font-size: 24rpx;
    .evaluate-log-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 72rpx;
      padding: 0 16rpx;
      background: #f7f8fa;
      border-radius: 16rpx;
      .uncomplete {
        color: #c00f0c;
      }
    }
  }
}
</style>
