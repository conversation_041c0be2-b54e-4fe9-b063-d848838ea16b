<template>
  <u-popup
    :show="show"
    round="32rpx"
    bgColor="#fff"
    :closeable="false"
    @close="closePop"
  >
    <view class="invitPopup">
      <view class="pop_top">
        <view class="ptit">开通一箭职达VIP</view>
      </view>
      <view class="pop_box">
        <view class="tips"> 请输入专属VIP兑换码，立即解锁VIP特权： </view>
        <view class="ipt">
          兑换码
          <input
            type="text"
            placeholder="请输入"
            placeholder-style="color: #C9CDD4"
            v-model="invitCode"
            cursor-spacing="120"
          />
        </view>
      </view>
      <view class="footbar">
        <button @click="handleCancel" class="btn secondary big flex-1">
          取消
        </button>
        <button @click="submit" class="btn primary big flex-1">提交</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { mapGetters } from "vuex";
import { USE_VIP_CODE } from "@/api/resume.js";

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      invitCode: "",
    };
  },
  created() {
    // console.log("userInfo", this.userInfo);
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    closePop() {
      this.$emit("update:show", false);
    },
    handleCancel() {
      this.closePop();
      this.$emit("cancel");
    },
    submit() {
      // this.$emit("success");
      // this.closePop();
      // return;
      if (this.invitCode.trim() === "") {
        uni.showToast({
          title: "请输入邀请码",
          icon: "none",
        });
        return;
      }
      USE_VIP_CODE({ code: this.invitCode }).then((res) => {
        if (this.qUtil.validResp(res) && res.code === 200) {
          this.$store.dispatch("refreshInfo");
          this.closePop();
          this.$emit("success");
        } else {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
        }
      });
    },
    joinWaitlist() {
      uni.navigateTo({
        url: "/pages_user/resume/waitlist",
      });
      this.closePop();
    },
  },
};
</script>

<style lang="scss">
.invitPopup {
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  position: relative;
  background: linear-gradient(180deg, #e3fffb 0%, #fff 47.01%);

  .pop_top {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 106rpx;

    .ptit {
      font-size: 30rpx;
      font-weight: 500;
    }
  }

  .pop_box {
    padding: 32rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 32rpx;
    .tips {
      font-size: 28rpx;
    }
    .ipt {
      display: flex;
      align-items: center;
      height: 106rpx;
      font-size: 32rpx;
      padding: 0 32rpx;
      border: 1px solid #e5e6eb;
      border-radius: 16rpx;
      input {
        flex: 1;
        margin-left: 32rpx;
      }
    }
    .link {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #86909c;
      font-size: 24rpx;
    }
  }
  .footbar {
    position: relative;
    padding: 33rpx;
    box-shadow: none;
  }
}
</style>
