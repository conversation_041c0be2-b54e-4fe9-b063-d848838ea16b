<template>
  <u-popup
    :show="show"
    round="32rpx"
    bgColor="#fff"
    :closeable="false"
    @close="closePop"
  >
    <view class="invitPopup">
      <view class="pop_top">
        <view class="ptit">欢迎来到一箭职达</view>
      </view>
      <view class="pop_box">
        <view class="tips">
          当前为内测阶段，尚未正式向公众开放。你可以输入邀请码开始使用，或加入等候名单。
        </view>
        <view class="ipt">
          你的邀请码
          <input
            type="text"
            placeholder="请输入"
            placeholder-style="color: #C9CDD4"
            v-model="invitCode"
            cursor-spacing="120"
          />
        </view>
        <view class="link" @click="joinWaitlist">加入等候名单 </view>
      </view>
      <view class="footbar">
        <button @click="handleCancel" class="btn secondary big flex-1">
          取消
        </button>
        <button @click="submit" class="btn primary big flex-1">开始使用</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { mapGetters } from "vuex";
import { USE_INVITE_CODE } from "@/api/resume.js";

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      invitCode: "",
    };
  },
  created() {
    // console.log("userInfo", this.userInfo);
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    closePop() {
      console.log("closePop");
      this.$emit("update:show", false);
    },
    handleCancel() {
      this.closePop();
      this.$emit("cancel");
    },
    submit() {
      // this.$emit("success");
      // this.closePop();
      // return;
      if (this.invitCode.trim() === "") {
        uni.showToast({
          title: "请输入邀请码",
          icon: "none",
        });
        return;
      }
      USE_INVITE_CODE({ code: this.invitCode }).then((res) => {
        if (this.qUtil.validResp(res) && res.code === 200) {
          this.$store.dispatch("refreshInfo");
          this.closePop();
          this.$emit("success");
        } else {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
        }
      });
    },
    joinWaitlist() {
      if (this.userInfo.isVip == 0 && this.userInfo.levelId == 10) {
        this.openModal({
          title: "提示",
          content: "您已经成功加入等候名单，请耐心等待，我们会第一时间通知您。",
          icon: "none",
        });
        return;
      }
      uni.navigateTo({
        url: "/pages_user/resume/waitlist",
      });
      this.closePop();
    },
  },
};
</script>

<style lang="scss">
.invitPopup {
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  position: relative;
  background: linear-gradient(180deg, #e3fffb 0%, #fff 47.01%);

  .pop_top {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 106rpx;

    .ptit {
      font-size: 30rpx;
      font-weight: 500;
    }
  }

  .pop_box {
    padding: 32rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 32rpx;
    .tips {
      font-size: 28rpx;
    }
    .ipt {
      display: flex;
      align-items: center;
      height: 106rpx;
      font-size: 32rpx;
      padding: 0 32rpx;
      border: 1px solid #e5e6eb;
      border-radius: 16rpx;
      input {
        flex: 1;
        margin-left: 32rpx;
      }
    }
    .link {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #86909c;
      font-size: 24rpx;
    }
  }
  .footbar {
    position: relative;
    padding: 33rpx;
    box-shadow: none;
  }
}
</style>
