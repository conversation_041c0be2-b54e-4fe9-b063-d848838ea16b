<template>
  <u-popup
    :show="show"
    :round="20"
    bgColor="#fff"
    :closeable="false"
    :safeAreaInsetBottom="false"
    @close="closePop"
  >
    <view class="evaluationPopup">
      <image
        :src="setImg('/images/match/popTop1.png')"
        class="top_img"
        mode="scaleToFill"
      ></image>
      <image
        :src="setImg('/images/public/icon-close.svg')"
        class="close-btn"
        mode="scaleToFill"
        @click="closePop"
      ></image>
      <view class="pop_top">
        <image
          :src="setImg('/images/match/title-evaluate.png')"
          class="title"
          mode="widthFix"
        ></image>
        <view class="desc">发现你的职场潜能</view>
      </view>
      <view class="pop_box">
        <view class="tips-item">
          <view class="doc"></view>
          <view class="text">
            <text>
              测评预计 10 分钟左右，通过专业量表和 AI
              分析，让AI发现你更多的优势与可能性，精准匹配适合的岗位与成长路线。
            </text>
          </view>
        </view>
        <view class="tips-item">
          <view class="doc"></view>
          <view class="text">
            <text>每月前 3 次无需预约，超过需预约，</text>
            <text style="color: #9e5540">测评中途退出也算一次。</text>
          </view>
        </view>
      </view>
      <view class="qa_box">
        <view class="qa_title">FAQ</view>
        <view class="qa_item">Q：测评结果会公开给企业吗？ </view>
        <view class="qa_item"
          >A：不会，测评数据仅用于内部的求职推荐与个人反馈，尊重并保护你的隐私。</view
        >
        <view class="qa_item">Q：测评会影响我已有的匹配结果吗？ </view>
        <view class="qa_item">A：不会，只影响测评完成后开始的精准匹配。</view>
        <view class="qa_item">Q：如果测评结果不符合预期，可以重测吗？ </view>
        <view class="qa_item"
          >A：测评的题目没有标准答案，只要认真作答即可，如果需要重测也可以在次数要求内重新发</view
        >
      </view>

      <view class="more mt-4" @click="openLog" v-if="matchResult.startTime">
        <text class="m_l"> 最近一次完成时间:{{ matchResult.startTime }} </text>
        <view class="m_r">
          <text>更多记录</text>
          <image class="right_icon" :src="setImg('images/right_icon.png')" />
        </view>
      </view>
      <view
        @click="handleStart"
        class="match-btn brown mid mt-4"
        :disabled="loading"
      >
        <text>开始测评</text>
        <text>
          本月次数：{{ matchResult.remainingTimes }}/{{
            matchResult.totalTimes
          }}
        </text>
      </view>
    </view>
    <EvaluateLogPopup ref="logRef" :show.sync="logPopupShow" />
  </u-popup>
</template>

<script>
import { checkLogin } from "@/utils/auth";
import EvaluateLogPopup from "./evaluateLogPopup.vue";
import { GET_RESULT_LASTEST, START_EVAL } from "@/api/resume.js";
export default {
  components: { EvaluateLogPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "evaluate",
    },
  },
  created() {
    this.getData();
  },
  data() {
    return {
      logPopupShow: false,
      matchResult: {},
      loading: false,
    };
  },
  methods: {
    getData() {
      GET_RESULT_LASTEST("S1").then((res) => {
        if (this.qUtil.validResp(res) && res.code === 200) {
          this.matchResult = res?.data || {};
        }
      });
    },
    openLog() {
      this.$refs.logRef.getData();
      this.logPopupShow = true;
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    closePop() {
      this.$emit("update:show", false);
    },
    async handleStart() {
      if (!checkLogin(true)) return;
      if (this.loading) return;
      try {
        this.loading = true;
        const res = await START_EVAL();
        console.log("START_EVAL", res);
        if (this.qUtil.validResp(res)) {
          uni.navigateTo({
            url: `/pages/public/web?url=${encodeURIComponent(
              res.data.testUrl
            )}`,
          });
        } else {
          uni.$u.toast(res.msg);
        }
      } catch (error) {
      } finally {
        this.loading = false;
      }
      // uni.showModal({
      //   showCancel: false,
      //   title: "提醒",
      //   content: "功能正在开发中,敬请期待!",
      // });
      // this.$emit('buyVip')
    },
    joinWaitlist() {
      uni.navigateTo({
        url: "/pages_user/resume/waitlist",
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
