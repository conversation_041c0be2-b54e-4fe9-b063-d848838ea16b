<template>
  <u-popup
    :show="show"
    :round="20"
    bgColor="#fff"
    :closeable="false"
    :safeAreaInsetBottom="false"
    @close="closePop"
  >
    <view class="evaluationPopup interviewPopup">
      <image
        :src="setImg('/images/match/popTop2.png')"
        class="top_img"
        mode="scaleToFill"
      ></image>

      <image
        :src="setImg('/images/public/icon-close.svg')"
        class="close-btn"
        mode="scaleToFill"
        @click="closePop"
      ></image>
      <view class="pop_top">
        <image
          :src="setImg('/images/match/title-interview.png')"
          class="title"
          mode="widthFix"
        ></image>
        <view class="desc">发现你的职场潜能</view>
      </view>
      <view class="pop_box">
        <view class="tips-item">
          <view class="doc"></view>
          <view class="text">
            面试预计约 20
            分钟，以普通面试的方式进行，重点了解你的真实沟通风格与求职偏好，真实表达即可。
          </view>
        </view>
        <view class="tips-item">
          <view class="doc"></view>
          <view class="text">
            每月前 3 次可随时开始，超过 3
            次需提前预约，面试中途退出也算一次。建议选择安静且网络通畅的环境。
          </view>
        </view>
      </view>
      <view class="qa_box">
        <view class="qa_title">FAQ</view>
        <view class="qa_item">
          Q：AI 面试结果会直接影响企业对我的评价吗？
        </view>
        <view class="qa_item">
          A：不会，结果仅用于更精准地匹配岗位，企业不会直接看到你的AI面试过程。
        </view>
        <view class="qa_item">Q：我需要特别准备什么吗？</view>
        <view class="qa_item">
          A：不需要特别准备。保持自然、真实地表达自己，这样才能更准确地体现你的优势和特点。
        </view>
        <view class="qa_item">
          Q：AI 面试能帮助我更精准地找到适合的岗位吗？
        </view>
        <view class="qa_item">
          A：可以。通过 AI
          面试，系统能更全面、更深入地了解你的特质和需求，更有效地为你匹配适合的岗位。
        </view>
      </view>
      <view class="more mt-4" @click="openLog" v-if="matchResult.startTime">
        <text class="m_l">最近一次完成时间:{{ matchResult.startTime }}</text>
        <view class="m_r">
          <text>更多记录</text>
          <image class="right_icon" :src="setImg('images/right_icon.png')" />
        </view>
      </view>
      <button
        @click="handleStart"
        class="match-btn blue mid mt-4"
        :disabled="starting"
      >
        <text>开始面试</text>
        <text>
          本月次数：{{ matchResult.remainingTimes }}/{{
            matchResult.totalTimes
          }}
        </text>
      </button>
    </view>
    <EvaluateLogPopup ref="logRef" :show.sync="logPopupShow" type="AIVideo" />
  </u-popup>
</template>

<script>
import { checkLogin } from "@/utils/auth";
import EvaluateLogPopup from "./evaluateLogPopup.vue";
import { GET_RESULT_LASTEST, CHECK_INTERVIEW } from "@/api/resume.js";
export default {
  components: { EvaluateLogPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      logPopupShow: false,
      matchResult: {},
      starting: false,
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      GET_RESULT_LASTEST("AIVideo").then((res) => {
        if (this.qUtil.validResp(res) && res.code === 200) {
          this.matchResult = res?.data || {};
        }
      });
    },
    openLog() {
      this.$refs.logRef.getData();
      this.logPopupShow = true;
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    closePop() {
      this.$emit("update:show", false);
    },
    async handleStart() {
      if (!checkLogin(true)) return;
      this.starting = true;
      const res = await CHECK_INTERVIEW();
      this.starting = false;
      if (!this.qUtil.validResp(res)) {
        this.openModal({
          title: "提示",
          content: res.msg,
          icon: "none",
        });
        return;
      }
      uni.navigateTo({
        url: "/pages_ai/ai/index",
      });
      // uni.showModal({
      //   showCancel: false,
      //   title: "提醒",
      //   content: "功能正在开发中,敬请期待!",
      // });
    },
    joinWaitlist() {
      uni.navigateTo({
        url: "/pages_user/resume/waitlist",
      });
    },
  },
};
</script>

<style lang="scss"></style>
