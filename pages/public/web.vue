<template>
  <web-view
    :src="src"
    @message="messageHandle"
    @onPostMessage="messageHandle"
  ></web-view>
</template>

<script>
import storageUtil from "@/utils/storageUtil";

export default {
  data() {
    return {
      src: "",
      delta: 1,
    };
  },
  onUnload() {},
  methods: {
    messageHandle(e) {
      console.log("messageHandle:", e, e.detail.data);
      // 目前就只有测评才用到该方法,所以先不放在判断里面,后续有其他页面时在添加
      const pageType = "evaluation";
      // const pageType = e.detail?.data?.pageType || '';
      if (pageType == "evaluation") {
        storageUtil.setItem("evaluationFinish", true);
      }
    },
  },
  onLoad(options) {
    if (options.url) {
      this.src = decodeURIComponent(options.url);
    }
  },
};
</script>

<style></style>
