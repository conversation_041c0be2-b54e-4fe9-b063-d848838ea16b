<template>
  <view class="container page-login">
    <u-navbar leftText="授权登录" @leftClick="backPage" fixed></u-navbar>
    <view class="login-main">
      <image src="/static/logo.png" style="width: 328rpx" mode="widthFix">
      </image>
      <view class="slogan">让校园求职更简单、更高效</view>
      <button @click="showAgreeTip" class="confirm-btn" v-if="!isAgree">
        微信授权登录
      </button>
      <button
        @getphonenumber="getPhoneNumber"
        open-type="getPhoneNumber"
        class="confirm-btn"
        v-else
      >
        微信授权登录
      </button>
      <view class="agree" @click="isAgree = !isAgree">
        <view class="checkbox" :class="{ checked: isAgree }">
          <text class="iconfont icon-check"></text>
        </view>
        <view class="agree-text">
          <text>我已阅读并了解</text>
          <text class="link" @click.stop="gotoProtocol('protocol')">
            《用户协议》
          </text>
          <text>和</text>
          <text class="link" @click.stop="gotoProtocol('privacy')">
            《隐私协议》
          </text>
        </view>
      </view>
    </view>
    <Modal />
    <InvitPopup
      :show.sync="invitePopupShow"
      @success="handleInviteSuccess"
      @cancel="handleInviteCancel"
    ></InvitPopup>
  </view>
</template>

<script>
import oauth from "@/mixins/oauth";
import { checkLogin } from "@/utils/auth";
import Modal from "@/components/modal.vue";
import InvitPopup from "../components/invitPopup.vue";
export default {
  components: { Modal, InvitPopup },
  mixins: [oauth],
  data() {
    return {
      isAgree: false,
    };
  },
  onShow() {
    this.loadCode();
  },
  onLoad() {
    if (checkLogin(false)) {
      this.navBack();
    }
  },
  methods: {
    showAgreeTip() {
      uni.showToast({
        title: "请先勾选协议",
        icon: "none",
      });
    },
    backPage() {
      uni.navigateBack();
    },
    handleInviteSuccess() {
      this.authSuccess(false);
    },
    handleInviteCancel() {
      this.authSuccess(false);
    },

    gotoProtocol(type) {
      const temp =
        type === "protocol"
          ? "h5/legal/user_agreement_v1.0.0_20250628l.html"
          : "h5/legal/privacy_policy_v1.0.0_20250628.html";
      const url = encodeURIComponent(`${this.staticBaseUrl}/${temp}`);
      this.navigateUtil.goto(`/pages/public/web?url=${url}`);
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
  },
};
</script>

<style lang="scss" scoped>
.page-login {
  min-height: 100vh;
  background: #fff;
}

.login-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 128rpx 84rpx;
  .slogan {
    margin-top: 32rpx;
    margin-bottom: 120rpx;
    font-size: 28rpx;
    font-weight: 500;
  }
  .agree {
    display: flex;
    gap: 8rpx;
  }
  .agree-text {
    display: flex;
    flex-wrap: wrap;
    font-size: 24rpx;
    color: #4e5969;
    .link {
      color: $uv-primary;
    }
  }
  .checkbox {
    width: 32rpx;
    height: 32rpx;
    background: #f2f3f5;
    border-radius: 50%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 2rpx;
    box-sizing: border-box;
    .iconfont {
      font-size: 16rpx;
      color: #fff;
    }
    &.checked {
      background: $uv-primary;
    }
  }
}

.checkbox-text {
  padding-top: 50rpx;
  text-align: left;
  border-top: 1rpx #f4f4f4 solid;
  font-size: 26rpx;
  color: #cbcbcb;
  line-height: 2;

  .t {
    color: #666;
  }
}

.to-home {
  margin: 20rpx auto;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.confirm-btn {
  width: 560upx;
  height: 80upx;
  line-height: 80upx;
  border-radius: 50px;
  background: #18c2a5;
  color: #fff;
  font-size: 30rpx;
  text-align: center;
  margin: 0 auto 40rpx;

  &:after {
    border-radius: 100px;
    border: none;
  }

  &.is-plain {
    margin-top: 40upx;
    color: #666;
    background: rgba(51, 102, 204, 0.2);
  }
}
</style>
