<template>
  <view class="container matching">
    <u-navbar :leftText="title" fixed autoBack></u-navbar>
    <view class="matching_main">
      <view class="matching_main_title" v-if="status == 2">
        <text>快速匹配中…</text>
        <text>预计等待2-3分钟</text>
      </view>
      <view class="matching_main_title" v-else>
        <text>匹配完成</text>
      </view>
      <scroll-view
        id="scrollView"
        class="matching_main_list_view"
        scroll-y="true"
        :scroll-top="scrollTop"
        :scroll-with-animation="true"
      >
        <view class="matching_main_list">
          <view
            class="matching_main_list_item"
            v-for="(item, index) in chain"
            :key="index"
            v-if="item._visible"
          >
            <view class="s_r">
              <image class="index" :src="getStatusIcon(item.status)"></image>
              <view class="line" v-if="index != chain.length - 1"></view>
            </view>
            <view class="s_l">
              <view class="s_l_t">{{
                getDisplayText(item, "content", "")
              }}</view>
              <view
                class="s_l_sec"
                v-for="(l2, l2i) in item.children"
                :key="l2i"
                v-if="l2._visible"
              >
                <view class="s_l_sec_i"></view>
                <view class="s_l_sec_c">
                  <view class="s_l_sec_c_t">{{
                    getDisplayText(l2, "content", item.sn)
                  }}</view>
                  <view
                    class="s_l_sec_c_i"
                    v-for="(l3, l3i) in l2.children"
                    :key="l3i"
                    v-if="l3._visible"
                    >{{
                      getStatusText(l3) +
                      getDisplayText(l3, "content", item.sn + "_" + l2.sn)
                    }}</view
                  >
                  <view class="s_l_sec_c_i" v-if="l2.status === 2">...</view>
                </view>
              </view>
              <view class="s_l_sec" v-if="item.status === 2">
                <view class="s_l_sec_i"></view>
                <view class="s_l_sec_c">
                  <view class="s_l_sec_c_t">...</view>
                </view>
              </view>
            </view>
          </view>
          <view class="holder" :class="{ end: !blankShow }"></view>
          <!-- <view v-if="blankShow" class="matching_main_list_item">
            <view class="s_l">
              <view class="s_l_sec">
                <view class="s_l_sec_c">
                  <view
                    class="s_l_sec_c_i"
                    v-for="i in [0, 1, 2, 3, 4, 5]"
                    :key="i"
                  ></view>
                </view>
              </view>
            </view>
          </view>
          <view class="holder" v-else></view> -->
        </view>
      </scroll-view>
    </view>

    <view class="footspace" v-if="scene === 'QuickMatch'">
      <button
        class="btn primary big flex-1"
        :class="{ disabled: status == 2 }"
        :disabled="status === 2"
        @click="handleButtonClick"
      >
        {{ status === 2 ? "匹配中..." : "查看匹配结果" }}
      </button>
    </view>
    <view class="footspace" v-else>
      <button class="btn big flex-1" disabled v-if="status === 2">
        匹配中...
      </button>
      <template v-else>
        <button class="btn big secondary flex-1" @click="handleButtonClick">
          查看推荐岗位
        </button>
        <button class="btn big primary flex-1" @click="handleViewReport">
          查看个人求职报告
        </button>
      </template>
    </view>
  </view>
</template>

<script>
import { GET_RESULT_DATA } from "@/api/resume.js";
let endTimer = null;
export default {
  data() {
    return {
      matchId: 0,
      title: "快速匹配",
      chain: [],
      result: {},
      status: 0, //  2-匹配中 3-完成
      blankShow: true,
      scene: "QuickMatch", // 根据实际场景设置，例如 "QuickMatch" 或 "ExactMatch"
      scrollTop: 0,
      typewriterTexts: {}, // 存储打字机显示的文本
      typewriterTimer: null, // 当前打字机定时器
      currentTypingItem: null, // 当前正在打字的项目
      waitingItems: [], // 等待打字的项目队列
    };
  },
  async onLoad(options) {
    console.log(options);
    if (options && options.matchId) {
      this.scene = options.scene;
      this.matchId = options.matchId;
      this.loadData(this.matchId);
    }
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    getStatusIcon(status) {
      // 1-待处理 2-处理中 3-处理完成 4-处理失败
      switch (status) {
        // case 1:
        //   return this.setImg('/images/match/pending.png');
        // case 2:
        //   return this.setImg('/images/match/processing.png');
        // case 4:
        //   return this.setImg('/images/match/failed.png');
        case 3:
          return this.setImg("/images/match/complete.png");
        default:
          return this.setImg("/images/match/loading.gif");
      }
    },
    getStatusText(item) {
      // 如果项目正在打字机效果中，显示处理中状态
      if (item._isTyping) {
        return "◐ ";
      }
      // 1-待处理 2-处理中 3-处理失败 4-处理完成
      switch (item.status) {
        // case 1:
        //   return '○';
        case 2:
          return "◐ ";
        case 4:
          return "✗ ";
        case 3:
        default:
          return "✓ ";
      }
    },
    async loadData(id) {
      if (!id) {
        return;
      }
      let res = await GET_RESULT_DATA(this.matchId);
      if (this.qUtil.validResp(res)) {
        this.result = res.data;
        this.status = res.data.status;
        // this.chain = res.data.data?.chain||[];
      }
      this.$store.dispatch("initWS");
      uni.$on("message", this.dealStreamMsg);
      // 启动WebSocket消息发送和监听
      this.$store.getters.ws.sendMsg({ scene: this.scene, id: this.matchId });
    },
    // 递归更新children的方法
    updateChildrenRecursively(existingParent, newParent, parentPath = "") {
      if (!newParent.children || newParent.children.length === 0) {
        return;
      }

      if (!existingParent.children) {
        this.$set(existingParent, "children", []);
      }

      const currentPath = parentPath
        ? `${parentPath}_${existingParent.sn}`
        : existingParent.sn;

      newParent.children.forEach((newChild) => {
        const existingChildIndex = existingParent.children.findIndex(
          (child) => child.sn === newChild.sn
        );

        if (existingChildIndex !== -1) {
          // 更新现有的child
          const existingChild = existingParent.children[existingChildIndex];
          // 逐个字段更新，避免覆盖子集
          if (newChild.content !== undefined)
            existingChild.content = newChild.content;
          if (newChild.status !== undefined)
            existingChild.status = newChild.status;

          // 递归处理下一级children
          this.updateChildrenRecursively(existingChild, newChild, currentPath);
        } else {
          // 新的子项目直接添加到父项目的children中，但初始时隐藏内容
          const childWithTypewriter = {
            ...newChild,
            _isTyping: false,
            _visible: false,
          };
          existingParent.children.push(childWithTypewriter);

          // 添加到等待队列
          this.waitingItems.push({
            item: childWithTypewriter,
            field: "content",
            key: currentPath + "_" + childWithTypewriter.sn + "_content",
          });

          // 如果当前没有正在打字的项目，立即开始
          if (!this.currentTypingItem) {
            this.processNextItem();
          }

          // 递归处理新child的children
          this.updateChildrenRecursively(
            childWithTypewriter,
            newChild,
            currentPath
          );
        }
      });
    },

    dealStreamMsg(msg) {
      console.log("dealStreamMsg", msg);
      if (msg.type === "STEP") {
        const newStep = msg.step;
        const existingIndex = this.chain.findIndex(
          (item) => item.sn === newStep.sn
        );
        if (existingIndex !== -1) {
          // 如果sn相同，则更新content和status，children进行增量更新
          const existingStep = this.chain[existingIndex];
          if (newStep.content) existingStep.content = newStep.content;
          if (newStep.status) existingStep.status = newStep.status;

          // 使用递归方法更新children
          this.updateChildrenRecursively(existingStep, newStep);
        } else {
          // 新项目直接添加到chain，但初始时隐藏内容
          const stepWithTypewriter = {
            ...newStep,
            _isTyping: false,
            _visible: false,
          };
          this.chain.push(stepWithTypewriter);

          // 添加到等待队列
          this.waitingItems.push({
            item: stepWithTypewriter,
            field: "content",
            key: stepWithTypewriter.sn + "_content",
          });

          // 如果当前没有正在打字的项目，立即开始
          if (!this.currentTypingItem) {
            this.processNextItem();
          }
        }

        // 自动滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } else if (msg.type === "FINISH") {
        this.status = msg.success ? 3 : 4; // 假设3是成功，4是失败
        uni.$off("message", this.dealStreamMsg);
        this.$store.dispatch("closeSocket");
      }
    },
    // 生成唯一的key（包含父级路径）
    generateUniqueKey(item, field, parentPath = "") {
      const fullPath = parentPath ? `${parentPath}_${item.sn}` : item.sn;
      return `${fullPath}_${field}`;
    },

    // 获取显示文本（支持打字机效果）
    getDisplayText(item, field, parentPath = "") {
      // 如果项目不可见，返回空字符串
      if (!item._visible) {
        return "";
      }
      const key = this.generateUniqueKey(item, field, parentPath);
      // 如果正在打字，返回打字机文本
      if (item._isTyping && this.typewriterTexts[key] !== undefined) {
        return this.typewriterTexts[key];
      }
      // 否则返回完整文本
      return item[field] || "";
    },
    // 处理下一个等待的项目
    processNextItem() {
      if (this.waitingItems.length === 0) {
        this.currentTypingItem = null;
        return;
      }

      const nextItem = this.waitingItems.shift();
      this.currentTypingItem = nextItem;
      this.executeTypewriter(nextItem);
    },

    // 执行打字机效果
    executeTypewriter(typingItem) {
      if (endTimer) {
        clearTimeout(endTimer);
      }

      let l = this.waitingItems.length;
      //根据队列长度调整打字速度, 队列越长速度越快，2=50,10=10
      let speed = l >= 2 ? 10 : 30 - 10 * l;
      const { key, item, field } = typingItem;
      const fullText = item[field] || "";

      // 标记项目为可见和正在打字
      this.$set(item, "_visible", true);
      this.$set(item, "_isTyping", true);

      // 清除之前的定时器
      if (this.typewriterTimer) {
        clearInterval(this.typewriterTimer);
      }

      // 初始化显示文本
      this.$set(this.typewriterTexts, key, "");

      let currentIndex = 0;
      this.typewriterTimer = setInterval(() => {
        if (currentIndex < fullText.length) {
          this.$set(
            this.typewriterTexts,
            key,
            fullText.substring(0, currentIndex + 1)
          );
          currentIndex++;

          // 每次更新文本后滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        } else {
          // console.log("打字完成，清除定时器");
          // 打字完成，清除定时器
          clearInterval(this.typewriterTimer);
          this.typewriterTimer = null;
          // 标记打字完成
          this.$set(item, "_isTyping", false);

          // 处理队列中的下一个项目
          this.processNextItem();

          endTimer = setTimeout(() => {
            this.blankShow = false;
          }, 1500);
        }
      }, speed);
    },

    scrollToBottom() {
      const query = uni.createSelectorQuery();
      query.select("#scrollView").scrollOffset();
      // query.select(".matching_main_list").boundingClientRect();
      query.exec((res) => {
        this.scrollTop = res[0].scrollHeight + 200;
      });
    },

    handleButtonClick() {
      if (this.status === 3) {
        // 跳转到匹配结果页面
        uni.navigateTo({
          url:
            "/pages_user/job/matched?matchId=" +
            this.matchId +
            "&scene=" +
            this.scene,
        });
      }
    },
    handleViewReport() {
      if (this.status === 3) {
        uni.navigateTo({
          url: "/pages_user/job/jobMatch?matchId=" + this.matchId,
        });
      }
    },
  },
  beforeDestroy() {
    // 清理打字机相关数据
    this.waitingItems = [];
    this.currentTypingItem = null;
    if (this.typewriterTimer) {
      clearInterval(this.typewriterTimer);
      this.typewriterTimer = null;
    }

    // 在组件销毁前取消监听，防止内存泄漏
    uni.$off("message", this.dealStreamMsg);
    this.$store.dispatch("closeSocket");
  },
};
</script>

<style lang="scss" scoped>
.holder {
  height: 200rpx;
  transition: all 0.5s ease;
  &.end {
    height: 40rpx;
  }
}
.matching {
  height: 100vh;
  background-color: #fff;
  position: relative;
  color: #1d2129;
  &_main {
    padding: 30rpx;
    padding-bottom: 180rpx; // 为固定底部按钮留出空间
    &_title {
      font-size: 36rpx;
      font-weight: 500;
      margin-bottom: 30rpx;
      text:nth-child(2) {
        color: #86909c;
        font-size: 28rpx;
      }
    }
    &_list_view {
      height: calc(
        100vh - 400rpx - env(safe-area-inset-bottom)
      ); // 减去导航栏、标题和底部按钮的高度
    }
    &_list {
      display: flex;
      flex-direction: column;
      &_item {
        display: flex;
        align-items: flex-start;
        padding-top: 20rpx;
        position: relative;
        &:before {
          content: "";
          position: absolute;
          top: 64rpx;
          bottom: 0rpx;
          left: 20rpx;
          width: 1px;
          background: #e5e6eb;
        }
        &:last-child::before {
          content: none;
        }
        .s_r {
          padding-right: 20rpx;
          display: flex;
          flex-direction: column;
          align-items: center;

          .line {
            flex: 1;
            width: 2rpx;
            background-color: #e5e6eb;
          }

          .index {
            width: 40rpx;
            height: 40rpx;
            // font-size: 20rpx;
            // text-align: center;
            // line-height: 40rpx;
            // border-radius: 50%;
          }
        }
        .s_l {
          display: flex;
          flex-direction: column;
          color: #4e5969;
          font-family: "PingFang SC";
          font-size: 20rpx;
          font-weight: 400;
          &_t {
            color: #1d2129;
            font-size: 28rpx;
            font-weight: 500;
            margin-bottom: 20rpx;
          }
          &_sec {
            display: flex;
            align-items: flex-start;
            &_i {
              width: 8rpx;
              height: 8rpx;
              background: #18c2a5;
              border-radius: 50%;
              margin-right: 10rpx;
              margin-top: 10rpx;
            }
            &_c {
              flex: 1;
              color: #4e5969;
              &_t {
                font-size: 24rpx;
                margin-bottom: 20rpx;
              }
              &_i {
                display: flex;
                align-items: center;
                font-size: 20rpx;
                padding: 3rpx 8rpx;
                border-radius: 8rpx;
                background: #f2f3f5;
                margin-bottom: 16rpx;
              }
            }
          }
        }
      }
    }
  }
}

.footspace {
  height: auto;
  display: flex;
  align-items: center;
  gap: 40rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 32rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}
</style>
