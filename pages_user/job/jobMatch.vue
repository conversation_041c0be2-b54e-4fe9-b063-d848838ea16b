<template>
  <view class="job_match">
    <u-navbar leftText="精准匹配结果" @leftClick="backPage" fixed></u-navbar>
    <view class="job_match_top">
      <view class="job_match_top_box">
        <card-title size="sm">精准推荐报告</card-title>
        <image
          class="job_match_top_box_ripple"
          :src="setImg(`/images/match/ripple.png`)"
        ></image>
        <view class="job_match_top_box_content">
          <text class="job_match_top_box_content_title">
            {{ reportData.name || "--" }}
          </text>
          <view class="job_match_top_box_content_item">
            <view class="job_match_top_box_content_item_title">
              报告撰写:
            </view>
            <image
              class="job_match_top_box_content_item_img"
              :src="
                reportData.writerAvatar
                  ? reportData.writerAvatar
                  : setImg(`/images/match/person.png`)
              "
            ></image>
            <view class="job_match_top_box_content_item_text">
              {{ reportData.writer || "--" }}
            </view>
          </view>
          <view class="job_match_top_box_content_item" v-if="reportData.expert">
            <view class="job_match_top_box_content_item_title">
              复核专家:
            </view>
            <image
              class="job_match_top_box_content_item_img"
              :src="
                reportData.expertAvatar
                  ? reportData.expertAvatar
                  : setImg(`/images/match/person.png`)
              "
            ></image>
            <view class="job_match_top_box_content_item_text">
              {{ reportData.expert || "--" }}
            </view>
          </view>
          <view class="job_match_top_box_content_item">
            <view class="job_match_top_box_content_item_title">
              撰写时间:
            </view>
            <view class="job_match_top_box_content_item_text">
              {{ reportData.writeTime || "--" }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="job_match_list">
      <image
        class="job_match_list_img"
        :src="setImg(`/images/match/BG.png`)"
      ></image>
      <!-- 个人核心竞争力评估 -->
      <view
        class="job_match_list_item"
        v-for="(item, index) in reportData.items"
        :key="index"
      >
        <view class="job_match_list_item_title">
          <text class="job_match_list_item_title_text">
            {{ item.title || "--" }}
          </text>
        </view>
        <view class="job_match_list_item_content">
          <view class="job_match_list_item_content_box">
            <image
              class="job_match_list_item_content_box_img"
              :src="setImg(`/images/match/ripple.png`)"
            ></image>
            <!-- <zero-markdown-view
              type="optimize"
              :markdown="item.content"
              :customTagStyle="customTagStyle"
            ></zero-markdown-view> -->
            <!-- 基本情况概述 -->
            <!-- {{ JSON.stringify(item.items) }} -->
            <report-item
              v-for="(cItem, cIndex) in item.items"
              :node="cItem"
              :key="cIndex"
            />
            <!-- <view
              class="job_match_list_item_content_box_basic"
              v-for="(cItem, cIndex) in item.items"
              :key="item.title"
            >
              <text class="job_match_list_item_content_box_basic_title">
                {{ cItem.content }}
              </text>
              <view class="job_match_list_item_content_box_basic_context">
                李会晨，北京交通大学交通工程专业本科在读（2021-2024），现阶段在北京建筑大学继续深造（2024-2025）。结合提供的信息与假设的面试和测评结果，你在专业基础、实践经历、团队合作、软件使用等方面，具备明确的竞争优势，具体体现为：
              </view>
              <view class="job_match_list_item_content_box_basic_info">
                <view class="job_match_list_item_content_box_basic_info_title">
                  <view
                    class="job_match_list_item_content_box_basic_info_title_icon"
                  ></view>
                  <text
                    class="job_match_list_item_content_box_basic_info_title_text"
                  >
                    专业背景清晰扎实
                  </text>
                </view>
                <view class="job_match_list_item_content_box_basic_info_text">
                  交通工程属于典型工科专业，对实践和技术能力要求较高。从你的经历来看，核心课程（交通工程学、交通流理论、交通规划、道路工程）均有不错的掌握，项目参与也覆盖了路网分析、道路设计、干线优化、出行特征调查等多个维度，实践经验超出同届本科毕业生的平均水平。
                </view>
              </view>
              <view class="job_match_list_item_content_box_basic_info">
                <view class="job_match_list_item_content_box_basic_info_title">
                  <view
                    class="job_match_list_item_content_box_basic_info_title_icon"
                  ></view>
                  <text
                    class="job_match_list_item_content_box_basic_info_title_text"
                  >
                    项目经历较丰富、软件应用能力突出
                  </text>
                </view>
                <view class="job_match_list_item_content_box_basic_info_text">
                  你参与的交通认知实习、道路工程设计、干线优化和生产实习等项目都体现出扎实的动手能力，尤其在CAD制图、交通软件（如Synchro）使用方面积累了一定优势，实际动手能力是你的核心竞争力之一。
                </view>
              </view>
            </view> -->
            <!-- 核心竞争力小结 -->
            <!-- <view class="job_match_list_item_content_box_basic">
              <text class="job_match_list_item_content_box_basic_title"
                >核心竞争力小结</text
              >
              <view class="job_match_list_item_content_box_basic_context"
                >你是典型的工程类应届毕业生，专业技术扎实、软件技能熟练，项目经验丰富，且有明显的团队协作和组织能力。总体具备较强的就业竞争力。</view
              >
              <view class="job_match_list_item_content_box_basic_section">
                <view
                  class="job_match_list_item_content_box_basic_section_icon"
                ></view>
                <text class="job_match_list_item_content_box_basic_section_text"
                  >提升建议</text
                >
              </view>
              <view class="job_match_list_item_content_box_basic_info">
                <view class="job_match_list_item_content_box_basic_info_title">
                  <view
                    class="job_match_list_item_content_box_basic_info_title_icon"
                  ></view>
                  <text
                    class="job_match_list_item_content_box_basic_info_title_text"
                  >
                    专业深度提升
                  </text>
                </view>
                <view class="job_match_list_item_content_box_basic_info_text">
                  你现在的项目经验多，但稍显分散，建议下一步可选一个领域（如智能交通、城市交通规划）进行深度的实践或研究，以便在面试时形成更明显的专业特色。
                </view>
              </view>
              <view class="job_match_list_item_content_box_basic_info">
                <view class="job_match_list_item_content_box_basic_info_title">
                  <view
                    class="job_match_list_item_content_box_basic_info_title_icon"
                  ></view>
                  <text
                    class="job_match_list_item_content_box_basic_info_title_text"
                  >
                    行业前沿认知提升
                  </text>
                </view>
                <view class="job_match_list_item_content_box_basic_info_text">
                  交通工程领域近年数字化转型明显，智慧交通、大数据分析等技能需求不断攀升。建议多关注智能交通、数字孪生技术应用、智慧城市建设等领域的前沿趋势。
                </view>
              </view>
            </view> -->
          </view>
        </view>
      </view>
      <!-- 推荐职位列表 -->
      <view class="job_match_list_item">
        <view class="job_match_list_item_title">
          <text class="job_match_list_item_title_text"> 推荐职位列表 </text>
        </view>
        <view class="job_match_list_item_content">
          <view class="job_match_list_item_content_box">
            <image
              class="job_match_list_item_content_box_img"
              :src="setImg(`/images/match/ripple.png`)"
            ></image>
            <!-- 岗位 -->
            <view
              class="job_match_list_item_content_box_basic"
              v-for="item in jobList"
              :key="item.title"
            >
              <text
                class="job_match_list_item_content_box_basic_title o6"
                :class="{
                  o6: item.recommendType === 2,
                  o7: item.recommendType === 4,
                  o8: item.recommendType === 6,
                }"
              >
                {{ item.title }}
              </text>
              <view class="job_match_list_item_content_box_basic_compay">
                <JobCard
                  v-for="item in item.list"
                  :item="item"
                  inReport
                  scene="ExactMatch"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="footbar">
      <button
        class="btn secondary big flex-1"
        @click="handelReview"
        v-if="reportData.reviewStatus === 1"
      >
        申请专家复核
      </button>
      <button class="btn secondary big flex-1" disabled v-else>
        {{ reportData.reviewStatus === 2 ? "复核中" : "已复核" }}
      </button>
      <navigator
        class="btn primary big flex-1"
        :url="`/pages_user/job/matched?matchId=${matchId}&scene=ExactMatch`"
      >
        更多岗位
      </navigator>
    </view>
  </view>
</template>
<script>
import { GET_REPORT, REVIEW_REPORT } from "@/api/resume.js";
import MatchItemCard from "@/pages_user/resume/components/matchItemCard.vue";
import CardTitle from "@/components/cardTitle.vue";
import JobCard from "@/components/jobCard.vue";
import ReportItem from "../components/reportItemA.vue";
export default {
  components: { MatchItemCard, CardTitle, JobCard, ReportItem },
  data() {
    return {
      matchId: "",
      reportData: {},
      customTagStyle: {},
    };
  },
  computed: {
    jobList() {
      const jobs = this.reportData.jobs || [];
      return [
        {
          title: "冲刺类岗位（挑战高、成长快）",
          recommendType: 2,
          list: jobs.filter((item) => item.recommendType === 2),
        },
        {
          title: "稳健类岗位（能力契合、机会稳健）",
          recommendType: 4,
          list: jobs.filter((item) => item.recommendType === 4),
        },
        {
          title: "保底类岗位（保障就业、安全选择）",
          recommendType: 6,
          list: jobs.filter((item) => item.recommendType === 6),
        },
      ];
    },
  },
  onLoad(opt) {
    if (opt.matchId) {
      this.setStyle();
      this.matchId = opt.matchId;
      this.getData(opt.matchId);
    }
  },
  methods: {
    setStyle() {
      this.customTagStyle = {
        h1: `
          color: #1D2129;
          font-size: 40rpx;
          font-weight: bold;
          `,
        h2: `
          color: #1D2129;
          font-size: 36rpx;
          font-weight: bold;
          `,
        h3: `
          color: #1D2129;
          font-size: 28rpx;
          font-weight: bold;
          `,
        p: `color: #1D2129; font-size: 28rpx; font-weight: 500; line-height: 1.4;`,
        li: `
          color: #1D2129;
          font-size: 28rpx;
          line-height: 1.4;
        `,
      };
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    async getData(id) {
      const result = await GET_REPORT(id);
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.reportData = result.data;
      }
    },
    backPage() {
      uni.navigateBack({
        delta: 1,
      });
    },
    async handelReview() {
      try {
        const res = await REVIEW_REPORT(this.matchId);
        if (this.qUtil.validResp(res)) {
          uni.showToast({
            title: "申请成功！",
            icon: "success",
          });
        }
      } catch (error) {}
    },
    collectionStatus(el) {
      this.jobData.map((item) =>
        item.id == el.id ? (item.collection = !item.collection) : item
      );
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .report-item {
  width: 100%;
  margin-bottom: 30rpx;
  &.in-children {
    margin-bottom: 0rpx;
  }
  &_h3 {
    padding-bottom: 10rpx;
    color: #1d2129;
    font-size: 32rpx;
    font-weight: bold;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUwSURBVHgB7ZzrbeNGEMdnlqS/xECYCswOzFRgpYRUcEkFd0B8QL6d8y2AA8SuIE4JqSB0B3IFR1cQHnC5A0Ttzs0sH3pYkk+yKPGxP8CmQNEPkvvf/+zMiAAOh2MvIDg6DdFVCJCFMPUjTRjJPiSKQKlvgXSIpELewcdgSADh3E9G638rpvY7kN2SbAkfCTH10L7O8OR6DI4FnJhaDNGbCPIg1AAxGhaEp86sQEDFhTA2CaJ5EHDMSksNwj9e4CWIv6cwYJyYjogVS+ko1k2QzniARsRfxxbKLoi4DMCtf3J9BwPEialhrGD0SWymJhJnYYeJCCHmdyTkCqGH8KBKMDj9EfEqgwHhg+PF2HVL/n9kwzHE8znBRCaXI3i+VnKg4eUG9B4OQUd8Pf7ll9/DgHDOtAULLqPovFi72HCslw7zUjhc/XlIIZ9zphWsEw27TFi7jOS6ZA52rEUBveLNHQyEQYvJiaZZ+KrF0EOWw3rlq1vJZA5CTNXJG0MjmwQgI6nl2InGsYkq22o0TwpzySOTf4zkfSwOAp3TA7+6652YarfRJuZU7fn8yddJAHAcAh5srS/sVkVvnQcjW8t7EqHAs8kjLB24s2J6kkFbdhuEATkNpuyrGd/tlNDI9lH2SsdCsaWUCLMgyGepau3FZPC1zbw1hAH8G1rCuhJFMdH6Ni7ZNUIhU9QEOyEmK5zpx9jaLc8cfNajZavtqWx48GOGBONKJFYgymSep3nWD7NtazmUX44M4V9NF4VZSLfBATN5dVvV6ppeoyUKVHhut9Ay7EXRn0ZyQdhxLqqLAb2FXYXFohEelWEHUZjxLR8HwTfpPoue5YT0zhC8gYYphbT3v7O6Y6QdrVUqOP3uqM60Sjjz65ue+E3tLiIYu44onQXxJoUDQVxEbTq7Jp0PfI6/BcF1Alsy7yya1y6r2qsKdykCscIGWpQ04iXHwcTUf+EsOYxP6aEFsw4O7f40RE0JiScKTLSi2yD4I1l+c7mrfbFhtxZKyGMhLFYdptIJlLIB6MDYsGt3aID5NU7/QjVZ1JvEgPogLtNESLZP6POvkfGm76EBENmJCB8UcfKDXcTuA4xmH/foc3i+DN6+2Jk2JQe67TiFaOrP8QR50gaX2Rajpj9BQ3DeZ6SAa3czK+mMk+wbyehtLSZZBNqcvE1HkwinCB/KtGL3LuQsPBOn6apo1sEJDblP4Ng7mYwXDezMEtZ7MN4opnqdQ3AxX8exkWz30tH1yXchPNsbhj64duaXsDjZbho3C2KiydvYttwsh2vQNb8pQzSDD21KBBwDHgApOJ5DJtqU7WFchfW7TLZWTGZyadOmRj6w1q1wzV4Edpt7sVqtMBmE22yBUZAognfgqAXTVPLIBgB68vZ9BzIvC2Fa39Y2TZJPfrlRgK+h/6xxmDw7xFixzkRE95zmjKA9FLULiVOVSYYcpu0DP9BXlPsXPfhIxEIBvKrnGaPSNkQkVkzyz7FHvYKjUCzwRNBDX980BV/PjLOwP5iJf4NHu89fQ9GwW4VibRPLcxR9og0W9pZwjnNk5F5rNb2SYvrhQvunXe1Vw24hlMOEYU1TJ0315PI/2O+zDGZrHCecVjLh7K1SJkKNsUEMPYKz4oGVM6Tdp3oY5WwnZlYUIM0/7CDSAUGQSZPuvEB26WrvMrWYyozeCHajzqq55IBjqNR1Jq7B3rO0Rl/3Y7M6jqSjT9yjch2OmZg21COKcE3Exsf4/unY1XEcjqfUYvL96djkPpTrHBeuORxb8gWdL6zRbAEQoAAAAABJRU5ErkJggg==)
      no-repeat;
    background-size: 80% auto;
    background-position: left bottom;
    &.o6 {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUJSURBVHgB7ZxfcuNEEMa/kbwUb5gTrG4Qc4KYR4qCqj0BcILlACwRxb6zOQHhBBQEikeUGyQnWO0J1vu2tZHU292yZMmWk9jxyPozv6rYjvyvPJ5vvp7ulgGHw3EQDBy9hv4Mp/gUUyQIQGmgB40JYOgzwJuCsikf4T+a8vHp6okItr6oQbx8TH5tvBhEb/gvhvFjeFiYr8NrOGo4MXUY+i8MkLEQ0nTGE1iE8DQXCP8vwrhLEK1ALCjDQsNfeOJH5qswxohxYjoiKpbCUdRNzFN2Er6WY8cWyj6wuMicm29/vcAIcWKyjArmNmFnMQHEWUQkBjO+XoZfgyRC4j8zz8IFRsQEjkej+5ZPWCR5OHZSEQw7Typ7GNl/VJ6AoTPHJPmfr7/AiHDOtAN1l/FOdO8CuT1Yh3kchB/GFPI5Z2pgq2iSdLpymQxuLboHg+/48gIjYdRicqKxzgwDZCOs9/1zyWSOQkzlh6d0jjwJIF/yzInGcRdlttXIIltNHqVBOV1k7mTpDV9eDE5MpdsYjweATmof3tE2nS/slkXvW15otZa3FqHog6pPaHiRjNSBeyumhgxa3W2KTz38zFnesUCG09DSoeAttFtBkI4FvZ+vvclCOhfK59zyWJn0OSTzZgvCH+gI20sUqbjPymX2iVBIX7MfYlLhTPTLn+nKgWy+YbWDhAViiMXBK3whEhUIH3syucZ7LHat5dBlOOdx/F0Lw1YhLt6+vEBLrBymsaZnt0Qhizk6uEnQQfGTuQ4I4bQcjKGiriLhEAvFmJgXiAVvaK/xAfEhi575uNIZTPYjrMNC+ublwd+nuWOkI61Vif/5UZ2pWTjSrFlZQXrvOhV3EcGQuS6cpdVeNi2iGtvZtYg/2y8spAg7UnMWmGlje5W4i6AWQBLGojZXjglvOVoT0+CFs+EwHI61LZgt0N9nv3FobElIuleLlm4Ubbz3eld7rWG3FAofS6f53qUIlgqxoCfzIp1ZCfNqe5yhhWp6eoIX8cR4py5jISQ7JPxdBJikr2EDw05ExGlh3s+Ji+gbskCK0z2GHJ5vQOePdqatyQG9s3gQ+kcpmuWmv6+nGEyy72ELkkygmddcROlI6NUmnNHbWUx5ilFz8pyOprnYW36HXPSwmFMNz8RphnZejse1Nldjs4CGtzJvbjSs59t3iqmyzzmt13GwFE+fOgYqH74H4dnBSM07ToDAsSfri+0d86YmJvo3nOUtN9VwrYeWXYRoyG66lAg4CiaLXZvUfchCy3PGsFiKsH6PxVbFRJcv5NyTGbJl+0Rv4oLlICC7Uqs1fjQKt9mNiP/OMHqqgrGTPMoz9v+8eN39zMtamOZ+c+DB0OVPr3j8nmPwbHEY+QGYFuZKHuZlhld2CtAZytoFDwivrGMO0w5BMgk5q3fK42m7aGuZtQJ4Uc/zJ3EXIpJcTJ78EIaeyNU+xQbP4Gr0+xtLyCTjZNKXnEx6xRPwON/zQygadotQrGNiuY88zLNZ2Ku/nXOcI6PftZ+E8DzO0LYUjTR1tRcNuyKUlsIw25RpHrr8+a1Wrg/30pU9jhNOF9HsbZoE+blf8mOV2sVQnwN5u0+89tSFikJfhB1EOyBYGNKkWxHIPl3tfaYiJs3ozbEXlayaSw44RkqlzkRXeWvIA6jWcTgd7X4q1+GoF20jNNYjynDtSh+TcLjm6jgOxwYrMbFIOAnBN+R8GxeuORy78hHiMvq8JqtKlQAAAABJRU5ErkJggg==);
      background-size: 60% auto;
    }
    &.o7 {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUrSURBVHgB7ZxdbttGFIXPUGJRFAWqrqDcgZjHogmsLMEPDdCntCtId5B0BU1WUPepgPNgdAVWkBR9LL2C0iuIgvwgMCVOzh1yGEqRFEsWJf7MB9ikCZk2h3Pm3Ll3SMDhcOwEBUejOftPD/Aeg+kUgdYI5JhWCLwevlEagxQYyFZzi+zLEqw5bSzflN0qxLMUlzxPLPvaw+TeDyqCYw4nphpz9q8OkpQCmCFUHrcevjMC0QiRCSPAYYnYg2Ju//Y9jI+/VzE6jBPTARGxWEcRN+lRLGY/E0mA5iHiekLXOkEHcWKqGBHMVYLQ8yiOXCwQZ6HDsPUHaCHsVON+guPju2qCDtGH48bIvCV5R5FIONbHsBAM3SWZsXN5nMfIB9PSL7V4GOO1jhIf59y9hQ7hnGkDyi7DifgwzRwmaKvD3BiFX7oU8jlnWsIq0dBlBtZltM4/7IajlTBZcp+bE3SETovJiaZa2HQhWshiWD9l0uUnZjI7ISZ78RwpR5IE4NwlpEhCJxrHOmy2lX0iLCePkrd5plX6DufBPYUL/nTSOjFZt5EGoFCG5YufSwI40VSOklR5zbFF7yTBSGp5ixFK0U/SNSdJMwdurJgWrXbRbQwaXSHmPZ+wVhXz0ieyWkEOyooFs81XLbCwWqSqZxxweOyBZN5QEfx//kRNWFWimHMZbBmhqOwcjRCTCGf6JnebbOQYLVqtoW1uo9n5lfmKrEhEILLf8znqf4nJ8a3NajlPn+sRO/kf8CouCmsWb2/vL5NnHWZpTW9diWI3DOVb7bqfNMrVa4ykQXjTj2xjoL3EIhZujVA4MEzQQ+R/hXhToazDDEhv8ZCd6VdUjQjpjtr531m2YqQuS6v8BN8e1JmWCUccpxhB2hCmldyFP10yLI2ss+xzLRvbVYqolWbXZOUDr/e3H++oMTak7Cw8z2DZ8ipxFzP8q2yT1qh/JF8g2JuYOiCcOYdRKeJ9C2YVT1/o3ytLU8tg4WHM632yTESLq9rLC3YLoXCffcEUvu18t25iuQZhJWFeeY7TwlAt9hTGDLtfictUEZLtErPyfIb/UQ1j3uMLCU3FReRALhD7uEeArsDQ9sbOtDI5oIo/0lSMaOyk3/eb+YjB1RQ/q+pmxiPe35Gcv2EusnvUFmGeGekkJy8LOlMjnNAKRzezQePyfKapolkFHWPY+Y5eBQxvOYhEjLwuTAlCI1orpmKe02eoVqrjzKWjm0Lp4psQnu2KmcYrV5++EXOD7bp+Myem0390qDLbLsI11cw6jgnRKP6LOiUCDoI2ncGxjmyglUfyIxvWbzPYGjEx23Ou8wfW5qrAdSdvBLrNM1OjoYC64DabwLFwzNv5EF2nJJiqkkdmzDp9oSXbE6DOLIRpbZvbVMnpc/2Yd/oB2s4qh/Ew2Udf6Wf/A0f2Ookpr11w79KbYdzpMG0H+FM8mvo4avwjEQsFcFvPS3vMttYgIjFiYigQsaHv4zCYCR4b5lnn5zcVIe9iODvXd698PFaHu8/XwSzYtaFY3cTyOUyY9xfT3b3qCnsfcY5zcORes+0f8cYfYX/RyCer2u2CXSOUPYVhVVPkeThvegns8F0GpTmOE049kewt700wU/l7+ZAt8yl/Rpb72JdRFscYaokQZF8cRFZAMHs6MduSQLZZ1d5kCjGZjN62z7aUsmouOeDoKkWdKZU5i7q2mIo6jqSj7912r8p1OAoxraxH5OGaiE0+0/+a4Zqr4zgcn1CIqT9FlPjZc/suXHM4NucD8McNBnhzCd0AAAAASUVORK5CYII=);
      background-size: 60% auto;
    }
    &.o8 {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUFSURBVHgB7ZxdcuNEFIVPd8S8YlYQ78BmBTFLSDE8DzxSMAysYIYVDCZQPE54B1ysIMoOlBWMswI0xUBNTSz13NvtttqynMSOJOunvyqXHJXtSK0+fW7f2xLg8XhKQcDTal6p2eAdMFgAQ4V0yPvoog7p9TEgB7RvQHvoldJ73hr4M9t+UwFz8xmx2tLvXPN+ATmXQPyNOI3gWcOLqcH8pmbDlISSIB3TnwPqxMdGIMmYhXGbIOpBkaBYaPj7I8jwa3E6R4/xYjogLBbrKMZN5LGCGvLr8ELZBxWRyKffic/P0UO8mCrGCEY7yZCdRWnxYEwNzyHXAJ1EhAuI0x/EaYweEcDzYHje8j+JhMMxEszIEQwJiQI1nnVAz0U03R/B1CRAekFvPkWP8M60A+suI0c8d6EmHKKzDvMwaPD4qk8hn3emAraJZmEyY0uXMY7j2Q611BPanKMn9FpMXjRVo8boIPmw/gZySvPDeS/E5Jz8xCQBxJgvtBeN5zacbOvYTR69zep5eh78COkVbc47JybrNtCC0cmAjZPPUgGeKqFhqvGFXVv0vqGBFrqWl49QcGfyKF06cGvFtJlBW3cbSx+8RugVCyIWULSVcYr0mvdnKxnSuUQQ88oF+x0acbm9nnHmDRVBnex3NIRtJQp3oGX2iVDUsibYCjGxcN7SiRu7lSPqLJM+uA2dkRYAnWNkRWKW9Kg4wFFEI2q8ay3nTM0mJK5XqLworKh4+/gcNZEtqyqs6VVaopAQozJ/rzSMcJIJNwgd3IltDHQUocWBiEZxEopeCxcfQUbvaX+ZRU9u13+B5ySk71E5avpUPC79/xSvGGnG0qoF5CcHdaYi4RjHESuVtz1Mc91lKZjIOkuda9n+Q3IhzDyyMuj3Q+rcPz4VX4TYkZyzDIqWVxl32QzJmtBHHtHx1Samrgsn7zA8f6lbMNv4Wc1eKrNYtnSUdlKEFIJOvy0QUcGqdmfB7koo1DfSZeFb5MTSln6Rjis5TneO07VQzYhGUudJ3rDLVBGSlYkJjdLXqAQRQqeFOfkhj3kPi8Pe7tHl8HwTNX2wM92VHHC3bSITjZn0t/UWAxLSl6gMzgSKiX63dBFDM0KvOlH7hHk80t2Y4ieloxULR4cPtvDZtkbMhWdRB+/LGcFTOmYuLCLq81fLsD66VUx2nkPjzIlbx8nSi+2RTu7kGx+elYd444vU+5MfbG/rN2ti+lXNxmbJjRuuidbVcWyIRudy1aREwGFI5lwJ8WxnOdDOecWGDev3GWy1mM7UXxfsOunKddoRrtlGoOO9ZKulBgj74TY7EdLrOXrOumCqSR5pzfyi/nytGp55yYdp/pkD9+dM/fETXepn6DjbHIbrfHX0lcAchLiETmk2g6x2oa7pz7DfYdrDWeDoRQB10vZbIgoK4LqeJ3E0b0JEosUk9IMw8AQHwE7wEuDSz2+qgTvZSzX7LEDKDnWQ63wfsgW7q1CsUWK5Cx3mUUNTjryqwl6Gd5zDY6518oI66kldRdWiVe12wS4Lpa4wrGpWeQZKQvxD3b20Zxm4cxx44TQSzt6SAwzNvV9qQNfr2H1QpYHDf/MwSguLgEXB79lBWCjK3N4RuwLZZ1V7m3HFdLHvvS25rJpPDnh6iVNnSigJISf3+ZJbx+F0tH9UrsezXrQNUVCPyMI1FhvCBYLI13E8nk1WYmKRBLpYq+c5PlzzeHbkA8HGHKsPXHx9AAAAAElFTkSuQmCC);
      background-size: 60% auto;
    }
  }
  &_p {
    margin-top: 30rpx;
    color: #1d2129;
    font-size: 28rpx;
    &.in-children {
      font-weight: normal;
      color: #4e5969;
      font-size: 24rpx;
      line-height: 1.2;
    }
  }
  &_h4 {
    margin-top: 32rpx;
    display: flex;
    align-items: center;
    color: #1d2129;
    font-size: 28rpx;
    font-weight: 500;
    &_icon {
      width: 8rpx;
      height: 32rpx;
      border-radius: 84rpx;
      margin-right: 10rpx;
      background: #18c2a5;
    }
  }
  &_info {
    margin-top: 20rpx;
    display: flex;
    padding: 20rpx;
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
    align-self: stretch;
    border-radius: 32rpx;
    background: #f7f8fa;
    &_title {
      display: flex;
      align-items: flex-start;
      color: #1d2129;
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-weight: 500;
      &_icon {
        width: 16rpx;
        height: 16rpx;
        border-radius: 4rpx;
        background: #18c2a5;
        margin-right: 10rpx;
        margin-top: 8rpx;
        flex-shrink: 0;
      }
    }
    &_text {
      color: #4e5969;
      font-size: 24rpx;
    }
  }
}
.job_match {
  min-height: 100vh;
  background-color: #e6f0e2;
  padding-bottom: 120rpx;
  &_top {
    width: 100%;
    padding-left: 30rpx;
    padding-right: 30rpx;
    padding-top: 60rpx;
    box-sizing: border-box;
    background: linear-gradient(
      133deg,
      #51ffeb 2.93%,
      #fffa72 49.58%,
      #94ffc1 94.48%
    );
    &_box {
      width: 98%;
      align-self: stretch;
      border-radius: 32rpx;
      border: 2rpx solid #fff;
      background: linear-gradient(
        134deg,
        rgba(255, 255, 255, 0.83) 44.77%,
        #ceffe6 86.11%
      );
      box-shadow: 0px 4px 20px 0px rgba(174, 174, 174, 0.1);
      box-sizing: border-box;
      position: relative;

      &_ripple {
        width: 420rpx;
        height: 396rpx;
        position: absolute;
        right: 0;
        top: 0;
      }
      &_content {
        display: flex;
        flex-direction: column;
        padding-top: 76rpx;
        padding-left: 32rpx;
        padding-right: 32rpx;
        padding-bottom: 44rpx;
        &_title {
          margin-bottom: 12rpx;
          color: #1d2129;
          font-family: FZLanTingHeiS-H-GB;
          font-size: 32rpx;
          font-weight: bold;
        }
        &_item {
          display: flex;
          align-items: center;
          margin-top: 20rpx;
          color: #1d2129;
          font-family: PingFang SC;
          font-size: 28rpx;
          &_img {
            width: 36rpx;
            height: 36rpx;
            margin: 0 8rpx;
          }
        }
      }
    }
  }
  &_list {
    width: 100%;
    padding: 30rpx;
    position: relative;
    box-sizing: border-box;
    &_img {
      width: 100%;
      height: 402rpx;
      position: absolute;
      top: -60rpx;
      left: 0;
    }
    &_item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30rpx;
      box-sizing: border-box;
      &_title {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 350rpx;
        height: 68rpx;
        background: url(data:image/png;base64,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)
          no-repeat;
        background-size: 100% 100%;
        z-index: 2;
        &_text {
          font-size: 28rpx;
          font-weight: bold;
          color: #fff;
        }
      }
      &_content {
        width: 100%;
        margin-top: 30rpx;
        padding: 10rpx;
        background: linear-gradient(
          133deg,
          #51ffeb 2.93%,
          #fffa72 49.58%,
          #94ffc1 94.48%
        );
        border-radius: 32rpx;
        box-sizing: border-box;
        z-index: 2;
        &_box {
          width: 100%;
          height: 100%;
          background: #fff;
          border-radius: 32rpx;
          position: relative;
          padding: 30rpx;
          box-sizing: border-box;
          text-align: justify;
          &_img {
            width: 420rpx;
            height: 396rpx;
            position: absolute;
            right: 0;
            top: 0;
          }
          &_desc {
            color: #1d2129;
            font-size: 28rpx;
            margin-bottom: 30rpx;
          }
          &_basic {
            position: relative;
            z-index: 2;
            width: 100%;
            margin-bottom: 30rpx;
            &_title {
              padding-bottom: 10rpx;
              color: #1d2129;
              font-size: 32rpx;
              font-weight: bold;
              background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUwSURBVHgB7ZzrbeNGEMdnlqS/xECYCswOzFRgpYRUcEkFd0B8QL6d8y2AA8SuIE4JqSB0B3IFR1cQHnC5A0Ttzs0sH3pYkk+yKPGxP8CmQNEPkvvf/+zMiAAOh2MvIDg6DdFVCJCFMPUjTRjJPiSKQKlvgXSIpELewcdgSADh3E9G638rpvY7kN2SbAkfCTH10L7O8OR6DI4FnJhaDNGbCPIg1AAxGhaEp86sQEDFhTA2CaJ5EHDMSksNwj9e4CWIv6cwYJyYjogVS+ko1k2QzniARsRfxxbKLoi4DMCtf3J9BwPEialhrGD0SWymJhJnYYeJCCHmdyTkCqGH8KBKMDj9EfEqgwHhg+PF2HVL/n9kwzHE8znBRCaXI3i+VnKg4eUG9B4OQUd8Pf7ll9/DgHDOtAULLqPovFi72HCslw7zUjhc/XlIIZ9zphWsEw27TFi7jOS6ZA52rEUBveLNHQyEQYvJiaZZ+KrF0EOWw3rlq1vJZA5CTNXJG0MjmwQgI6nl2InGsYkq22o0TwpzySOTf4zkfSwOAp3TA7+6652YarfRJuZU7fn8yddJAHAcAh5srS/sVkVvnQcjW8t7EqHAs8kjLB24s2J6kkFbdhuEATkNpuyrGd/tlNDI9lH2SsdCsaWUCLMgyGepau3FZPC1zbw1hAH8G1rCuhJFMdH6Ni7ZNUIhU9QEOyEmK5zpx9jaLc8cfNajZavtqWx48GOGBONKJFYgymSep3nWD7NtazmUX44M4V9NF4VZSLfBATN5dVvV6ppeoyUKVHhut9Ay7EXRn0ZyQdhxLqqLAb2FXYXFohEelWEHUZjxLR8HwTfpPoue5YT0zhC8gYYphbT3v7O6Y6QdrVUqOP3uqM60Sjjz65ue+E3tLiIYu44onQXxJoUDQVxEbTq7Jp0PfI6/BcF1Alsy7yya1y6r2qsKdykCscIGWpQ04iXHwcTUf+EsOYxP6aEFsw4O7f40RE0JiScKTLSi2yD4I1l+c7mrfbFhtxZKyGMhLFYdptIJlLIB6MDYsGt3aID5NU7/QjVZ1JvEgPogLtNESLZP6POvkfGm76EBENmJCB8UcfKDXcTuA4xmH/foc3i+DN6+2Jk2JQe67TiFaOrP8QR50gaX2Rajpj9BQ3DeZ6SAa3czK+mMk+wbyehtLSZZBNqcvE1HkwinCB/KtGL3LuQsPBOn6apo1sEJDblP4Ng7mYwXDezMEtZ7MN4opnqdQ3AxX8exkWz30tH1yXchPNsbhj64duaXsDjZbho3C2KiydvYttwsh2vQNb8pQzSDD21KBBwDHgApOJ5DJtqU7WFchfW7TLZWTGZyadOmRj6w1q1wzV4Edpt7sVqtMBmE22yBUZAognfgqAXTVPLIBgB68vZ9BzIvC2Fa39Y2TZJPfrlRgK+h/6xxmDw7xFixzkRE95zmjKA9FLULiVOVSYYcpu0DP9BXlPsXPfhIxEIBvKrnGaPSNkQkVkzyz7FHvYKjUCzwRNBDX980BV/PjLOwP5iJf4NHu89fQ9GwW4VibRPLcxR9og0W9pZwjnNk5F5rNb2SYvrhQvunXe1Vw24hlMOEYU1TJ0315PI/2O+zDGZrHCecVjLh7K1SJkKNsUEMPYKz4oGVM6Tdp3oY5WwnZlYUIM0/7CDSAUGQSZPuvEB26WrvMrWYyozeCHajzqq55IBjqNR1Jq7B3rO0Rl/3Y7M6jqSjT9yjch2OmZg21COKcE3Exsf4/unY1XEcjqfUYvL96djkPpTrHBeuORxb8gWdL6zRbAEQoAAAAABJRU5ErkJggg==)
                no-repeat;
              background-size: 80% auto;
              background-position: left bottom;
              &.o6 {
                background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUJSURBVHgB7ZxfcuNEEMa/kbwUb5gTrG4Qc4KYR4qCqj0BcILlACwRxb6zOQHhBBQEikeUGyQnWO0J1vu2tZHU292yZMmWk9jxyPozv6rYjvyvPJ5vvp7ulgGHw3EQDBy9hv4Mp/gUUyQIQGmgB40JYOgzwJuCsikf4T+a8vHp6okItr6oQbx8TH5tvBhEb/gvhvFjeFiYr8NrOGo4MXUY+i8MkLEQ0nTGE1iE8DQXCP8vwrhLEK1ALCjDQsNfeOJH5qswxohxYjoiKpbCUdRNzFN2Er6WY8cWyj6wuMicm29/vcAIcWKyjArmNmFnMQHEWUQkBjO+XoZfgyRC4j8zz8IFRsQEjkej+5ZPWCR5OHZSEQw7Typ7GNl/VJ6AoTPHJPmfr7/AiHDOtAN1l/FOdO8CuT1Yh3kchB/GFPI5Z2pgq2iSdLpymQxuLboHg+/48gIjYdRicqKxzgwDZCOs9/1zyWSOQkzlh6d0jjwJIF/yzInGcRdlttXIIltNHqVBOV1k7mTpDV9eDE5MpdsYjweATmof3tE2nS/slkXvW15otZa3FqHog6pPaHiRjNSBeyumhgxa3W2KTz38zFnesUCG09DSoeAttFtBkI4FvZ+vvclCOhfK59zyWJn0OSTzZgvCH+gI20sUqbjPymX2iVBIX7MfYlLhTPTLn+nKgWy+YbWDhAViiMXBK3whEhUIH3syucZ7LHat5dBlOOdx/F0Lw1YhLt6+vEBLrBymsaZnt0Qhizk6uEnQQfGTuQ4I4bQcjKGiriLhEAvFmJgXiAVvaK/xAfEhi575uNIZTPYjrMNC+ublwd+nuWOkI61Vif/5UZ2pWTjSrFlZQXrvOhV3EcGQuS6cpdVeNi2iGtvZtYg/2y8spAg7UnMWmGlje5W4i6AWQBLGojZXjglvOVoT0+CFs+EwHI61LZgt0N9nv3FobElIuleLlm4Ubbz3eld7rWG3FAofS6f53qUIlgqxoCfzIp1ZCfNqe5yhhWp6eoIX8cR4py5jISQ7JPxdBJikr2EDw05ExGlh3s+Ji+gbskCK0z2GHJ5vQOePdqatyQG9s3gQ+kcpmuWmv6+nGEyy72ELkkygmddcROlI6NUmnNHbWUx5ilFz8pyOprnYW36HXPSwmFMNz8RphnZejse1Nldjs4CGtzJvbjSs59t3iqmyzzmt13GwFE+fOgYqH74H4dnBSM07ToDAsSfri+0d86YmJvo3nOUtN9VwrYeWXYRoyG66lAg4CiaLXZvUfchCy3PGsFiKsH6PxVbFRJcv5NyTGbJl+0Rv4oLlICC7Uqs1fjQKt9mNiP/OMHqqgrGTPMoz9v+8eN39zMtamOZ+c+DB0OVPr3j8nmPwbHEY+QGYFuZKHuZlhld2CtAZytoFDwivrGMO0w5BMgk5q3fK42m7aGuZtQJ4Uc/zJ3EXIpJcTJ78EIaeyNU+xQbP4Gr0+xtLyCTjZNKXnEx6xRPwON/zQygadotQrGNiuY88zLNZ2Ku/nXOcI6PftZ+E8DzO0LYUjTR1tRcNuyKUlsIw25RpHrr8+a1Wrg/30pU9jhNOF9HsbZoE+blf8mOV2sVQnwN5u0+89tSFikJfhB1EOyBYGNKkWxHIPl3tfaYiJs3ozbEXlayaSw44RkqlzkRXeWvIA6jWcTgd7X4q1+GoF20jNNYjynDtSh+TcLjm6jgOxwYrMbFIOAnBN+R8GxeuORy78hHiMvq8JqtKlQAAAABJRU5ErkJggg==);
                background-size: 60% auto;
              }
              &.o7 {
                background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUrSURBVHgB7ZxdbttGFIXPUGJRFAWqrqDcgZjHogmsLMEPDdCntCtId5B0BU1WUPepgPNgdAVWkBR9LL2C0iuIgvwgMCVOzh1yGEqRFEsWJf7MB9ikCZk2h3Pm3Ll3SMDhcOwEBUejOftPD/Aeg+kUgdYI5JhWCLwevlEagxQYyFZzi+zLEqw5bSzflN0qxLMUlzxPLPvaw+TeDyqCYw4nphpz9q8OkpQCmCFUHrcevjMC0QiRCSPAYYnYg2Ju//Y9jI+/VzE6jBPTARGxWEcRN+lRLGY/E0mA5iHiekLXOkEHcWKqGBHMVYLQ8yiOXCwQZ6HDsPUHaCHsVON+guPju2qCDtGH48bIvCV5R5FIONbHsBAM3SWZsXN5nMfIB9PSL7V4GOO1jhIf59y9hQ7hnGkDyi7DifgwzRwmaKvD3BiFX7oU8jlnWsIq0dBlBtZltM4/7IajlTBZcp+bE3SETovJiaZa2HQhWshiWD9l0uUnZjI7ISZ78RwpR5IE4NwlpEhCJxrHOmy2lX0iLCePkrd5plX6DufBPYUL/nTSOjFZt5EGoFCG5YufSwI40VSOklR5zbFF7yTBSGp5ixFK0U/SNSdJMwdurJgWrXbRbQwaXSHmPZ+wVhXz0ieyWkEOyooFs81XLbCwWqSqZxxweOyBZN5QEfx//kRNWFWimHMZbBmhqOwcjRCTCGf6JnebbOQYLVqtoW1uo9n5lfmKrEhEILLf8znqf4nJ8a3NajlPn+sRO/kf8CouCmsWb2/vL5NnHWZpTW9diWI3DOVb7bqfNMrVa4ykQXjTj2xjoL3EIhZujVA4MEzQQ+R/hXhToazDDEhv8ZCd6VdUjQjpjtr531m2YqQuS6v8BN8e1JmWCUccpxhB2hCmldyFP10yLI2ss+xzLRvbVYqolWbXZOUDr/e3H++oMTak7Cw8z2DZ8ipxFzP8q2yT1qh/JF8g2JuYOiCcOYdRKeJ9C2YVT1/o3ytLU8tg4WHM632yTESLq9rLC3YLoXCffcEUvu18t25iuQZhJWFeeY7TwlAt9hTGDLtfictUEZLtErPyfIb/UQ1j3uMLCU3FReRALhD7uEeArsDQ9sbOtDI5oIo/0lSMaOyk3/eb+YjB1RQ/q+pmxiPe35Gcv2EusnvUFmGeGekkJy8LOlMjnNAKRzezQePyfKapolkFHWPY+Y5eBQxvOYhEjLwuTAlCI1orpmKe02eoVqrjzKWjm0Lp4psQnu2KmcYrV5++EXOD7bp+Myem0390qDLbLsI11cw6jgnRKP6LOiUCDoI2ncGxjmyglUfyIxvWbzPYGjEx23Ou8wfW5qrAdSdvBLrNM1OjoYC64DabwLFwzNv5EF2nJJiqkkdmzDp9oSXbE6DOLIRpbZvbVMnpc/2Yd/oB2s4qh/Ew2Udf6Wf/A0f2Ookpr11w79KbYdzpMG0H+FM8mvo4avwjEQsFcFvPS3vMttYgIjFiYigQsaHv4zCYCR4b5lnn5zcVIe9iODvXd698PFaHu8/XwSzYtaFY3cTyOUyY9xfT3b3qCnsfcY5zcORes+0f8cYfYX/RyCer2u2CXSOUPYVhVVPkeThvegns8F0GpTmOE049kewt700wU/l7+ZAt8yl/Rpb72JdRFscYaokQZF8cRFZAMHs6MduSQLZZ1d5kCjGZjN62z7aUsmouOeDoKkWdKZU5i7q2mIo6jqSj7912r8p1OAoxraxH5OGaiE0+0/+a4Zqr4zgcn1CIqT9FlPjZc/suXHM4NucD8McNBnhzCd0AAAAASUVORK5CYII=);
                background-size: 60% auto;
              }
              &.o8 {
                background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANMAAAAfCAYAAAB9LfgWAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUFSURBVHgB7ZxdcuNEFIVPd8S8YlYQ78BmBTFLSDE8DzxSMAysYIYVDCZQPE54B1ysIMoOlBWMswI0xUBNTSz13NvtttqynMSOJOunvyqXHJXtSK0+fW7f2xLg8XhKQcDTal6p2eAdMFgAQ4V0yPvoog7p9TEgB7RvQHvoldJ73hr4M9t+UwFz8xmx2tLvXPN+ATmXQPyNOI3gWcOLqcH8pmbDlISSIB3TnwPqxMdGIMmYhXGbIOpBkaBYaPj7I8jwa3E6R4/xYjogLBbrKMZN5LGCGvLr8ELZBxWRyKffic/P0UO8mCrGCEY7yZCdRWnxYEwNzyHXAJ1EhAuI0x/EaYweEcDzYHje8j+JhMMxEszIEQwJiQI1nnVAz0U03R/B1CRAekFvPkWP8M60A+suI0c8d6EmHKKzDvMwaPD4qk8hn3emAraJZmEyY0uXMY7j2Q611BPanKMn9FpMXjRVo8boIPmw/gZySvPDeS/E5Jz8xCQBxJgvtBeN5zacbOvYTR69zep5eh78COkVbc47JybrNtCC0cmAjZPPUgGeKqFhqvGFXVv0vqGBFrqWl49QcGfyKF06cGvFtJlBW3cbSx+8RugVCyIWULSVcYr0mvdnKxnSuUQQ88oF+x0acbm9nnHmDRVBnex3NIRtJQp3oGX2iVDUsibYCjGxcN7SiRu7lSPqLJM+uA2dkRYAnWNkRWKW9Kg4wFFEI2q8ay3nTM0mJK5XqLworKh4+/gcNZEtqyqs6VVaopAQozJ/rzSMcJIJNwgd3IltDHQUocWBiEZxEopeCxcfQUbvaX+ZRU9u13+B5ySk71E5avpUPC79/xSvGGnG0qoF5CcHdaYi4RjHESuVtz1Mc91lKZjIOkuda9n+Q3IhzDyyMuj3Q+rcPz4VX4TYkZyzDIqWVxl32QzJmtBHHtHx1Samrgsn7zA8f6lbMNv4Wc1eKrNYtnSUdlKEFIJOvy0QUcGqdmfB7koo1DfSZeFb5MTSln6Rjis5TneO07VQzYhGUudJ3rDLVBGSlYkJjdLXqAQRQqeFOfkhj3kPi8Pe7tHl8HwTNX2wM92VHHC3bSITjZn0t/UWAxLSl6gMzgSKiX63dBFDM0KvOlH7hHk80t2Y4ieloxULR4cPtvDZtkbMhWdRB+/LGcFTOmYuLCLq81fLsD66VUx2nkPjzIlbx8nSi+2RTu7kGx+elYd444vU+5MfbG/rN2ti+lXNxmbJjRuuidbVcWyIRudy1aREwGFI5lwJ8WxnOdDOecWGDev3GWy1mM7UXxfsOunKddoRrtlGoOO9ZKulBgj74TY7EdLrOXrOumCqSR5pzfyi/nytGp55yYdp/pkD9+dM/fETXepn6DjbHIbrfHX0lcAchLiETmk2g6x2oa7pz7DfYdrDWeDoRQB10vZbIgoK4LqeJ3E0b0JEosUk9IMw8AQHwE7wEuDSz2+qgTvZSzX7LEDKDnWQ63wfsgW7q1CsUWK5Cx3mUUNTjryqwl6Gd5zDY6518oI66kldRdWiVe12wS4Lpa4wrGpWeQZKQvxD3b20Zxm4cxx44TQSzt6SAwzNvV9qQNfr2H1QpYHDf/MwSguLgEXB79lBWCjK3N4RuwLZZ1V7m3HFdLHvvS25rJpPDnh6iVNnSigJISf3+ZJbx+F0tH9UrsezXrQNUVCPyMI1FhvCBYLI13E8nk1WYmKRBLpYq+c5PlzzeHbkA8HGHKsPXHx9AAAAAElFTkSuQmCC);
                background-size: 60% auto;
              }
            }
            &_context {
              margin-top: 30rpx;
              color: #1d2129;
              font-size: 28rpx;
              font-weight: 500;
            }
            &_section {
              margin-top: 32rpx;
              display: flex;
              align-items: center;
              color: #1d2129;
              font-size: 28rpx;
              font-weight: 500;
              &_icon {
                width: 8rpx;
                height: 32rpx;
                border-radius: 84rpx;
                margin-right: 10rpx;
                background: #18c2a5;
              }
            }
            &_info {
              margin-top: 20rpx;
              display: flex;
              padding: 20rpx;
              flex-direction: column;
              align-items: flex-start;
              gap: 8rpx;
              align-self: stretch;
              border-radius: 32rpx;
              background: #f7f8fa;
              &_title {
                display: flex;
                align-items: center;
                color: #1d2129;
                font-family: "PingFang SC";
                font-size: 24rpx;
                font-weight: 500;
                &_icon {
                  width: 16rpx;
                  height: 16rpx;
                  border-radius: 4rpx;
                  background: #18c2a5;
                  margin-right: 10rpx;
                }
              }
              &_text {
                color: #4e5969;
                font-size: 24rpx;
              }
            }
            &_tips {
              margin-top: 30rpx;
              color: #1d2129;
              font-family: "PingFang SC";
              font-size: 28rpx;
              font-weight: 500;
              &_desc {
                text:nth-child(1) {
                  font-size: 36rpx;
                  font-weight: 500;
                  background: linear-gradient(
                    90deg,
                    #ff7d00 0%,
                    #165dff 50%,
                    #00b42a 100%
                  );
                  background-clip: text;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
                text:nth-child(2) {
                  color: #86909c;
                  font-weight: 400;
                }
              }
            }
            &_compay {
              margin-top: 30rpx;
              display: flex;
              flex-direction: column;
              gap: 20rpx;
            }
          }
        }
      }
    }
  }
}
</style>
