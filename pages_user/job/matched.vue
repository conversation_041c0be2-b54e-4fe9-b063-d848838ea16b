<template>
  <view :class="['page-matched', { hiddenBox: popupShow }]">
    <u-navbar
      leftText="我的匹配"
      bgColor="transparent"
      fixed
      autoBack
    ></u-navbar>
    <view class="tab-wrapper">
      <u-tabs
        :list="tabList"
        :activeStyle="{
          color: '#18C2A5',
          fontSize: '32rpx',
          fontWeight: 500,
        }"
        :inactiveStyle="{
          color: '#1D2129',
          fontSize: '32rpx',
          fontWeight: 400,
        }"
        push
        lineColor="#18C2A5"
        :scrollable="false"
        :current="current"
        @change="tabChange"
      ></u-tabs>
    </view>
    <view class="matched-main">
      <swiper
        class="main-swiper"
        :indicator-dots="false"
        :autoplay="false"
        :duration="300"
        :current="current"
        @change="mainSwiperChange"
      >
        <swiper-item>
          <MatchedList scene="QuickMatch" />
        </swiper-item>
        <swiper-item>
          <MatchedList scene="ExactMatch" />
        </swiper-item>
      </swiper>
      <!-- <Empty v-if="jobData.length === 0 && loaded" text="暂无匹配信息" />
      <template v-else>
        <template v-for="item in jobData">
          <view class="matched-time"
            >{{ item.startTime }} · {{ item.count }}个职位</view
          >
          <view class="job-match-list mt-4">
            <job-card
              v-for="jobItem in item.jobs"
              :key="jobItem.id"
              :item="jobItem"
              :scene="scene"
            />
          </view>
        </template>
        <u-loadmore :status="loading ? 'loading' : loaded ? 'nomore' : ''" />
      </template> -->
    </view>
    <MatchTip v-if="current === 0" />
  </view>
</template>

<script>
import Empty from "@/components/empty/index.vue";
import { MATCH_LIST } from "@/api/resume.js";
import JobCard from "@/components/jobCard.vue";
import MatchTip from "../components/matchTip.vue";
import MatchedList from "../components/matchedList.vue";

export default {
  components: { Empty, JobCard, MatchTip, MatchedList },
  data() {
    return {
      jobData: [],
      pageNum: 1,
      pageSize: 10,
      totalPage: 0,
      loading: false,
      loaded: false,
      popupShow: false,
      current: 0,
      tabList: [
        {
          name: "快速匹配",
          scene: "QuickMatch",
        },
        {
          name: "精准匹配",
          scene: "ExactMatch",
        },
      ],
    };
  },
  computed: {
    scene() {
      return this.tabList[this.current].scene;
    },
  },
  onShow() {},
  onLoad(options) {
    if (options.scene) {
      this.current = this.tabList.findIndex(
        (item) => item.scene === options.scene
      );
    }
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },

    popupFn(data) {
      this.popupShow = data;
    },

    mainSwiperChange(e) {
      console.log("mainSwiperChange", e.detail.current);
      this.current = e.detail.current;
    },
    tabChange(item) {
      this.current = item.index;
    },
  },
};
</script>

<style lang="scss" scoped>
.page-matched {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;
  position: relative;

  .tab-wrapper {
    position: relative;
    z-index: 2;
    background: #fff;
    box-shadow: 0px 10px 10px 0 rgba(174, 174, 174, 0.1);
  }
  .matched-main {
    flex: 1;
    overflow: auto;
    padding: 32rpx 0;
    background: #f7f8fa;
    .main-swiper {
      height: 100%;
    }
  }
}
</style>
