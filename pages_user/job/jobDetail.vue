<template>
  <view class="jobDetail">
    <u-navbar
      leftText="职位详情"
      :fixed="true"
      autoBack
      :bgColor="isFixed ? '#a7f5e8' : 'transparent'"
    ></u-navbar>
    <image
      :src="setImg(`/images/public/topBg1.png`)"
      class="bgimg"
      mode="widthFix"
    ></image>
    <view class="jobDetail_content">
      <view class="jobDetail_content_Basics white-card">
        <view class="jobDetail_content_Basics_top">
          <text class="jobName">{{ detailData.posName || "--" }}</text>
          <text class="jobSalary">{{ detailData.salary || "面议" }}</text></view
        >
        <view class="jobDetail_content_Basics_tags">
          <template v-if="options.scene === 'ExactMatch'">
            <text
              class="tag-plain primary"
              v-if="detailData.recommendType === 6"
            >
              保底型
            </text>
            <text
              class="tag-plain blue"
              v-else-if="detailData.recommendType === 4"
            >
              稳健型
            </text>
            <text
              class="tag-plain org"
              v-else-if="detailData.recommendType === 2"
            >
              挑战型
            </text>
          </template>

          <text class="tag-plain brown" v-if="detailData.internTag">{{
            detailData.internTag
          }}</text>
        </view>
        <view class="jobDetail_content_Basics_info">
          <view class="info_item">
            <view class="title">
              <image
                class="info_item_icon"
                :src="setImg(`/images/match/location.png`)"
              ></image>
              <text class="info_item_title_text">工作地点</text>
            </view>
            <view class="info_item_text">{{
              detailData.location || "--"
            }}</view>
          </view>
          <view class="info_item">
            <view class="title">
              <image
                class="info_item_icon"
                :src="setImg(`/images/match/degree.png`)"
              ></image>
              <text class="info_item_title_text">毕业要求</text>
            </view>
            <view class="info_item_text"
              >{{ detailData.year || "--" }}年后毕业</view
            >
          </view>
        </view>
        <view class="jobDetail_content_Basics_info">
          <view class="info_item">
            <view class="title">
              <image
                class="info_item_icon"
                :src="setImg(`/images/match/kaishishijian.png`)"
              ></image>
              <text class="info_item_title_text">网申开始时间</text>
            </view>
            <view class="info_item_text">{{ detailData.start || "--" }}</view>
          </view>
          <view class="info_item">
            <view class="title">
              <image
                class="info_item_icon"
                :src="setImg(`/images/match/jieshushijian.png`)"
              ></image>
              <text class="info_item_title_text">网申结束时间</text>
            </view>
            <view class="info_item_text">{{ detailData.end || "--" }}</view>
          </view>
        </view>
        <view
          class="jobDetail_content_Basics_company"
          @click="companyShow = true"
        >
          <view class="company-info">
            <view class="company-info_logo">
              <u-avatar
                :src="detailData.logoUrl || ''"
                fontSize="14"
                mode="aspectFit"
                :text="logoName(detailData)"
                randomBgColor
                size="80rpx"
              ></u-avatar>
            </view>
            <view class="company-info_item">
              <view class="company-info_item_name">{{
                detailData.companyName || "--"
              }}</view>
              <view class="company-info_item_tag" v-if="companyTag">
                <text class="tag-plain outline" v-for="item in companyTag">
                  {{ item }}
                </text>
              </view>
            </view>
          </view>
          <view class="c_r">
            <text class="c_r_item_text">详情</text>
            <image class="c_r_icon" :src="setImg('images/right_icon.png')" />
          </view>
        </view>
        <view class="jobDetail_content_Basics_tip"
          >发布于{{ detailData.pubDate || "--" }}
        </view>
      </view>
      <view class="jobDetail_content_descText white-card mt-4">
        <view class="jobDetail_content_descText_item">
          <view class="jobDetail_content_descText_item_title">
            <text>薪资福利</text>
            <view class="jobDetail_content_descText_item_title_line"></view>
          </view>
          <text class="jobDetail_content_descText_item_text">
            {{ detailData.salaryBenefit }}
          </text>
        </view>
        <view class="jobDetail_content_descText_item">
          <view class="jobDetail_content_descText_item_title">
            <text>职位要求</text>
            <view class="jobDetail_content_descText_item_title_line"></view>
          </view>
          <text class="jobDetail_content_descText_item_text">
            {{ detailData.demand }}
          </text>
        </view>
        <view class="jobDetail_content_descText_item">
          <view class="jobDetail_content_descText_item_title">
            <text>岗位描述</text>
            <view class="jobDetail_content_descText_item_title_line"></view>
          </view>
          <text class="jobDetail_content_descText_item_text">
            {{ detailData.info }}
          </text>
        </view>
      </view>
      <template v-if="options.scene === 'ExactMatch'">
        <view class="jobDetail_content_descText white-card mt-4 recommend">
          <image
            class="recommend_icon"
            :src="setImg(`/images/match/title3.png`)"
            mode="widthFix"
            v-if="detailData.recommendType === 2"
          ></image>
          <image
            class="recommend_icon"
            :src="setImg(`/images/match/title2.png`)"
            mode="widthFix"
            v-else-if="detailData.recommendType === 4"
          ></image>
          <image
            class="recommend_icon"
            :src="setImg(`/images/match/title1.png`)"
            mode="widthFix"
            v-else
          ></image>
          <view class="recommend_content">
            <view class="recommend_title">
              <text class="recommend_title_text">推荐理由</text>
              <image
                class="recommend_icon_line"
                :src="setImg(`/images/match/text_bg3.png`)"
                mode="widthFix"
                v-if="detailData.recommendType === 2"
              ></image>
              <image
                class="recommend_icon_line"
                :src="setImg(`/images/match/text_bg2.png`)"
                mode="widthFix"
                v-else-if="detailData.recommendType === 4"
              ></image>
              <image
                class="recommend_icon_line"
                :src="setImg(`/images/match/text_bg1.png`)"
                mode="widthFix"
                v-else
              ></image>
            </view>
            <view class="recommend_tip">
              经过对你的专业背景、实习偏好与岗位需求进行评估：
            </view>
            <image
              class="recommend_type_img"
              :src="setImg(`/images/match/type3.png`)"
              mode="scaleToFill"
              v-if="detailData.recommendType === 2"
            ></image>
            <image
              class="recommend_type_img"
              :src="setImg(`/images/match/type2.png`)"
              mode="scaleToFill"
              v-else-if="detailData.recommendType === 4"
            ></image>
            <image
              class="recommend_type_img"
              :src="setImg(`/images/match/type1.png`)"
              mode="scaleToFill"
              v-else
            ></image>
            <view class="recommend_context mt-4">
              <view
                class="recommend_context_item"
                v-for="(item, index) in recommendReason"
                :key="index"
              >
                <text>•</text>
                <text>{{ item }}</text>
              </view>
            </view>
            <view class="splitter"></view>
            <view class="subtitle">投递建议</view>
            <view class="recommend_context">
              <view class="recommend_context_item">
                <text>•</text>
                <text>
                  你的专业背景、实习偏好与岗位需求与岗位描述相符，适合你
                </text>
              </view>
              <view
                class="recommend_context_item"
                v-for="(item, index) in suggest"
                :key="index"
              >
                <text>•</text>
                <text>{{ item }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>
    <MatchTip :safeArea="false" v-if="options.scene !== 'ExactMatch'" />
    <view class="footer-holder"></view>
    <view class="footbar">
      <button
        class="fbtn0 outline"
        @click="handleCollect"
        v-if="detailData.collection"
      >
        取消收藏
      </button>
      <button class="fbtn0" @click="handleCollect" v-else>收藏</button>
      <button class="fbtn1" v-if="detailData.delivery" disabled>已投递</button>
      <button class="fbtn1" v-else @click="jobSend('send')">投递</button>
    </view>
    <u-popup
      :show="companyShow"
      round="32rpx"
      :round="20"
      bgColor="#fff"
      :closeable="false"
      @close="companyShow = false"
    >
      <view class="company-info-popup">
        <view class="flex items-center justify-between">
          <view class="popup-title">企业介绍</view>
          <view class="popup-close" @click="companyShow = false">关闭</view>
        </view>
        <view class="company-info mt-4">
          <view class="company-info_logo">
            <u-avatar
              :src="detailData.logoUrl || ''"
              fontSize="14"
              mode="aspectFit"
              :text="logoName(detailData)"
              randomBgColor
              size="80rpx"
            ></u-avatar>
          </view>
          <view class="company-info_item">
            <view class="company-info_item_name text-base">
              {{ detailData.companyName || "--" }}
            </view>
            <view class="company-info_item_tag" v-if="companyTag">
              <text class="tag-plain outline" v-for="item in companyTag">{{
                item
              }}</text>
            </view>
          </view>
        </view>
        <text class="company-intro">
          {{ detailData.companyIntro || "--" }}
        </text>
      </view>
    </u-popup>
  </view>
</template>
<script>
import global from "@/common/global";
import { COLLECT_OPT, GET_JOB_DETAIL } from "@/api/resume.js";
import MatchTip from "../components/matchTip.vue";
export default {
  mixins: [],
  props: {},
  components: {
    MatchTip,
  },
  computed: {
    recommendReason() {
      const content = this.detailData.recommendReason;
      if (content) {
        return content.split("\n");
      }
      return [];
    },
    suggest() {
      const content = this.detailData.suggest;
      if (content) {
        return content.split("\n");
      }
      return [];
    },
    companyTag() {
      if (this.detailData.companyTag) {
        return this.detailData.companyTag.split(",");
      }
      return null;
    },
  },
  data() {
    return {
      options: {},
      isFixed: false,
      detailData: {
        collection: false,
      },
      companyShow: false,
    };
  },
  onPageScroll(res) {
    if (res.scrollTop > 10) {
      this.isFixed = true;
    } else {
      this.isFixed = false;
    }
  },
  onLoad(options) {
    this.options = options;
    this.getData(options.id);
  },
  onPullDownRefresh() {
    this.getData(this.options.id);
  },
  methods: {
    logoName(item) {
      if (item.logoUrl || !item.companyName) {
        return "";
      }
      let name = item.companyName.replace(
        /(中国|北京|上海|广州|深圳|厦门|杭州|苏州|南京|天津|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)(省|市)?/g,
        ""
      );
      return name.substr(0, 2);
    },
    async getData(id) {
      try {
        uni.showLoading();
        const result = await GET_JOB_DETAIL(id);
        if (this.qUtil.validResp(result) && result.code === 200) {
          this.detailData = result.data;
        }
      } catch (error) {
      } finally {
        uni.hideLoading();
        uni.stopPullDownRefresh();
      }
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    async handleCollect() {
      const params = {
        jobId: this.options.id,
        collection: !this.detailData.collection,
      };
      const result = await COLLECT_OPT(params);
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.detailData.collection = !this.detailData.collection;
        uni.$u.toast(this.detailData.collection ? "收藏成功" : "取消收藏成功");
      }
    },
    jobSend(type) {
      if (type === "send") {
        // uni.navigateTo({
        //     url: '/pages_user/resume/jobSend'
        // })
      } else if (type === "entrance") {
        // uni.navigateTo({
        //     url: '/pages_user/resume/jobEntrance'
        // })
      } else if (type === "optimize") {
        // uni.navigateTo({
        //     url: '/pages_user/resume/jobOptimize'
        // })
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-holder {
  height: 142rpx;
}
.company-info-popup {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 32rpx;
  line-height: 1.4;
  .popup-title {
    font-size: 30rpx;
    font-weight: 500;
  }
  .popup-close {
    font-size: 28rpx;
    color: #86909c;
  }
}
.company-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  max-height: 50vh;
  overflow: auto;
  .company-info_logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    border: 1px solid #e5e6eb;
  }
  .company-info_item {
    .company-info_item_name {
      overflow: hidden;
      color: #1d2129;
      text-overflow: ellipsis;
      font-size: 24rpx;
      font-weight: 500;
      margin-bottom: 10rpx;
    }
    .company-info_item_tag {
      display: flex;
      align-items: center;
      gap: 8rpx;
    }
  }
}
.jobDetail {
  min-height: 100vh;
  position: relative;
  .bgimg {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    z-index: -1;
  }
  &_content {
    padding: 32rpx;
    position: relative;
    &_Basics_top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 32rpx;
      font-weight: 500;
      .jobName {
        color: #000;
      }
      .jobSalary {
        color: #ff7d00;
      }
    }
    &_Basics_tags {
      display: flex;
      align-items: center;
      margin-top: 20rpx;
      gap: 8rpx;
    }
    &_Basics_info {
      display: flex;
      flex-direction: column;
      gap: 24rpx;
      margin-top: 32rpx;
      line-height: 1.4;
      .info_item {
        flex: 1;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 90rpx;
        .title {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: #86909c;
          white-space: nowrap;
          flex-shrink: 0;
          .info_item_icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }
        }
        .info_item_text {
          font-size: 24rpx;
          font-weight: bold;
          color: #1d2129;
          text-align: right;
        }
      }
    }
    &_Basics_company {
      display: flex;
      padding: 20rpx;
      align-items: center;
      justify-content: space-between;
      gap: 16rpx;
      align-self: stretch;
      border-radius: 16rpx;
      background: #f7f8fa;
      margin-top: 32rpx;

      .c_r {
        display: flex;
        align-items: center;
        overflow: hidden;
        color: #86909c;
        text-overflow: ellipsis;
        font-size: 24rpx;
        font-weight: 400;
        .c_r_icon {
          width: 28rpx;
          height: 28rpx;
        }
      }
    }
    &_Basics_tip {
      color: #86909c;
      font-size: 24rpx;
      margin-top: 32rpx;
    }
    &_descText {
      display: flex;
      flex-direction: column;
      gap: 40rpx;
      &_item {
        display: flex;
        flex-direction: column;
        gap: 40rpx;
        gap: 32rpx;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 1.6;
        &_title {
          font-weight: 500;
          position: relative;
          &_line {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 116rpx;
            height: 10rpx;
            opacity: 0.5;
            background: linear-gradient(90deg, #0ec7ad 0%, #bef7cc 100%);
          }
        }
      }
    }
    .recommend {
      position: relative;
      .recommend_icon {
        width: 100%;
        position: absolute;
        left: 0;
        top: 0;
      }
      .splitter {
        width: 100%;
        height: 0;
        margin: 32rpx 0;
        border-bottom: 0.5px dashed #e5e6eb;
      }
      .subtitle {
        font-size: 28rpx;
        font-weight: 500;
        margin-bottom: 20rpx;
      }
      .recommend_content {
        width: 100%;
        height: auto;
        .recommend_title {
          position: relative;
          font-size: 32rpx;
          font-weight: bold;
          .recommend_title_text {
            position: absolute;
            left: 0;
            top: 0;
            z-index: 2;
          }
          .recommend_icon_line {
            width: 120rpx;
            position: absolute;
            left: 0;
            top: 30rpx;
          }
        }
      }
      .recommend_tip {
        color: #86909c;
        font-size: 24rpx;
        font-weight: 400;
        margin-top: 100rpx;
        z-index: 2;
      }
      .recommend_type_img {
        width: 100%;
        height: 96rpx;
      }
      .recommend_context {
        // color: #4e5969;
        font-size: 28rpx;
        font-weight: 400;
        text-align: justify;
        &_item {
          display: flex;
          line-height: 1.6;
          text:nth-child(1) {
            margin-right: 10rpx;
          }
        }
      }
    }
  }
  &_footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 128rpx;
    padding: 0 32rpx;
    view {
      padding: 20rpx 52rpx;
      border-radius: 8rpx;
      font-size: 14px;
      font-weight: 600;
    }
    .btn_default {
      background-color: #fff;
      color: rgba(0, 0, 0, 0.9);
    }
    .btn_primary {
      background-color: #18c2a5;
      color: #fff;
    }
  }
}
</style>
