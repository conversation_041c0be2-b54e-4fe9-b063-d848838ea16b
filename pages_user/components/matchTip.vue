<template>
  <view :class="['match-tip', { 'safe-bottom': safeArea }]">
    <image
      class="match-tip-img"
      :src="setImg(`/images/match/person.png?v=4`)"
      mode="widthFix"
    ></image>
    <text class="match-tip-tips"> 想了解更多是否真的合适、需要投递策略？ </text>
    <view
      class="match-tip-btns"
      @click="navTo(`/pages/match/index?scene=ExactMatch`)"
    >
      去试试精准匹配
    </view>
  </view>
</template>

<script>
export default {
  props: {
    safeArea: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.match-tip {
  position: relative;
  padding: 32rpx 90rpx 48rpx 240rpx;
  background: linear-gradient(to bottom, #ccffdf, #fff);
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  box-sizing: border-box;
  &.safe-bottom {
    padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
  }

  .match-tip-img {
    position: absolute;
    left: 0;
    top: -44rpx;
    width: 240rpx;
    margin-right: 30rpx;
  }
  .match-tip-tips {
    font-family: "DingTalk JinBuTi";
    font-size: 32rpx;
    color: #3ecdb5;
    font-weight: bold;
    font-style: italic;
  }
  .match-tip-btns {
    margin-top: 20rpx;
    display: flex;
    height: 56rpx;
    width: 260rpx;
    justify-content: center;
    align-items: center;
    border-radius: 84rpx;
    border: 1px solid #18c2a5;
    background: #18c2a5;
    font-size: 28rpx;
    color: #fff;
  }
}
</style>
