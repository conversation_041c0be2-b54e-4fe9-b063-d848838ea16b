<template>
  <scroll-view
    class="matched-view"
    ref="matchScrollView"
    scroll-y="true"
    :scroll-top="scrollTop"
    lower-threshold="50"
    refresher-enabled="true"
    refresher-threshold="80"
    :refresher-triggered="refreshing"
    @scrolltolower="onLoadMore"
    @refresherrefresh="onRefresh"
  >
    <Empty v-if="jobData.length === 0 && loaded" text="暂无匹配信息" />
    <template v-else>
      <template v-for="item in jobData">
        <view class="matched-time">
          {{ item.startTime }} · {{ item.count }}个职位
        </view>
        <view class="job-match-list mt-4 mb-4">
          <job-card
            v-for="jobItem in item.jobs"
            :key="jobItem.id"
            :item="jobItem"
            :scene="scene"
          />
        </view>
      </template>
      <u-loadmore :status="loading ? 'loading' : loaded ? 'nomore' : ''" />
    </template>
  </scroll-view>
</template>
<script>
import Empty from "@/components/empty/index.vue";
import { MATCH_LIST } from "@/api/resume.js";
import JobCard from "@/components/jobCard.vue";
export default {
  components: { Empty, JobCard },
  props: {
    scene: {
      type: String,
      default: "QuickMatch",
    },
  },
  data() {
    return {
      jobData: [],
      pageNum: 1,
      pageSize: 10,
      totalPage: 0,
      refreshing: false,
      loading: false,
      loaded: false,
      scrollTop: 0,
    };
  },
  created() {
    this.getJobList();
  },
  methods: {
    async getJobList(page = 1) {
      if (this.loading) return;
      try {
        if (page === 1) {
          this.jobData = [];
          this.totalPage = 0;
          this.loaded = false;
          this.refreshing = true;
        } else {
          this.loading = true;
        }
        const result = await MATCH_LIST({
          pageNum: page,
          scene: this.scene,
        });
        if (this.qUtil.validResp(result) && result.code === 200) {
          this.pageNum = page;
          const thisData = result.data || {};
          this.jobData = [...this.jobData, thisData];
          this.totalPage = result.data.totalPage;
          this.loaded = this.jobData.length === this.totalPage;
        }
      } catch (error) {
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },
    onLoadMore() {
      if (this.loaded || this.loading) {
        return;
      }
      this.getJobList(this.pageNum + 1);
    },
    onRefresh() {
      console.log("onRefresh");
      this.getJobList(1);
    },
  },
};
</script>
<style lang="scss" scoped>
.matched-view {
  height: 100%;
}

.matched-time {
  font-size: 24rpx;
  color: #86909c;
  text-align: center;
}
.job-match-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 0 32rpx;
}
</style>
