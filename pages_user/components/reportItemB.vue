<template>
  <view :class="['report-item', inChildren ? 'in-children' : '']">
    <text
      :class="[`report-item_${node.type}`, inChildren ? 'in-children' : '']"
      v-if="node.type !== 'list1' && node.type !== 'list2'"
    >
      {{ node.content }}
    </text>
    <view class="report-item_info" v-else>
      <view class="report-item_info_title">
        <view class="report-item_info_title_icon"></view>
        <text class="report-item_info_title_text">
          {{ node.content }}
        </text>
      </view>
      <template v-if="node.children">
        <report-item
          v-for="(cItem, cIndex) in node.children"
          :inChildren="true"
          :key="cIndex"
          :node="cItem"
        />
      </template>
    </view>
  </view>
</template>
<script>
import ReportItem from "./reportItemA.vue";
export default {
  name: "report-item-b",
  components: { ReportItem },
  props: {
    node: {
      type: Object,
      default: () => ({}),
    },
    inChildren: {
      type: Boolean,
      default: false,
    },
  },
  created() {},
  methods: {},
};
</script>
<style lang="scss"></style>
