<template>
  <view class="container">
    <view class="workContent">
      <textarea
        v-model="value"
        maxlength="-1"
        placeholder-class="custom-placeholder"
        :placeholder="textPlaceholder"
      ></textarea>
    </view>
    <view class="footbar">
      <button @click="cancel" class="fbtn0">取消</button>
      <button @click="submit" class="fbtn1">确认</button>
    </view>
  </view>
</template>

<script>
import {
  ADD_RESUME_ITEM,
  EDIT_RESUME_ITEM,
  GET_RESUME_ITEM,
} from "@/api/resume";
export default {
  components: {},
  data() {
    return {
      value: "",
      mode: "",
      submitType: "",
      moduleId: "",
    };
  },
  async onLoad(opt) {
    uni.setNavigationBarTitle({
      title: decodeURIComponent(opt.title),
    });
    this.mode = opt.mode;
    this.submitType = opt.type;
    this.moduleId = opt.moduleId;
    if (opt.id) {
      this.loadData(opt.id);
    }
  },
  computed: {
    textPlaceholder() {
      if (this.mode == "interest") {
        return "请输入你的兴趣爱好";
      } else if (this.mode == "summary") {
        return "丰富个人优势能更好的赢得offer";
      } else if (this.mode == "other") {
        return "可填写你的荣誉奖项，资格证书，技能，语言等内容";
      } else {
        return "请输入你的内容";
      }
    },
  },
  methods: {
    async loadData(id) {
      this.id = id;
      if (this.submitType == "edit") {
        const result = await GET_RESUME_ITEM(this.id);
        if (this.qUtil.validResp(result)) {
          let data = result.data;
          this.value = data.content || "";
        }
      }
    },
    async submit() {
      const certificate = this.value;
      if (!certificate) {
        return uni.showToast({
          title: "请输入内容",
          icon: "none",
        });
      }
      if (this.submitType == "add") {
        await ADD_RESUME_ITEM({
          moduleId: this.moduleId,
          content: certificate,
        });
        uni.navigateBack();
      } else {
        await EDIT_RESUME_ITEM({
          id: this.id,
          content: certificate,
        });
        uni.navigateBack();
      }
    },
    cancel() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.container {
  width: 100%;
  background-color: #fff;
  height: calc(100% - 140rpx);
  padding: 0 30rpx 0 30rpx;
  box-sizing: border-box;
}
// .desc {
//   flex: 1;
//   padding: 0 31rpx;
//   &_textArea {
//     width: 100%;
//     box-sizing: border-box;
//     // height: 200rpx;
//     min-height: 200rpx;
//     background-color: #f5f6f7;
//     border-radius: 8rpx;
//     padding: 40rpx 76rpx 31rpx 31rpx;
//   }
// }

.workContent {
  width: 100%;
  height: 500rpx;
  max-height: 500rpx;
  display: flex;
  background-color: #f5f6f7;
  border-radius: 10rpx;
  padding: 19rpx 19rpx;
  box-sizing: border-box;
  textarea {
    position: relative;
    width: 100%;
    height: 400rpx !important;
    max-height: 430rpx;
    line-height: 40rpx;
    font-size: 16px;
    white-space: pre-wrap; /* 保持换行符和空白符 */
    word-wrap: break-word; /* 自动换行 */
    overflow-y: scroll; /* 使textarea垂直滚动 */
  }
}
// ::v-deep.custom-placeholder {
//   color: #adacb1;
//   font-size: 14px;
// }
::v-deep.custom-placeholder {
  color: #0000004d;
  font-weight: 600;
  // font-size: 16px;
}
</style>
