<template>
  <view class="container experience">
    <view class="no_resume" v-if="!showFile">
      <image :src="setImg('images/resume/fileBox.png')" type="widthFix" />
      <text>暂无简历，请上传您的简历</text>
    </view>
    <view class="footer" v-if="!showFile">
      <view class="button_box" @click="resumeShow = true">上传简历</view>
    </view>
    <view class="resume" v-if="showFile">
      <view class="resume_left" @click="goDetail(fileInfo.url)">
        <image
          :src="
            fileInfo.type === 'pdf'
              ? setImg('images/resume/pdfs.png')
              : setImg('images/resume/img.png')
          "
          type="widthFix"
        />
        <!-- <image :src="setImg('images/resume/img.png')" type="widthFix" /> -->
        <view>
          <text>{{ fileInfo.fileName }}</text>
          <text>{{ fileInfo.createTime }} 上传</text>
        </view>
      </view>
      <image
        class="resume_right"
        @click="deleteFn"
        :src="setImg('images/delete.png')"
        type="widthFix"
      />
    </view>
    <text class="text" v-if="showFile">当前最多上传一份简历</text>

    <ResumePopup
      :show.async="resumeShow"
      @close="resumeShow = false"
      @getDataOrUrl="getDataOrUrl"
    />
  </view>
</template>

<script>
import global from "@/common/global";
import ResumePopup from "@/components/resumePopup.vue";
import { GET_RESUME_DATA, DEL_RESUME_FILE } from "@/api/resume.js";
export default {
  data() {
    return {
      jobWant: {},
      wantArr: [],
      resumeShow: false,
      showFile: false,
      fileInfo: {},
    };
  },
  onLoad(options) {
    this.loadData();
  },
  components: { ResumePopup },
  onReady() {},
  methods: {
    async loadData() {
      const data = await GET_RESUME_DATA();
      if (data.data?.upload) {
        this.fileInfo = {
          fileName: data.data?.upload?.name,
          ...data.data?.upload,
          type: data.data?.upload?.url?.includes("pdf") ? "pdf" : "img",
        };
        this.showFile = true;
      } else {
        this.showFile = false;
      }

      // if (data.data.upload) {
      //   this.showFile = true;
      // }
    },
    // @click="navTo('pages_h5/webview/index')"
    async deleteFn() {
      const res = await DEL_RESUME_FILE(this.fileInfo.ossId);
      if (res.code === 200) {
        uni.$u.toast("删除成功");
        this.showFile = false;
        this.fileInfo = {};
      }
    },
    goDetail(url) {
      if (this.systemInfo.platform === "android") {
        let localData = this.storageUtil.getItem("localData") || {};
        const url = encodeURIComponent(
          this.staticBaseUrl +
            "h5/webView/?token=" +
            localData.vuex_token +
            "&clientid=" +
            this.clientId
        );
        uni.navigateTo({
          url: `/pages_h5/webview/index?url=${url}`,
        });
      } else {
        uni.navigateTo({
          url: `/pages_h5/webview/index?url=${encodeURIComponent(
            url
          )}&type=preview`,
        });
      }
    },
    getDataOrUrl(data) {
      if (data) {
        this.resumeShow = false;
        // this.fileInfo = {
        //   ...data,
        //   createTime: dayjs().format("YYYY.MM.DD"),
        // };
        this.loadData();
        this.showFile = true;
        console.log(this.fileInfo, data, "data");
      }
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  padding: 20rpx 33rpx;
  box-sizing: border-box;
  .text {
    font-size: 12px;
    color: #ccc;
    padding-top: 50rpx;
  }
}
.no_resume {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 300rpx;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  image {
    width: 270rpx;
    height: 225rpx;
  }
  text {
    display: block;
    padding-top: 40rpx;
  }
}
.resume {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
  box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
  &_left {
    display: flex;
    align-items: center;
    image {
      width: 75rpx;
      height: 75rpx;
      margin-right: 20rpx;
    }
    view {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text:nth-child(1) {
        font-size: 16px;
        display: inline-block;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 显示省略号 */
        width: 450rpx; /* 设置容器宽度 */
      }
      text:nth-child(2) {
        font-size: 12px;
        color: #999;
      }
    }
  }
  &_right {
    width: 40rpx;
    height: 40rpx;
  }
}
.footer {
  height: 200rpx;
  .button_box {
    height: 106rpx;
    line-height: 106rpx;
    text-align: center;
    width: 406rpx;
    text-align: center;
    border-radius: 64rpx;
    color: #ffffff;
    margin: 20rpx auto;
    background-color: #18C2A5;
    z-index: 9999;
  }
}
</style>
