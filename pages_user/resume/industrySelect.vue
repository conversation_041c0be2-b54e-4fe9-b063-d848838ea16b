<template>
  <view class="container industry">
    <view class="top">
      <view class="ind_top">
        <view class="tit">
          <view class="tl">已选行业</view>
          <view class="tr">
            <text class="text-primary">{{ selectList.length || 0 }}</text>
            /3
          </view>
        </view>
        <view class="box">
          <view class="placeholder" v-if="selectList.length == 0">
            请从下方选择
          </view>
          <view class="tag-list" v-else>
            <view class="tag" v-for="(item, index) in selectList" :key="index">
              <text>{{ item.name }}</text>
              <text
                class="iconfont icon-close text-primary"
                @click="del(index)"
              ></text>
            </view>
          </view>
        </view>
      </view>
      <view class="industry-list">
        <u-collapse accordion :border="false">
          <u-collapse-item
            :title="itm.name"
            v-for="(itm, idx) in industryList"
            :key="idx"
          >
            <view class="indbox">
              <view
                class="item"
                :class="{
                  curr: selectedIndex.find(
                    (v) => v.index === idx && v.subIndex === bidx
                  ),
                }"
                v-for="(bitm, bidx) in itm.children"
                :key="bidx"
                @click="add(idx, bidx)"
              >
                {{ bitm.name }}
              </view>
            </view>
          </u-collapse-item>
        </u-collapse>
      </view>
    </view>

    <view class="footer">
      <!-- <button @click="cancel" class="fbtn0">取消</button> -->
      <button @click="submit" class="fbtn1">保存</button>
    </view>
  </view>
</template>

<script>
import uniRequest from "@/utils/request";
import { handleTree } from "@/utils";
import storageUtil from "@/utils/storageUtil";

export default {
  data() {
    return {
      selectList: [],
      industryList: [],
      industryListOrigin: [],
      selectedIndex: [
        // {
        //   index:null,
        //   subIndex:null
        // }
      ],
    };
  },
  onLoad(options) {
    this.loadData(options);
  },
  onReady() {},
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    add(idx, bidx) {
      const existIndex = this.selectedIndex.findIndex(
        (v) => v.index === idx && v.subIndex === bidx
      );
      if (existIndex >= 0) {
        this.selectedIndex.splice(existIndex, 1);
        this.selectList.splice(existIndex, 1);
        return;
      }
      if (this.selectList.length >= 3) {
        uni.showToast({
          title: "最多添加3个",
          icon: "none",
        });
      } else {
        this.selectedIndex.push({ index: idx, subIndex: bidx });
        this.selectList.push(this.industryList[idx].children[bidx]);
      }
    },
    del(index) {
      this.selectList.splice(index, 1);
      this.selectedIndex.splice(index, 1);
    },
    async loadData(options) {
      uni.showLoading({
        title: "加载中",
      });

      let res = await uniRequest.get("/chatai/industry/all");

      this.industryListOrigin = res.data;
      this.industryList = handleTree(res.data, "id", "parentId");

      if (options.industry) {
        options.industry
          .split(",")
          .map((v) => Number(v))
          .forEach((v) => {
            let child = this.industryListOrigin.find((vv) => vv.id === v);
            this.selectList.push(child);
            let father = this.industryListOrigin.find(
              (vv) => vv.id === child.parentId
            );
            this.selectedIndex.push({
              index: this.industryList.findIndex((v) => v.id === father.id),
              subIndex: father.children.findIndex((v) => v.id === child.id),
            });
          });
      }

      uni.hideLoading();
    },
    submit() {
      const selectList = this.selectList;
      if (selectList.length === 0) {
        return uni.$u.toast("请选择行业信息");
      }
      storageUtil.setItem("industryIds", selectList);
      this.navigateUtil.back();
    },
    cancel() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
.industry {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .top {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 20rpx;
  }
  .ind_top {
    background: #fff;
    padding: 20rpx 30rpx;

    .tit {
      font-size: 28rpx;
      font-weight: bold;
      color: #000;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .box {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      padding: 20rpx 0;

      .tips {
        font-size: 28rpx;
        color: #999;
      }
    }
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
    }
  }

  .industry-list {
    background: #fff;
    margin-top: 30rpx;
  }

  .indbox {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 30rpx;

    .item {
      background: #f5f5f5;
      border: 1px solid #f5f5f5;
      padding: 12rpx 16rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      line-height: 44rpx;
      color: #333;

      &.curr {
        background: #e3fffb;
        color: $uv-primary;
        border-color: $uv-primary;
      }
    }
  }
  .footer {
    width: 100%;
    height: auto;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx 50rpx 30rpx;
    box-sizing: border-box;
    background: #fff;
    box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.06);

    button {
      height: 80rpx;
      line-height: 80rpx;
      border: 0;
      border-radius: 40rpx;

      &::after {
        display: none;
      }
    }

    .fbtn0 {
      width: 48%;
      background: #f6f7f9;
      font-size: 30rpx;
      color: #333;
    }

    .fbtn {
      width: 100%;
      background: #18c2a5;
      font-size: 30rpx;
      color: #fff;
    }

    .fbtn1 {
      width: 100%;
      background: #18c2a5;
      font-size: 30rpx;
      color: #fff;
    }
  }
}
</style>
