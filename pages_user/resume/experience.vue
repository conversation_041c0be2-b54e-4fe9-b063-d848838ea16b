<template>
  <view class="container experience">
    <view class="mainForm">
      <u-form
        :model="form"
        :rules="rules"
        labelPosition="top"
        label-width="100"
        ref="uForm"
      >
        <u-form-item label="公司名称" borderBottom prop="title">
          <input
            v-model="form.title"
            placeholder-class="custom-placeholder" 
            placeholder="例如：XX科技有限公司"
          ></input>
        </u-form-item>
        <u-form-item
          label="所在行业"
          borderBottom
          prop="category"
          @click="getIndustry"
        >
        <view class="flex">
          <input
            v-model="form.category"
            placeholder="请选择"
            readonly
            disabled
            placeholder-class="custom-placeholder" 
          />
          <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
          <!-- <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
        </view>
        </u-form-item>
        <u-form-item
          label="所在城市"
          borderBottom
          prop="area"
          @click="openCity"
        >
        <view class="flex">
          <input
            placeholder-class="custom-placeholder" 
            v-model="form.area"
            placeholder="请选择"
            readonly
            disabled
          />
          <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
          <!-- <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
        </view>
        </u-form-item>
        <u-form-item label="在职时间" borderBottom prop="years">
          <time-label ref="timeLabel" @change="changeTime" :start="form.start" :end="form.end" />
        </u-form-item>
        <u-form-item
          label="职位名称"
          borderBottom
          prop="role"
          @click="getJob"
        >
        <view class="flex">
          <input
            v-model="form.role"
            placeholder="请选择"
            readonly
            disabled
            placeholder-class="custom-placeholder" 
          />
          <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
        </view>
        </u-form-item>
        <u-form-item label="所在部门" borderBottom prop="dept">
          <input
            v-model="form.dept"
            placeholder-class="custom-placeholder" 
            placeholder="请输入"
          ></input>
        </u-form-item>
        <u-form-item label="工作内容"  prop="content">
          <view class="workContent">
            <textarea
              maxlength="-1"
              v-model="form.content"
              placeholder-class="custom-placeholder" 
              placeholder="建议具体+数据形式，精简描述，重点突出"
            ></textarea
          ></view>
        </u-form-item>
      </u-form>
    </view>
    <view class="footbar">
      <button @click="cancel" class="fbtn0">{{submitType === 'add' ? '取消' : '删除'}}</button>
      <button @click="submit" class="fbtn1">保存</button>
    </view>
    <barry-picker ref="cityPicker" @get-address="getCity"></barry-picker>
  </view>
</template>

<script>
import global from "@/common/global";
import storageUtil from "../../utils/storageUtil";
import TimeLabel from "./components/timeLabel.vue";
import { setTime } from "@/utils";
import { ADD_RESUME_ITEM,DEL_RESUME_ITEM,EDIT_RESUME_ITEM,GET_RESUME_ITEM } from '@/api/resume';
export default {
  data() {
    return {
      // form: {},
      form: {
        start: "",
        end: "",
        title: "",
        industry: "",
        category: "",
        years: "",
        jobType: "",
        role: null,
        content: "",
        dept: "",
        area: null,
      },
      id:"",
      mode:"",
      submitType:"",
      moduleId:"",
      rules: {
        title: [
          {
            required: true,
            message: "请填写公司名称",
            trigger: "blur,change",
          },
        ],
        role: [
          {
            required: true,
            message: "请选择公司职位",
            trigger: "blur,change",
          },
        ],
        area: [
          {
            required: true,
            message: "请选择所在城市",
            trigger: "blur,change",
          },
        ],
        content: [
          {
            required: true,
            message: "请输入具体工作内容",
            trigger: "blur,change",
          },
        ],
        years: [
          {
            // required: true,
            validator: (rule, value, callback) => {
              console.log(value)
              if (value.startTime||value.start) {
                if (value.endTime||value.end) {
                  callback();
                } else {
                  callback(new Error("请选择结束时间"));
                }
              } else {
                callback(new Error("请选择开始时间"));
              }
            },
            message: "请选择在职时间",
            trigger: ["change", "blur"],
          },
        ],
      },
    };
  },
  components: {
    TimeLabel,
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
        title: decodeURIComponent(options.title)
    });
    this.mode = options.mode;
    this.moduleId = options.moduleId;
    this.submitType = options.type;
    if (options.id) {
      this.loadData(options.id);
    }
  },
  onShow() {
    let industryIds = storageUtil.getItem("industryIds");
    let currJobType = storageUtil.getItem("currJobType");

    if (industryIds) {
      this.industryList = industryIds;
      this.form.industry = industryIds.map((v) => v.id).join(",");
      this.form.category = industryIds.map((v) => v.name).join(",");
      storageUtil.removeItem("industryIds");
    }
    if (currJobType) {
      this.form.jobType = currJobType.id;
      this.form.role = currJobType.name;
      storageUtil.removeItem("currJobType");
    }
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    getJob() {
      uni.navigateTo({
        url: "/pages_user/resume/jobSelect",
      });
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    changeTime(data) {
      if (data.start && data.end) {
        this.form.years = data;
      }
    },
    openCity() {
      this.$refs.cityPicker.show = true;
    },
    getCity(e) {
      this.form.area = e;
    },
    getIndustry() {
      uni.navigateTo({
        url: "/pages_user/resume/industrySelect",
      });
    },
    async loadData(id) {
      this.id = id;
      if (id > 0) {
        const result = await GET_RESUME_ITEM(this.id);
        // const resp = await this.uniRequest.get(`/member/work/${id}`);
        if (this.qUtil.validResp(result)) {
          let data = result.data;
          data.years = {
            startTime: setTime(data.start),
            endTime: setTime(data.end),
          };
          this.form = data;
          this.form.start = setTime(data.start);
          this.form.end = setTime(data.end);
          
        }
      }
    },
    submit() {
      this.$refs.uForm
        .validate()
        .then(async (res) => {
          let data = {
            ...this.form,
            ...this.form.years,
          };
          let result="";
          if(this.submitType === 'add'){
            result = await ADD_RESUME_ITEM({...data, moduleId:this.moduleId})
          }else {
            result = await EDIT_RESUME_ITEM({...data})
          }
    
          if (this.qUtil.validResp(result)) {
            uni.showToast({
              title: "提交成功！",
              icon: "success",
            });
            setTimeout(function () {
              uni.navigateBack({
                delta: 1,
              });
            }, 500);
          } else {
            uni.showToast({
              icon: "none",
              title: result.msg || "提交失败！",
            });
          }
        })
        .catch((errors) => {
          // uni.$u.toast('校验失败')
        });
    },
    async cancel() {
      if(this.submitType === 'add') {
        uni.navigateBack();
      }else {
       const result = await DEL_RESUME_ITEM(this.id);
       if (this.qUtil.validResp(result)) {
          uni.showToast({
            title: "删除成功！",
            icon: "success",
          });
          setTimeout(function () {
            uni.navigateBack({
              delta: 1,
            });
          }, 1400);
        } else {
          uni.showToast({
            icon: "none",
            title: result.msg || "删除失败！",
          });
        }
      }
      
    },
  },
};
</script>

<style lang="scss" scoped>
page,.container {
  height: 100vh;
  background: #fff;
}
.popupBox {
  padding: 30rpx;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.1);
  ::v-deep .u-checkbox-group {
    flex-wrap: wrap !important;
    .u-checkbox {
      margin: 20rpx 20rpx;
    }
  }
  .pop_tit {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }
  .inputbox {
    flex: 1;
    height: 24px;
    line-height: 24px;
    text-align: right;
    padding-right: 15rpx;
    display: flex;
    justify-content: flex-end;

    .ip {
      color: rgb(192, 196, 204);
      font-size: 16px;
    }
    .ic {
      color: #1c819f;
      font-size: 16px;
    }
  }
  .pop_tips {
    font-size: 26rpx;
    color: #999;
    text-align: left;
    padding: 15rpx 20rpx;
  }
  .pop_bt {
    padding-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    button {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 100rpx;
      font-size: 30rpx;
      color: #fff;
      margin: 0;
      padding: 0;
      &::after {
        display: none;
      }
      &.fbtn0 {
        background: -webkit-linear-gradient(top, #999, #666);
        width: 35%;
      }
      &.fbtn {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 100%;
      }
      &.fbtn1 {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 60%;
      }
    }
  }
  &.careerPop {
    width: 660rpx;
  }
}
.workContent {
  width: 100%;
  height: 400rpx;
  max-height: 500rpx;
  display: flex;
  align-items: center;
  background-color: #f5f6f7;
  border-radius: 10rpx;
  padding: 0 19rpx;
  box-sizing: border-box;
  textarea {
    position: relative;
    width: 100%;
    height: 360rpx !important;
    max-height: 500rpx;
    line-height: 40rpx;
    white-space: pre-wrap; /* 保持换行符和空白符 */
    word-wrap: break-word; /* 自动换行 */
    overflow-y: scroll; /* 使textarea垂直滚动 */
  }
}
// ::v-deep.custom-placeholder {
//   color: #adacb1;
// }
::v-deep.custom-placeholder {
  color: #0000004D;
  font-weight: 600;
  // font-size: 16px;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
