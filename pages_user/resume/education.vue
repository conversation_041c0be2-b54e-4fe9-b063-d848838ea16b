<template>
  <view class="container education">
    <view class="mainForm">
      <u-form
        :model="form"
        :rules="rules"
        labelPosition="top"
        label-width="100"
        ref="uForm"
      >
        <u-form-item label="学校名称" borderBottom prop="title">
          <input
            v-model="form.title"
            border="none"
            placeholder="请输入"
            placeholder-class="custom-placeholder" 
          ></input>
        </u-form-item>
        <u-form-item
          label="学历"
          borderBottom
          prop="category"
          @click="educationShow = true"
        >
        <view class="flex" >
          <input
            v-model="form.category"
            readonly
            disabled
            border="none"
            placeholder="请选择"
            placeholder-class="custom-placeholder" 
          ></input>
          <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
          <!-- <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
        </view>
        </u-form-item>
        <u-form-item label="专业" borderBottom prop="role">
          <input
            v-model="form.role"
            border="none"
            placeholder="请填写"
            placeholder-class="custom-placeholder" 
          ></input>
        </u-form-item>
        <u-form-item label="时间段" borderBottom prop="years">
          <time-label ref="timeLabel" @change="changeTime" :start="form.start" :end="form.end"/>
          <!-- <u-input
            v-model="form.years"
            placeholder="请选择"
            readonly
            border="none"
          />
          <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
        </u-form-item>
        <u-form-item label="备注" prop="content">
          <view class="Content">
            <textarea
              v-model="form.content"
              maxlength="-1"
              placeholder-class="custom-placeholder" 
              placeholder="请输入详细内容"
            ></textarea>        
          </view>
        </u-form-item>
      </u-form>
    </view>
    <view class="footbar">
      <button @click="cancel" class="fbtn0">{{submitType === 'add' ? '取消' : '删除'}}</button>
      <button @click="submit" class="fbtn1">保存</button>
    </view>
    <u-picker
      :show="educationShow"
      :columns="[educationList]"
      keyName="label"
      :selectIndex="educationIndex"
      :closeOnClickOverlay="true"
      @close="educationShow = false"
      @cancel="educationShow = false"
      @confirm="educationSelect"
    ></u-picker>
  </view>
</template>

<script>
import { ADD_RESUME_ITEM,DEL_RESUME_ITEM,EDIT_RESUME_ITEM,GET_RESUME_ITEM } from '@/api/resume';
import TimeLabel from "./components/timeLabel.vue";
import { setTime } from "@/utils";
import global from "@/common/global";
export default {
  data() {
    return {
      form: {
        title: "",
        category: "",
        years: "",
        start: "",
        endYear: "",
        role: "",
        content: "",
       
      },
      educationShow: false,
      educationList: [],
      birthShow: false,
      educationIndex: [0],
      submitType:"",
      moduleId:"",
      rules: {
        title: [
          {
            required: true,
            message: "请输入学校名称",
            trigger: ["blur", "change"],
          },
          {
            min: 2,
            max: 30,
            message: "长度在2-30个字符之间",
          },
        ],
        category: [
          {
            required: true,
            message: "请选择学历",
            trigger: "blur,change",
          },
        ],
        years: [
          {
            // required: true,
            validator: (rule, value, callback) => {
              console.log(value,'val')
              if (value.startTime||value.start) {
                if (value.endTime||value.end) {
                  callback();
                } else {
                  callback(new Error("请选择结束时间"));
                }
              } else {
                callback(new Error("请选择开始时间"));
              }
            },
            message: "请选择在校时间",
            trigger: ["change", "blur"],
          },
        ],
        role: [
          {
            required: true,
            message: "请选择专业",
            trigger: "blur,change",
          },
        ],
        content: [
          {
            required: true,
            message: "请输入备注",
            trigger: "blur,change",
          },
        ],
      },
      id:"",
      mode:""
    };
  },
  onLoad(options) {
    console.log(options, "options");
    uni.setNavigationBarTitle({
        title: decodeURIComponent(options.title)
    });
    this.mode = options.mode;
    this.moduleId = options.moduleId;
    this.submitType = options.type;
    this.loadData(options.id);
  },
  components: {
    TimeLabel,
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    setImg(url) {
      return global.STATIC_URL + url;
    },
    educationSelect(e) {
      this.form.category = e.value[0].label;
      this.form.educationValue = e.value[0].value;
      this.educationIndex = e.indexs;
      this.educationShow = false;
    },
    changeTime(data) {
      if (data.start && data.end) {
        this.form.years = data;
      }
    },
    birthConfirm(e) {
      this.form.start = e.value[0];
      this.form.endYear = e.value[1];
      this.form.years = `${e.value[0]}-${e.value[1]}`;
      this.birthShow = false;
    },
    async loadData(id = 0) {
      this.id = id;
      this.educationList = await this.$store.dispatch(
        "getDict",
        "ai_member_education"
      );
      if (this.submitType === 'edit') {
        const result = await GET_RESUME_ITEM(this.id);
        let data = result.data;
          data.years = {
            start: setTime(data.start),
            end: setTime(data.end),
          };
          this.form = data;
          this.form.start = setTime(data.start);
          this.form.end = setTime(data.end);
      }
    },
    submit() {
      this.$refs.uForm
        .validate()
        .then(async (res) => {
          let data = {
            ...this.form,
            ...this.form.years,
          };
          let result="";
          if(this.submitType === 'add'){
            result = await ADD_RESUME_ITEM({...data, moduleId:this.moduleId})
          }else {
            result = await EDIT_RESUME_ITEM({...data})
          }
          if (this.qUtil.validResp(result)) {
            uni.showToast({
              title: "提交成功！",
              icon: "success",
            });
            setTimeout(function () {
              uni.navigateBack({
                delta: 1,
              });
            }, 500);
          } else {
            uni.showToast({
              icon: "none",
              title: result.msg || "提交失败！",
            });
          }
        })
        .catch((errors) => {
          // uni.$u.toast('校验失败')
        });
    },
   async cancel() {
      if(this.submitType === 'add') {
        uni.navigateBack();
      }else {
       const result = await DEL_RESUME_ITEM(this.id);
       if (this.qUtil.validResp(result)) {
          uni.showToast({
            title: "删除成功！",
            icon: "success",
          });
          setTimeout(function () {
            uni.navigateBack({
              delta: 1,
            });
          }, 1400);
        } else {
          uni.showToast({
            icon: "none",
            title: result.msg || "删除失败！",
          });
        }
      }
      
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #fff;
}
 .container{
  height: 100vh;
  width: 100vw;
  background: #fff;
}

.popupBox {
  padding: 30rpx;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.1);

  ::v-deep .u-checkbox-group {
    flex-wrap: wrap !important;

    .u-checkbox {
      margin: 20rpx 20rpx;
    }
  }

  .pop_tit {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }

  .inputbox {
    flex: 1;
    height: 24px;
    line-height: 24px;
    text-align: right;
    padding-right: 15rpx;
    display: flex;
    justify-content: flex-end;

    .ip {
      color: rgb(192, 196, 204);
      font-size: 16px;
    }

    .ic {
      color: #1c819f;
      font-size: 16px;
    }
  }

  .pop_tips {
    font-size: 26rpx;
    color: #999;
    text-align: left;
    padding: 15rpx 20rpx;
  }

  .pop_bt {
    padding-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    button {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 100rpx;
      font-size: 30rpx;
      color: #fff;
      margin: 0;
      padding: 0;

      &::after {
        display: none;
      }

      &.fbtn0 {
        background: -webkit-linear-gradient(top, #999, #666);
        width: 35%;
      }

      &.fbtn {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 100%;
      }

      &.fbtn1 {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 60%;
      }
    }
  }

  &.careerPop {
    width: 660rpx;
  }

}
.Content {
  width: 100%;
  height: 400rpx;
  max-height: 500rpx;
  display: flex;
  align-items: center;
  background-color: #f5f6f7;
  border-radius: 10rpx;
  padding: 0 19rpx;
  box-sizing: border-box;
  textarea {
    position: relative;
    width: 100%;
    height: 360rpx !important;
    max-height: 500rpx;
    line-height: 40rpx;
    white-space: pre-wrap; /* 保持换行符和空白符 */
    word-wrap: break-word; /* 自动换行 */
    overflow-y: scroll; /* 使textarea垂直滚动 */
  }
}
::v-deep.custom-placeholder {
    color: #0000004d;
    font-weight: 600;
    // font-size: 16px;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
