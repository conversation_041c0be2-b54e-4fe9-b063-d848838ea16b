<template>
  <view class="container project">
    <view class="mainForm">
      <u-form
        :model="form"
        :rules="rules"
        labelPosition="top"
        label-width="100"
        ref="uForm"
      >
        <u-form-item :label="titleName" borderBottom prop="title">
          <input
            v-model="form.title"
            placeholder-class="custom-placeholder" 
            :placeholder="'请填写'+titleName"
          ></input>
        </u-form-item>
        <u-form-item label="担任角色" borderBottom prop="role">
          <input
            v-model="form.role"
            placeholder-class="custom-placeholder" 
            placeholder="请填写你所担任的角色"
          ></input>
        </u-form-item>
        <u-form-item label="起止时间" borderBottom prop="years">
          <time-label ref="timeLabel" @change="changeTime"  :start="form.start" :end="form.end"  />
          <!-- <u-input v-model="form.time" border="none" readonly placeholder="请选择项目时间"></u-input>
          <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
        </u-form-item>
        <u-form-item :label="remarkText" prop="content">
          <view class="workContent">
            <textarea
              maxlength="-1"
              v-model="form.content"
              placeholder-class="custom-placeholder"
              :placeholder="textPlaceholder"
            ></textarea>
          </view>
        </u-form-item>
        <!-- <u-form-item label="备注" borderBottom prop="remark">
          <u-textarea
            v-model="form.remark"
            autoHeight
            border="none"
            placeholder="请补充备注"
          ></u-textarea>
        </u-form-item> -->
      </u-form>
    </view>
    <view class="footbar">
      <button @click="cancel" class="fbtn0">{{submitType === 'add' ? '取消' : '删除'}}</button>
      <button @click="submit" class="fbtn1">保存</button>
    </view>
  </view>
</template>

<script>
import { ADD_RESUME_ITEM,DEL_RESUME_ITEM,EDIT_RESUME_ITEM,GET_RESUME_ITEM } from '@/api/resume';
import TimeLabel from "./components/timeLabel.vue";
import { setTime } from '@/utils'
export default {
  data() {
    return {
      // form: {},
      form: {
        id: null,
        title: null,
        role: null,
        years: null,
        startYear: null,
        endYear: null,
        content: null,
        remark: null,
      },

      rules: {
        title: [
          {
            required: true,
            message: "请填写项目名称",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
        role: [
          {
            required: true,
            message: "请填写你所担任的角色",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
        years: [
          {
            // required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error("请选择项目时间"));
              } else if (value.startTime||value.start) {
                if (value.endTime||value.end) {
                  callback();
                } else {
                  callback(new Error("请选择结束时间"));
                }
              } else {
                callback(new Error("请选择开始时间"));
              }
            },
            message: "请选择起止时间",
            trigger: ["change", "blur"],
          },
        ],
        content: [
          {
            required: true,
            message: "请补充备注",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
      },
      id:"",
      mode:"",
      submitType:"",
      moduleId:"",
    };
  },
  components: {
    TimeLabel,
  },
  computed: {
    titleName(){
      if(this.mode == 'activity'){
         return '组织/活动名称'
      }else if(this.mode == 'project'||this.mode == 'research'){
        return '项目名称'
      }else if(this.mode == 'welfare'){
        return '公益名称'
      }else {
        return '名称'
      }
    },
    remarkText(){
      if(this.mode == 'activity'||this.mode == 'research'){
         return '描述'
      }else if(this.mode == 'project'){
        return '项目内容'
      }else if(this.mode == 'welfare'){
        return '公益描述'
      }else {
        return '描述'
      }
    },
    textPlaceholder(){
      if(this.mode == 'activity'||this.mode == 'research'||this.mode == 'welfare'){
         return '请填写详细内容公司名称提示文案：例如：XX科技有限公司'
      }else if(this.mode == 'project'){
        return '建议具体+数据形式，精简描述，重点突出'
      }else {
        return '建议具体+数据形式，精简描述，重点突出'
      }
    },
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
        title: decodeURIComponent(options.title)
    });
    this.mode = options.mode;
    this.moduleId = options.moduleId;
    this.submitType = options.type;
    this.id = options.id;
    if (options.id) {
      this.getProject(options.id);
    }
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    async getProject(id) {
      if(this.submitType == 'edit'){
        const result = await GET_RESUME_ITEM(id);
        if (this.qUtil.validResp(result)) {
          let data = result.data;
          data.years = {
            startTime: setTime(data.start),
            endTime: setTime(data.end),
          };
          this.form = data;
          this.form.start = setTime(data.start);
          this.form.end = setTime(data.end);
        }
      }
    },
    openCity() {
      this.$refs.cityPicker.show = true;
    },
    getCity(e) {
      this.form.city = e;
    },
    changeTime(data) {
      if (data.start && data.end) {
        this.form.years = data;
      }
    },
    getJob() {
      uni.navigateTo({
        url: "/pages_user/resume/jobSelect",
      });
    },
    getIndustry() {
      uni.navigateTo({
        url: "/pages_user/resume/industrySelect",
      });
    },
    submit() {
      const that = this;

      this.$refs.uForm.validate().then(async (valid) => {
        if (valid) {
          let data = {
            ...this.form,
            ...this.form.years,
          };
          let result="";
          if(this.submitType === 'add'){
            result = await ADD_RESUME_ITEM({...data, moduleId:this.moduleId})
          }else {
            result = await EDIT_RESUME_ITEM({...data})
          }
    
          if (that.qUtil.validResp(result)) {
            uni.showToast({
              title: "恭喜，提交成功！",
              icon: "success",
            });
            setTimeout(function () {
              uni.navigateBack({
                delta: 1,
              });
            }, 500);
          } else {
            uni.showToast({
              icon: "none",
              title: result.msg || "提交失败！",
            });
          }
        } else {
          console.log("校验不通过");
        }
      });
    },
    async cancel() {
      if(this.submitType === 'add') {
        uni.navigateBack();
      }else {
       const result = await DEL_RESUME_ITEM(this.id);
       if (this.qUtil.validResp(result)) {
          uni.showToast({
            title: "删除成功！",
            icon: "success",
          });
          setTimeout(function () {
            uni.navigateBack({
              delta: 1,
            });
          }, 1400);
        } else {
          uni.showToast({
            icon: "none",
            title: result.msg || "删除失败！",
          });
        }
      }
      
    },
  },
};
</script>

<style lang="scss">
page {
  background: #fff;
}

.popupBox {
  padding: 30rpx;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.1);

  ::v-deep .u-checkbox-group {
    flex-wrap: wrap !important;

    .u-checkbox {
      margin: 20rpx 20rpx;
    }
  }

  .pop_tit {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }

  .inputbox {
    flex: 1;
    height: 24px;
    line-height: 24px;
    text-align: right;
    padding-right: 15rpx;
    display: flex;
    justify-content: flex-end;

    .ip {
      color: rgb(192, 196, 204);
      font-size: 16px;
    }

    .ic {
      color: #1c819f;
      font-size: 16px;
    }
  }

  .pop_tips {
    font-size: 26rpx;
    color: #999;
    text-align: left;
    padding: 15rpx 20rpx;
  }

  .pop_bt {
    padding-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    button {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 100rpx;
      font-size: 30rpx;
      color: #fff;
      margin: 0;
      padding: 0;

      &::after {
        display: none;
      }

      &.fbtn0 {
        background: -webkit-linear-gradient(top, #999, #666);
        width: 35%;
      }

      &.fbtn {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 100%;
      }

      &.fbtn1 {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 60%;
      }
    }
  }

  &.careerPop {
    width: 660rpx;
  }
}

.workContent {
  width: 100%;
  height: 400rpx;
  max-height: 500rpx;
  display: flex;
  align-items: center;
  background-color: #f5f6f7;
  border-radius: 10rpx;
  padding: 0 19rpx;
  box-sizing: border-box;
  textarea {
    position: relative;
    width: 100%;
    height: 360rpx !important;
    max-height: 500rpx;
    line-height: 40rpx;
    white-space: pre-wrap; /* 保持换行符和空白符 */
    word-wrap: break-word; /* 自动换行 */
    overflow-y: scroll; /* 使textarea垂直滚动 */
  }
}
// ::v-deep.custom-placeholder {
//   color: #adacb1;
// }
::v-deep.custom-placeholder {
  color: #0000004D;
  font-weight: 600;
  // font-size: 16px;
}
</style>
