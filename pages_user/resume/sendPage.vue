<template>
  <view :class="['p-4  ', { hiddenBox: popupShow }]">
    <u-navbar leftText="我的投递" @leftClick="backPage" fixed></u-navbar>
    <Empty v-if="jobData.length === 0 && noData" text="暂无数据" />
    <view v-else class="flex flex-col gap-4">
      <JobCard v-for="(item, index) in jobData" :key="index" :item="item" />
    </view>
  </view>
</template>
<script>
import Empty from "@/components/empty/index.vue";
import { MATCH_LIST } from "@/api/resume.js";
// import JobMatchCard from "@/pages_user/resume/components/jobMatchCard.vue";
import JobCard from "@/components/jobCard.vue";
export default {
  mixins: [],
  props: {},
  components: { Empty, JobCard },
  data() {
    return {
      jobData: [],
      pageNum: 1,
      pageSize: 10,
      totalPage: null,
      noData: false,
      popupShow: false,
    };
  },
  onLoad() {
    this.getJobList();
  },
  onReachBottom() {
    if (this.pageSize == this.totalPage) {
      uni.$u.toast("没有更多了");
      return;
    }
    this.pageNum++;
    this.getJobList();
  },
  computed: {},
  methods: {
    backPage() {
      uni.navigateBack();
    },
    async getJobList() {
      if (this.totalPage == this.jobData.length) return;
      const result = await MATCH_LIST({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: 4,
      });
      if (this.qUtil.validResp(result) && result.code === 200) {
        const dataList = result.rows || [];
        this.jobData = [...this.jobData, ...dataList] || [];
        this.totalPage = result.total;
        if (this.jobData.length === 0) {
          this.noData = true;
        }
      }
    },
    popupFn(data) {
      this.popupShow = data;
    },
    collectionStatus(el) {
      this.jobData.map((item) =>
        item.id == el.id ? (item.collection = !item.collection) : item
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.hiddenBox {
  height: 100vh;
  overflow: hidden;
}
</style>
