<template>
  <view class="container education">
    <u-navbar leftText="等候名单" @leftClick="backPage" fixed></u-navbar>
    <view class="form-title">
      <text>告诉我们你的信息</text>
      <text class="tip">我们将很快发送邀请码给你</text>
    </view>
    <view class="mainForm mt-4">
      <u-form
        :model="form"
        :rules="rules"
        labelPosition="left"
        label-width="80"
        ref="uForm"
      >
        <u-form-item label="手机号" borderBottom prop="mobile" required>
          <input
            v-model="form.mobile"
            border="none"
            placeholder="请输入"
            type="number"
            placeholder-class="custom-placeholder"
          />
        </u-form-item>
        <u-form-item label="学校名称" borderBottom prop="schoolName" required>
          <input
            v-model="form.schoolName"
            border="none"
            placeholder="请输入"
            placeholder-class="custom-placeholder"
          />
          <!-- <view class="flex-1 text-sm" v-if="form.schoolName">
            {{ form.schoolName }}
          </view>
          <view class="custom-placeholder text-sm" v-else>请选择</view> -->
          <!-- <image
            class="right-icon"
            :src="setImg('images/right_icon.png')"
            mode="widthFix"
            slot="right"
          /> -->
        </u-form-item>
        <u-form-item label="专业" borderBottom prop="majorName" required>
          <input
            v-model="form.majorName"
            border="none"
            placeholder="请输入"
            placeholder-class="custom-placeholder"
          />

          <!-- <view class="flex-1 text-sm" v-if="form.majorName">
            {{ form.majorName }}
          </view>
          <view class="custom-placeholder text-sm" v-else>请选择</view> -->
          <!-- <image
            class="right-icon"
            :src="setImg('images/right_icon.png')"
            mode="widthFix"
            slot="right"
          /> -->
        </u-form-item>
        <u-form-item label="毕业年份" borderBottom prop="year" required>
          <input
            v-model="form.year"
            border="none"
            placeholder="请输入"
            type="number"
            placeholder-class="custom-placeholder"
          />
          <!-- <view class="flex-1" v-if="form.year">
            {{ form.year }}
          </view>
          <view class="custom-placeholder" v-else>请输入</view> -->
        </u-form-item>
        <u-form-item label="其他信息" prop="additionalInfo" borderBottom>
          <input
            v-model="form.additionalInfo"
            border="none"
            placeholder="补充更多信息，以便我们了解更多"
            placeholder-class="custom-placeholder"
          />
        </u-form-item>
      </u-form>
    </view>
    <view class="footbar">
      <button @click="submit" class="fbtn">立即加入等候名单</button>
    </view>
    <u-picker
      title="选择学校"
      :show="schoolShow"
      :columns="[schoolList]"
      keyName="name"
      :defaultIndex="[0]"
      @confirm="handelSchoolSelect"
      :immediateChange="true"
      confirmColor="#18C2A5"
      @cancel="schoolShow = false"
    ></u-picker>
    <u-picker
      title="选择专业"
      :show="majorShow"
      :columns="[majorList]"
      keyName="name"
      :defaultIndex="[0]"
      @confirm="handelMajorSelect"
      :immediateChange="true"
      confirmColor="#18C2A5"
      @cancel="majorShow = false"
    ></u-picker>
  </view>
</template>

<script>
import {
  GET_RESUME_ITEM,
  GET_SCHOOL_LIST,
  GET_MAJOR_LIST,
  JOIN_WAIT_LIST,
} from "@/api/resume";
import TimePop from "./components/timePop.vue";
import { setTime } from "@/utils";
import global from "@/common/global";
export default {
  data() {
    return {
      form: {
        mobile: "",
        schoolId: "",
        schoolName: "",
        majorId: "",
        majorName: "",
        year: "",
        additionalInfo: "",
      },
      timeType: "start",
      timeShow: false,
      educationShow: false,
      educationList: [],
      schoolShow: false,
      schoolList: [],
      majorShow: false,
      majorList: [],
      birthShow: false,
      educationIndex: [0],
      submitType: "",
      moduleId: "",
      rules: {
        mobile: [
          {
            required: true,
            message: "请输入手机号",
            trigger: ["blur", "change"],
          },

          {
            validator: (rule, value, callback) => {
              return this.$u.test.mobile(value);
            },
            message: "手机号码不正确",
            trigger: ["change", "blur"],
          },
        ],
        schoolName: [
          {
            required: true,
            message: "请选择学校",
            trigger: ["blur", "change"],
          },
        ],
        year: [
          {
            required: true,
            message: "请输入毕业年份",
            trigger: ["change", "blur"],
          },
        ],
        majorName: [
          {
            required: true,
            message: "请选择专业",
            trigger: ["blur", "change"],
          },
        ],
      },
      id: "",
      mode: "",
    };
  },
  onLoad() {
    this.getSchool();
    this.getMajor();
  },
  components: {
    TimePop,
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    getSchool() {
      GET_SCHOOL_LIST().then((res) => {
        if (this.qUtil.validResp(res) && res.code === 200) {
          this.schoolList = res.data;
        }
      });
    },
    handelSchoolSelect(e) {
      if (e.value[0]) {
        this.form.schoolName = e.value[0].name;
        this.form.schoolId = e.value[0].id;
      } else {
        this.form.schoolName = this.schoolList[0].name;
        this.form.schoolId = this.schoolList[0].id;
      }
      this.schoolShow = false;
    },
    getMajor() {
      GET_MAJOR_LIST().then((res) => {
        if (this.qUtil.validResp(res) && res.code === 200) {
          this.majorList = res.data;
        }
      });
    },
    handelMajorSelect(e) {
      if (e.value[0]) {
        this.form.majorName = e.value[0].name;
        this.form.majorId = e.value[0].id;
      } else {
        this.form.majorName = this.majorList[0].name;
        this.form.majorId = this.majorList[0].id;
      }
      this.majorShow = false;
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    backPage() {
      uni.navigateBack();
    },
    async loadData(id = 0) {
      this.id = id;
      this.educationList = await this.$store.dispatch(
        "getDict",
        "ai_member_education"
      );
      if (this.submitType === "edit") {
        const result = await GET_RESUME_ITEM(this.id);
        let data = result.data;
        data.years = {
          start: setTime(data.start),
          end: setTime(data.end),
        };
        this.form = data;
        this.form.start = setTime(data.start);
        this.form.end = setTime(data.end);
      }
    },
    submit() {
      this.$refs.uForm
        .validate()
        .then(async (res) => {
          let data = {
            ...this.form,
          };
          let result = await JOIN_WAIT_LIST({ ...data });
          if (this.qUtil.validResp(result)) {
            uni.showToast({
              title: "提交成功！",
              icon: "success",
            });
            setTimeout(function () {
              uni.navigateBack({
                delta: 1,
              });
            }, 500);
          } else {
            uni.showToast({
              icon: "none",
              title: result.msg || "提交失败！",
            });
          }
        })
        .catch((errors) => {
          // uni.$u.toast('校验失败')
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.right-icon {
  width: 32rpx;
}
.container {
  height: 100vh;
  width: 100vw;
  background: #fff;
  padding: 32rpx;
  box-sizing: border-box;
}

.form-title {
  font-size: 40rpx;
  font-weight: 500;
  color: #1d2129;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  .tip {
    font-size: 24rpx;
    color: #86909c;
    font-weight: normal;
  }
}
.mainForm {
  padding: 0;
}

.popupBox {
  padding: 30rpx;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.1);

  ::v-deep .u-checkbox-group {
    flex-wrap: wrap !important;

    .u-checkbox {
      margin: 20rpx 20rpx;
    }
  }

  .pop_tit {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }

  .inputbox {
    flex: 1;
    height: 24px;
    line-height: 24px;
    text-align: right;
    padding-right: 15rpx;
    display: flex;
    justify-content: flex-end;

    .ip {
      color: rgb(192, 196, 204);
      font-size: 16px;
    }

    .ic {
      color: #1c819f;
      font-size: 16px;
    }
  }

  .pop_tips {
    font-size: 26rpx;
    color: #999;
    text-align: left;
    padding: 15rpx 20rpx;
  }

  .pop_bt {
    padding-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    button {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 100rpx;
      font-size: 30rpx;
      color: #fff;
      margin: 0;
      padding: 0;

      &::after {
        display: none;
      }

      &.fbtn0 {
        background: -webkit-linear-gradient(top, #999, #666);
        width: 35%;
      }

      &.fbtn {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 100%;
      }

      &.fbtn1 {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 60%;
      }
    }
  }

  &.careerPop {
    width: 660rpx;
  }
}
.Content {
  width: 100%;
  height: 400rpx;
  max-height: 500rpx;
  display: flex;
  align-items: center;
  background-color: #f5f6f7;
  border-radius: 10rpx;
  padding: 0 19rpx;
  box-sizing: border-box;
  textarea {
    position: relative;
    width: 100%;
    height: 360rpx !important;
    max-height: 500rpx;
    line-height: 40rpx;
    white-space: pre-wrap; /* 保持换行符和空白符 */
    word-wrap: break-word; /* 自动换行 */
    overflow-y: scroll; /* 使textarea垂直滚动 */
  }
}
::v-deep .custom-placeholder {
  color: #c9cdd4;
  font-size: 32rpx;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
