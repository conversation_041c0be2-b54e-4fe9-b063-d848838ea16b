<template>
  <u-popup :show="show" round="19">
    <view>
      <view class="header">
        <text @click="onCancel">取消</text>
        <text>期望薪资</text>
        <text @click="handleConfirm">确定</text>
      </view>
      <view>
        <picker-view
          indicator-class="indicator"
          :value="value"
          @change="bindChange"
          class="picker-view"
        >
          <picker-view-column class="column one">
            <view
              class="item"
              v-for="(item, index) in startSalaryList"
              :key="index"
              >{{ item == "面议" ? item : item + "k" }}</view
            >
          </picker-view-column>
          <picker-view-column class="column two">
            <view
              class="item"
              v-for="(item, index) in endSalaryList"
              :key="index"
              >{{ item == "面议" ? item : item + "k" }}</view
            >
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { getSalaryRange } from "@/utils";
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    salaryVal: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const startSalaryList = getSalaryRange(3, 30, "init");
    const endSalaryList = getSalaryRange(3, 30, "init");
    return {
      startSalaryList,
      endSalaryList,
      value: [0, 0],
      startSalary: "面议",
      endSalary: "面议",
    };
  },
  watch: {
    show(val) {
      if (val) {
        let startSalary = `${this.salaryVal.startSalary}`;
        let endSalary = `${this.salaryVal.endSalary}`;
        if (startSalary && endSalary) {
          if (startSalary.includes("k")) {
            startSalary = startSalary.replace("k", "");
          }
          if (endSalary.includes("k")) {
            endSalary = endSalary.replace("k", "");
          }
          let startInd = this.startSalaryList.findIndex(
            (item) => item === startSalary
          );
          let endInd;
          if (startInd == 0) {
            this.endSalaryList = ["面议"];
            endInd = 0;
            this.startSalary = "面议";
            this.endSalary = "面议";
          } else {
            const current = this.startSalaryList[startInd] * 1;
            if(current === 30){
              this.endSalaryList = getSalaryRange(30, 30);
            }else{
              this.endSalaryList = getSalaryRange(current + 1, 30); 
            }
            
            endInd = this.endSalaryList.findIndex((item) => item === endSalary);
            this.startSalary = this.startSalaryList[startInd] * 1;
            this.endSalary = this.endSalaryList[endInd] * 1;
          }

          this.value = [startInd, endInd];
        } else {
          this.endSalaryList = ["面议"];
        }
      }
    },
  },
  mounted() {},
  methods: {
    bindChange(e) {
      const val = e.detail.value;
      if (val[0] == 0) {
        this.endSalaryList = ["面议"];
        this.startSalary = "面议";
        this.endSalary = "面议";
        this.value = val;
      } else if (val[0] !== 0) {
        const current = this.startSalaryList[val[0]] * 1;
        if(current === 30){
          this.endSalaryList = getSalaryRange(30, 30);
        }else {
          this.endSalaryList = getSalaryRange(current + 1, 30);
        }
        this.startSalary = current;
        this.endSalary = this.endSalaryList[val[1]] * 1;
        if (val[0] == this.value[0]) {
          this.value = e.detail.value;
        } else {
          this.value = [val[0], 0];
        }
      }
    },
    handleConfirm() {
      this.$emit("confirm", {
        startSalary: this.startSalary,
        endSalary: this.endSalary,
      });
    },
    onConfirm(e) {
      const { value } = e;
    },
    onCancel() {
      this.$emit("cancel");
    },
  },
};
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 113rpx;
  align-items: center;
  border-bottom: 1rpx solid #f6f6f6;
  text:first-child {
    font-size: 14px;
    color: #666666;
  }
  text:last-child {
    font-size: 14px;
    color: #18C2A5;
  }
}
.picker-view {
  width: 100%;
  height: 330rpx;
  margin-top: 30rpx;
  box-sizing: border-box;
}
.item {
  height: 96rpx;
  align-items: center;
  justify-content: center;
  text-align: center;
}
::v-deep .column {
  font-size: 31rpx;
  color: #000;
  text-align: center;
  line-height: 96rpx;
}
::v-deep .column .indicator {
  width: 100% !important;
  overflow: hidden;
  height: 96rpx;
  background: #F4F4F5;
  box-sizing: border-box;
  z-index: -1;
}
</style>
