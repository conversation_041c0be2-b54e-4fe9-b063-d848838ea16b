<template>
  <u-picker
    :show="show"
    ref="yearPicker"
    :columns="arr"
    :defaultIndex="selectIndex"
    @confirm="onConfirm"
    @cancel="onCancel"
    @change="onYearChange"
    @close="close"
    :selectIndex="selectIndex"
    confirmColor="#18C2A5"
  ></u-picker>
</template>

<script>
import { getYearRange, getMonthRange } from "@/utils";
import dayjs from "dayjs";
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    timeType: {
      type: String,
      default: "",
    },
    time: {
      type: String,
      default: "",
    },
  },
  data() {
    const years = getYearRange(1990, dayjs().year());
    const months = getMonthRange(1, dayjs().month() + 1);
    return {
      startYear: 2000, // 设置开始年份的初始值
      endYear: dayjs().year(), // 设置结束年份的初始值
      year: dayjs().year(),
      month: dayjs().month() + 1,
      yearColumns: [
        years, // 年份
        months, // 月份
      ],
      selectIndex: [0, 0],
      arr: [
        years, // 年份
        months, // 月份
      ],
    };
  },
  watch: {
    show(val) {
      if (val) {
        this.selectIndex = [0, 0];
        if (this.timeType !== "start") {
          this.arr = [["至今", ...this.yearColumns[0]], ["-"]];
        }

        // const time = this.time.split("年");
        // console.log(this.time.split("年"));
        // const y = time[0] + "年";
        // const m = time[1];
        // let arr = [];
        // if (y == dayjs().year() + "年") {
        //   arr = getMonthRange(1, dayjs().month() + 1);
        // } else {
        //   arr = getMonthRange(1, 12);
        // }
        // const YInd = this.yearColumns[0].findIndex((item) => item === y);
        // const MInd = arr.findIndex((item) => item === m);
        // this.selectIndex = [YInd, MInd];
        // this.year = this.arr[0][this.time[0]];
        // this.month = this.arr[1][this.time[1]];
        // console.log(this.year);
        // this.selectIndex = [this.time[0], this.time[1]];
        // if (this.timeType === "start" && this.time[0] != 0) {
        //   this.arr[1] = getMonthRange(1, 12);
        // } else if (this.timeType === "end" && this.time[0] == 0) {
        //   this.arr = [["至今", ...this.yearColumns[0]], ["-"]];
        // } else if (this.timeType === "end" && this.time[0] != 0) {
        //   this.arr = [["至今", ...this.yearColumns[0]], this.yearColumns[1]];
        // } else {
        //   this.arr = this.yearColumns;
        // }
      }
    },
  },
  mounted() {},
  methods: {
    onYearChange(e) {
      const val = e.value;
      if (val[0] == dayjs().year() + "年") {
        this.arr[1] = getMonthRange(1, dayjs().month() + 1);
        this.year = val[0];
        const ind = this.arr[0].findIndex((item) => item === val[0]);
        this.month = val[1] && val[1] != "-" ? val[1] : this.arr[1][0];
        this.selectIndex = [ind, 0];
      } else if (val[0] === "至今") {
        this.arr[1] = ["-"];
        this.year = "至今";
        this.month = "-";
        this.selectIndex = [0, 0];
      } else {
        this.arr[1] = getMonthRange(1, 12);
        this.year = val[0];
        const ind = this.arr[0].findIndex((item) => item === val[0]);
        this.month = val[1] && val[1] != "-" ? val[1] : this.arr[1][0];
        this.selectIndex = [ind, 0];
      }
    },
    onConfirm(e) {
      const { value } = e;
      this.$emit("confirm", e, {
        year: value[0] || this.year,
        month: value[1] || this.month,
      });
      this.close();
    },
    onCancel() {
      this.$emit("cancel");
      this.close();
    },
    close() {
      this.$emit("update:show", false);
    },
  },
};
</script>
