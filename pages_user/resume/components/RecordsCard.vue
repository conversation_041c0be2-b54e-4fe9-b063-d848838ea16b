<template>
  <view class="Records">
    <view
      @click="showFn"
      :class="!Item.show ? 'RecordsHead RecordsBox' : 'RecordsHead'"
    >
      <text>{{ Item.title }}</text>
      <image
        :src="setImage(Item)"
        :class="!Item.show ? 'expandS' : 'expandH'"
      ></image>
    </view>
    <view class="RecordsList" v-if="Item.show">
      <view
        class="RecordsList_item"
        v-for="(el, i) in Item.list"
        @click="goDetail(el)"
        :key="i"
      >
        <text>生成时间：{{ el.startTime || "--" }}</text>
        <image :src="setImg('images/optimizeRecords/leftDetailIcon.png')" />
      </view>
    </view>
  </view>
</template>
<script>
import global from "@/common/global";
export default {
  mixins: [],
  props: {
    Item: {
      type: Object,
      default: () => ({}),
    },
    currentIndex: {
      type: Number,
      default: 0,
    },
  },
  components: {},
  data() {
    return {
      show: false,
    };
  },
  computed: {},
  methods: {
    // 设置图片
    setImage(item) {
      if (!item.show) {
        return this.setImg("images/optimizeRecords/bottomIcon.png");
      } else {
        return this.setImg("images/optimizeRecords/topIcon.png");
      }
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    // 展开收起
    showFn() {
      this.$emit("currentFn", this.currentIndex);
    },
    // 查看详情
    goDetail(el) {
      if (this.Item.key !== "S6") {
        uni.navigateTo({
          url: `/pages_user/resume/resumeOptimized?scene=${this.Item.key}&id=${el.id}`,
        });
      } else {
        uni.navigateTo({
          url: `/pages_user/resume/jobMatch?id=${el.id}`,
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.Records {
  box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
  border-radius: 16rpx;
  margin-bottom: 32rpx;

  .RecordsHead {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 16rpx 16rpx 0 0;
    background-color: #f6f6f6;
    padding: 24rpx 32rpx 22rpx 32rpx;
    text {
      font-size: 16px;
      font-weight: 600;
    }
    image {
      width: 48rpx;
      height: 48rpx;
      transition: all linear 0.1s;
      display: inline-block;
    }
    .expandH {
      transform: rotate(0deg);
    }
    .expandS {
      transform: rotate(-360deg);
    }
  }
  .RecordsBox {
    background-color: #fff;
    padding: 32rpx 32rpx;
    border-radius: 16rpx;
  }
  .RecordsList_item {
    display: flex;
    align-items: center;
    padding: 36rpx 36rpx 36rpx 32rpx;
    justify-content: space-between;
    text {
      font-size: 12px;
    }
    image {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .RecordsList_item:nth-child(even) {
    background-color: #f5faff;
  }
}
</style>
