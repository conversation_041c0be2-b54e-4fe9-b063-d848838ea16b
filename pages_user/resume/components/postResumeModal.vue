<template>
  <u-modal
    :show="show"
    width="295px"
    :showConfirmButton="false"
    borderRadius="8px"
    @close="close"
    class="editModal"
    marginTop="208px"
    :closeOnClickOverlay="true"
  >
    <view class="modal">
      <view class="topBox">
        <text class="title">请复制后在{{postResumeObj.method !== 'detail'?'电脑端':'浏览器'}}打开</text>
        <view class="content">
          <view class="content_item">
            <text v-if="postResumeObj.method !== 'detail'">{{
              postResumeObj.method === "online"
                ? "网申地址："
                : "投递邮箱地址："
            }}</text>
            <view class="url"> {{ postResumeObj.url }}</view>
          </view>
          <view class="content_item" v-if="postResumeObj.method === 'email'">
            <text>邮箱话术：</text>
            <scroll-view
              :scroll-with-animation="true"
              id="scrollView"
              class="textBox"
              :scroll-y="true"
              :scroll-top="scrollTop"
            >
              <view class="contentBox">
                <zero-markdown-view :markdown="content"></zero-markdown-view>
                <!-- <rich-text
                  >{{ content }}
                  <view class="loadingTextBox" v-if="loading"
                    ><loadingPulse bgColor="#CECECE" /></view
                >
                </rich-text> -->

                <view class="loadingBox" v-if="loadTextShow"
                  ><text>话术生成中</text
                  ><loadingPulse :style="{ width: '51px' }" bgColor="#CECECE"
                /></view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <view class="modal_footer">
        <view @click="close" class="cancelBox">关闭</view>
        <view @click="copyText" class="sureBox">复制</view>
      </view>
    </view>
  </u-modal>
</template>
<script>
import { GET_EMAIL_PHRASING } from "@/api/resume";
import loadingPulse from "./loadingPulse.vue";
export default {
  name: "PostResumeModal",
  components: {
    loadingPulse,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    postResumeObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      modalTit: "",
      loading: false,
      content: "",
      loadTextShow: false,
      scrollTop: 0,
    };
  },
  watch: {
    show(val) {
      if (val && this.postResumeObj.method === "email") {
        this.getEmailContent();
        setTimeout(() => {
          this.getElement();
        }, 700);
      }
    },
  },
  methods: {
    //获取滚动内部元素
    getElement() {
      let q = uni.createSelectorQuery().in(this);
      q.select(".contentBox")
        .boundingClientRect((data) => {
          this.scrollTop = data.height;
        })
        .exec();
    },
    // 邮箱话术生成
    async getEmailContent() {
      this.loadTextShow = true;
      const emailContent = await GET_EMAIL_PHRASING({
        positionId: this.postResumeObj.id,
      });
      if (this.qUtil.validResp(emailContent) && emailContent.code === 200) {
        this.content = emailContent.data.emailContent;
        this.loadTextShow = false;
        this.loading = true;
        // this.startTyping(emailContent.data.emailContent);
      }
    },
    //复制
    copyText() {
      if (this.postResumeObj.method === "email" && this.loadTextShow) {
        uni.$u.toast("邮箱话术生成成功后可复制！");
        return;
      }
      let copyText = "";
      if (this.postResumeObj.method === "email") {
        copyText = `投递邮箱地址：${this.postResumeObj.url} \n 邮箱话术：${this.content}`;
      } else {
        copyText = this.postResumeObj.url;
      }
      uni.setClipboardData({
        data: copyText,
        success: () => {
          uni.showToast({ title: "复制成功", duration: 2000 });
        },
        fail: () => {
          uni.showToast({ title: "复制失败", icon: "none", duration: 2000 });
        },
      });
    },
    //打字机效果
    startTyping(text) {
      let currentIndex = 0;
      const typingSpeed = 150; // 打字速度，单位：毫秒

      const timer = setInterval(() => {
        if (currentIndex == text.length - 1) {
          this.loading = false;
        }
        this.content += text[currentIndex];
        this.getElement();
        currentIndex++;

        if (currentIndex >= text.length) {
          clearInterval(timer);
        }
      }, typingSpeed);
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>
<style lang="scss">
.modal {
  width: 100%;
  .title {
    width: 100%;
    font-size: 18px;
    line-height: 27rpx;
    font-weight: 600;
    padding-bottom: 30rpx;
    display: inline-block;
    text-align: center;
    color: #333333;
  }

  .content {
    &_item {
      font-size: 14px;
      line-height: 24px;
      text {
        font-weight: 600;
        color: #333333;
      }
      .url {
        width: 100%;
        word-wrap: break-word;
        word-break: break-all;
        color: #18C2A5;
        font-size: 14px;
        white-space: pre-wrap;
      }
      .textBox {
        max-height: 300rpx;
      }
    }
  }
  .loadingTextBox {
    display: inline-block;
    margin-left: 10rpx;
    transform: translateY(-5rpx);
  }

  .loadingBox {
    display: inline;
    // display: flex;
    // align-items: center;
    text {
      font-weight: 400;
      padding-right: 15rpx;
    }
  }

  .modal_footer {
    display: flex;
    justify-content: space-between;
    padding-top: 50rpx;
    .cancelBox,
    .sureBox {
      width: 196rpx;
      height: 71rpx;
      border-radius: 60rpx;
      line-height: 71rpx;
      text-align: center;
      color: #18C2A5;
      border: 2rpx solid #18C2A5;
    }
    .sureBox {
      background-color: #18C2A5;
      color: #fff;
    }
  }
}

::v-deep.u-modal {
  border-radius: 40rpx !important;
}
::v-deep.u-popup__content {
  border-radius: 40rpx !important;
  margin-top: -223px !important;
}
</style>
