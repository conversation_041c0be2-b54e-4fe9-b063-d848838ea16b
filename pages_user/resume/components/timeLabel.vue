<template>
  <view>
    <view class="timeBox">
      <text
        @click="openTime('start')"
        :class="!start && !startTime ? 'default' : ''"
        >{{ startTime || start || "开始时间" }}</text
      >
      <text> - </text>
      <text
        @click="openTime('end')"
        :class="!end && !endTime ? 'default' : ''"
        >{{ endTime || end || "结束时间" }}</text
      >
    </view>
    <TimePop
      ref="TimePop"
      :timeType="timeType"
      :show.sync="timeShow"
      @confirm="handleTime"
      @cancel="timeShow = false"
      :time="start"
    />
    <!-- <TimePop
      ref="TimePop"
      :timeType="timeType"
      :show.sync="timeEndShow"
      @confirm="handleTime"
      @cancel="timeEndShow = false"
      :time="timeS"
    /> -->
    <TimePop
      ref="TimePop"
      :timeType="timeType"
      :show.sync="timeEndShow"
      @confirm="handleTime"
      @cancel="timeEndShow = false"
      :time="end"
    />
  </view>
</template>
<script>
import TimePop from "./timePop.vue";
import dayjs from "dayjs";
import { getYearRange, getMonthRange } from "@/utils";
export default {
  props: {
    start: {
      type: String,
      default: "",
    },
    end: {
      type: String,
      default: "",
    },
  },
  data() {
    const years = getYearRange(1990, dayjs().year());
    const months = getMonthRange(1, 12);
    return {
      timeShow: false,
      timeEndShow: false,
      timeType: "start",
      startTime: this.start,
      endTime: "",
      arr: [years, months],
      timeS: [0, 0],
      timeE: [0, 0],
    };
  },
  components: {
    TimePop,
  },
  watch: {
    timeShow() {
      console.log(this.start, this.end);
    },
  },
  onReady() {},
  methods: {
    async handleTime(e, time) {
      let year = "";
      let month = "";
      if (time.year === "至今") {
        year = "至今";
        month = "-";
      } else {
        year = time.year;
        month = time.month;
      }
      if (this.timeType == "start") {
        this.startTime = `${year}${month}`;
        this.timeShow = false;
        this.timeS = e.indexs;
      } else {
        this.endTime =
          time.year == "至今" ? "至今" : `${time.year}${time.month}`;
        this.timeEndShow = false;
        this.timeE = e.indexs;
      }
      this.$emit("change", {
        start: this.startTime || "",
        end: this.endTime || "",
      });
    },
    openTime(type) {
      this.timeType = type;
      let y = "";
      let m = "";
      let yInd = 0;
      let mInd = 0;
      let currentMArr = getMonthRange(1, dayjs().month() + 1);
      if (type == "start") {
        // if (this.start) {
        //   // this.startTime = this.start;
        //   // this.endTime = this.end;
        //   // y = this.start.split("-")[0] + "年";
        //   // m = this.start.split("-")[1] + "月";
        //   // currentMArr =
        //   //   this.end.split("-")[0] == dayjs().year()
        //   //     ? getMonthRange(1, dayjs().month() + 1)
        //   //     : getMonthRange(1, 12);
        //   // yInd = this.arr[0].findIndex((item) => item === y);
        //   // mInd = currentMArr.findIndex((item) => item === m);
        //   this.timeS = ;
        // }
        this.timeShow = true;
      } else {
        // if (this.end === "至今") {
        //   this.startTime = this.start;
        //   this.endTime = this.end;
        //   this.timeE = [0, 0];
        // } else if (this.end) {
        //   this.startTime = this.start;
        //   this.endTime = this.end;
        //   y = this.end.split("-")[0] + "年";
        //   m = this.end.split("-")[1] + "月";
        //   currentMArr =
        //     this.end.split("-")[0] == dayjs().year()
        //       ? getMonthRange(1, dayjs().month() + 1)
        //       : getMonthRange(1, 12);
        //   yInd = this.arr[0].findIndex((item) => item === y);
        //   mInd = currentMArr.findIndex((item) => item === m);
        //   this.timeE = [yInd + 1, mInd];
        //   console.log(this.timeE, this.arr[1], dayjs().month() + 1);
        // }
        this.timeEndShow = true;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.timeBox {
  width: 100%;
  display: flex;
  font-size: 16px;
  justify-content: space-between;
  .default {
    color: #0000004d;
    font-weight: 600;
    font-size: 14px;
  }
}
</style>
