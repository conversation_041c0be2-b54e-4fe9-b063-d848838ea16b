<template>
  <u-modal
    :show="show"
    :showConfirmButton="false"
    borderRadius="20px"
    @close="close"
    class="editModal"
    marginTop="255px"
    :closeOnClickOverlay="true"
  >
    <view class="modalEdit">
      <view class="topBox">
        <text>自定义模块</text>
        <view class="inputBox">
          <!-- <input
            class="input"
            placeholderStyle="color:red"
            placeholder="请输入内容"
            v-model="modalTit"
          /> -->
          <u-input
            v-model="modalTit"
            border="none"
            placeholder="请输入内容"
          ></u-input>
        </view>
      </view>

      <view class="modal_footer">
        <view @click="close" class="cancelBox">取消</view>
        <view @click="saveTit" class="sureBox">保存</view>
      </view>
    </view>
  </u-modal>
</template>
<script>
export default {
  name: "editModal",
  data() {
    return {
      modalTit: "",
      show: false,
    };
  },
  methods: {
    showModal(tit) {
      this.modalTit = tit;
      this.show = true;
    },
    saveTit() {
      if (this.modalTit === "") {
        uni.showToast({
          title: "请输入名称",
          icon: "none",
        });
        return;
      }
      this.$emit("save", this.modalTit);
      this.modalTit = "";
      this.close();
    },
    close() {
      this.show = false;
    },
  },
};
</script>
<style lang="scss">
.modalEdit {
  width: 100%;
  .topBox {
    // background: linear-gradient(to right, #f5fff2, #f4fedb);
  }
  text {
    width: 100%;
    padding-bottom: 20rpx;
    display: inline-block;
    text-align: center;
  }
  .inputBox {
    border-radius: 60rpx;
    border: 2rpx solid #000;
    padding: 32rpx;
    background-color: #f7f7f7;
  }
  .modal_footer {
    display: flex;
    justify-content: space-between;
    padding-top: 50rpx;
    .cancelBox,
    .sureBox {
      width: 45%;
      height: 78rpx;
      border-radius: 60rpx;
      line-height: 78rpx;
      text-align: center;
      border: 2rpx solid #000;
    }
    .sureBox {
      background-color: #000;
      color: #fff;
    }
  }
}
.editModal {
  background-color: red;
}
::v-deep.u-modal {
  border-radius: 40rpx !important;
}
::v-deep.u-popup__content {
  border-radius: 40rpx !important;
  margin-top: -223px !important;
}
</style>
