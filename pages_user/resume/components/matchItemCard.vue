<template>
  <view class="job-match-item bg0">
    <view class="flex-between job-title">
      <view class="job-name">
        <text class="job-tag">实习</text>
        <text class="job-name-text">销售工程师</text>
      </view>
      <view class="job-salary">20~25K</view>
    </view>
    <view class="flex job-company-info">
      <image
        class="job-company-logo"
        src="setImg(`/images/user/defaultUser.png`)"
      />
      <text>经欧贝尔外贸有限公司 · 上市公司</text>
    </view>
    <view class="flex job-match-tags">
      <text class="tag-plain">有HR项目实习经验、善于发现和解决问题的</text>
      <text class="tag-plain">本科以上</text>
      <text class="tag-plain">理科或管理类专业</text>
      <text class="tag-plain">简单标签</text>
    </view>
    <view class="flex-between job-match-time">
      <text>2021-08-01</text>
      <text>上海</text>
    </view>
    <postResumeModal
      :show.async="postShow"
      @close="close"
      :postResumeObj="postResumeObj"
    />
  </view>
</template>
<script>
import {
  COLLECT_OPT,
  GET_RESUME_DATA,
  ADD_TARGET_DATA,
  EDIT_TARGET_DATA,
} from "@/api/resume.js";
import postResumeModal from "@/pages_user/resume/components/postResumeModal.vue";
export default {
  mixins: [],
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    ListId: {
      type: String,
      default: () => "",
    },
  },
  components: { postResumeModal },
  data() {
    return {
      collectFlag: false,
      postShow: false,
      postResumeObj: {},
    };
  },
  computed: {},
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    close() {
      this.postShow = false;
      this.$emit("popupFn", this.postShow);
    },
    async optimizationFn(item) {
      const res = await GET_RESUME_DATA();

      if (res.code === 200) {
        if (res.data.jobTarget && res.data.jobTarget.id) {
          await EDIT_TARGET_DATA({
            companyName: item.companyName,
            jobName: item.posName,
            jobDemand: item.tags.join("、"),
            id: res.data.jobTarget.id,
          });
        } else {
          await ADD_TARGET_DATA({
            companyName: item.companyName,
            jobName: item.posName,
            jobDemand: item.tags.join("、"),
          });
        }
      }
      uni.navigateTo({
        url: `/pages_chat/chat/index?chatScene=S5&popFlag=true`,
      });
    },
    async send(item) {
      if (item.methodType === 1) {
        this.postResumeObj = {
          url: item.url,
          method: "online",
          id: item.id,
        };
      } else {
        this.postResumeObj = {
          url: item.email,
          method: "email",
          id: item.id,
        };
      }
      this.postShow = true;
      // this.$emit("popupFn", this.postShow);
    },
    detail(item) {
      this.postResumeObj = {
        url: item.srcUrl || "",
        method: "detail",
        id: item.id,
      };
      this.postShow = true;
      // this.$emit("popupFn", this.postShow);
    },
    // 收藏
    async collectFn(item) {
      const params = {
        positionId: item.id,
        collection: !item.collection,
      };
      const result = await COLLECT_OPT(params);
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.$emit("load", item);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.job-match-item {
  width: 686rpx;
  height: 316rpx;
  border-radius: 32rpx;
  padding: 30rpx;
  margin: auto;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  .job-name {
    display: flex;
    align-items: center;
    .job-tag {
      font-size: 24rpx;
      color: #fff;
      padding: 0 10rpx;
      border-radius: 16rpx;
    }
    .job-name-text {
      font-size: 32rpx;
      font-weight: 500;
      color: #000000;
      margin-left: 10rpx;
    }
  }
  .job-salary {
    font-size: 32rpx;
    font-weight: 500;
    color: #ff7d00;
  }
  &.bg0 {
    background: linear-gradient(to bottom right, #d5ddff 10%, #f9f9f9 40%);
    .job-name {
      .job-tag {
        background: linear-gradient(to right, #3bb4ff, #3258ff);
      }
    }
  }
  &.bg1 {
    background: linear-gradient(to bottom right, #ccffdf 10%, #f1fff9 40%);
    .job-name {
      .job-tag {
        background: linear-gradient(to right, #41eaa4, #1bdc5e);
      }
    }
  }

  .job-company-info {
    color: #4e5969;
    font-size: 24rpx;
    .job-company-logo {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
      border-radius: 50%;
    }
  }

  .job-match-tags {
    width: 100%;
    margin-top: 20rpx;
    flex-wrap: wrap;
    .tag {
      margin-right: 10rpx;
      margin-bottom: 10rpx;
      font-size: 24rpx;
      color: #4e5969;
      padding: 0 4rpx;
      border-radius: 8rpx;
      text-align: center;
      border: 2rpx solid var(---fill-4, #c9cdd4);
    }
  }
  .job-match-time {
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #86909c;
  }
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex {
  display: flex;
  align-items: center;
}
</style>
