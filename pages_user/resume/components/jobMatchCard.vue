<template>
  <view class="jobMatchItem">
    <view class="flex-between companyName">
      <text>{{ item.companyName }}</text>
      <view @click="detail(item)">详情</view>
    </view>
    <text class="text">{{ item.companyIntro || "--" }}</text>
    <view class="jobInfo">
      <view class="jobInfo_item">
        <text>职位</text>
        {{ item.posName || "--" }}
      </view>
      <view class="jobInfo_item">
        <text>时间</text>
        {{ item.appTime || "--" }}
      </view>
      <view class="jobInfo_item">
        <text>地区</text>
        {{ item.city || "--" }}
      </view>
      <view class="jobInfo_item">
        <text>薪资福利</text>
        {{ item.salary || "--" }}
      </view>
      <view class="jobInfo_item">
        <text>岗位要求</text>
        <view class="jobDemand">
          <text
            v-if="item.tags.length != 0"
            v-for="(el, i) in item.tags"
            :key="i"
            >{{ el }}</text
          >
          <view class="noTags" v-if="item.tags.length == 0">--</view>
        </view>
      </view>
    </view>
    <view class="jobAI">
      <view class="ai_name">
        <image
          :src="setImg(`images/avatar_bot.png`)"
          style="height: auto"
          mode="widthFix"
        />
        <text class="text">小御建议</text>
      </view>
      <text class="text">{{ item.suggest || "--" }}</text>
    </view>
    <view class="jobBottom flex-between">
      <view class="collect_box" @click="collectFn(item)">
        <image
          :src="
            item.collection
              ? setImg(`images/resume/collected.png`)
              : setImg(`images/resume/collect.png`)
          "
          style="height: auto"
          mode="widthFix"
        />
        <text>{{ item.collection ? "已收藏" : "收藏" }}</text>
      </view>
      <view class="btn_box">
        <view @click="optimizationFn(item)">简历优化</view>
        <view @click="send(item)">投递简历</view>
      </view>
    </view>
    <postResumeModal
      :show.async="postShow"
      @close="close"
      :postResumeObj="postResumeObj"
    />
  </view>
</template>
<script>
import {
  COLLECT_OPT,
  GET_RESUME_DATA,
  ADD_TARGET_DATA,
  EDIT_TARGET_DATA,
} from "@/api/resume.js";
import postResumeModal from "@/pages_user/resume/components/postResumeModal.vue";
export default {
  mixins: [],
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    ListId: {
      type: String,
      default: () => "",
    },
  },
  components: { postResumeModal },
  data() {
    return {
      collectFlag: false,
      postShow: false,
      postResumeObj: {},
    };
  },
  computed: {},
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    close() {
      this.postShow = false;
      this.$emit("popupFn", this.postShow);
    },
    async optimizationFn(item) {
      const res = await GET_RESUME_DATA();

      if (res.code === 200) {
        if (res.data.jobTarget && res.data.jobTarget.id) {
          await EDIT_TARGET_DATA({
            companyName: item.companyName,
            jobName: item.posName,
            jobDemand: item.tags.join("、"),
            id: res.data.jobTarget.id,
          });
        } else {
          await ADD_TARGET_DATA({
            companyName: item.companyName,
            jobName: item.posName,
            jobDemand: item.tags.join("、"),
          });
        }
      }
      uni.navigateTo({
        url: `/pages_chat/chat/index?chatScene=S5&popFlag=true`,
      });
    },
    async send(item) {
      if (item.methodType === 1) {
        this.postResumeObj = {
          url: item.url,
          method: "online",
          id: item.id,
        };
      } else {
        this.postResumeObj = {
          url: item.email,
          method: "email",
          id: item.id,
        };
      }
      this.postShow = true;
      // this.$emit("popupFn", this.postShow);
    },
    detail(item){
      this.postResumeObj = {
          url: item.srcUrl||'',
          method: "detail",
          id: item.id,
      };
      this.postShow = true;
      // this.$emit("popupFn", this.postShow);
    },
    // 收藏
    async collectFn(item) {
      const params = {
        positionId: item.id,
        collection: !item.collection,
      };
      const result = await COLLECT_OPT(params);
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.$emit("load", item);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.jobMatchItem {
  padding: 29rpx 29rpx 26rpx 29rpx;
  margin-bottom: 12rpx;
  background-color: #fff;
  .companyName {
    padding-bottom: 15rpx;
    text {
      width: 90%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
      font-size: 18px;
      font-weight: 600;
      color: #333333;
      line-height: 46rpx;
    }
    view {
      font-size: 14px;
      font-weight: 400;
      color: #18C2A5;
      line-height: 32rpx;
    }
  }
  .text {
    font-size: 14px;
    font-weight: 400;
    color: #333;
    line-height: 40rpx;
    padding-bottom: 16rpx;
  }
  .jobInfo {
    &_item {
      padding-top: 28rpx;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      line-height: 40rpx;
      text {
        padding-bottom: 8rpx;
        display: block;
        font-weight: 400;
        color: #a6a6a6;
      }
      .jobDemand {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        text {
          border-radius: 4rpx;
          padding: 4rpx 15rpx;
          color: #565656;
          font-size: 12px;
          line-height: 18px;
          background-color: #f5f5f5;
          margin-right: 8rpx;
          margin-bottom: 8rpx;
        }
        .noTags {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          line-height: 40rpx;
        }
      }
    }
  }
  .jobAI {
    padding-top: 43rpx;
    .ai_name {
      display: flex;
      align-items: center;
      padding-bottom: 10rpx;
      image {
        width: 43rpx;
        height: 43rpx;
      }
      text {
        padding-bottom: 0;
        padding-left: 8rpx;
      }
    }
  }
  .jobBottom {
    padding-top: 43rpx;
    box-sizing: border-box;
    .collect_box {
      display: flex;
      align-items: center;
      flex-direction: column;
      font-size: 12px;
      line-height: 24rpx;
      width: 78rpx;
      text {
        padding-top: 4rpx;
      }
    }
    .btn_box {
      display: flex;
      align-items: center;
      view {
        width: 246rpx;
        height: 71rpx;
        line-height: 71rpx;
        text-align: center;
        color: #fff;
        background-color: #18C2A5;
        border-radius: 40rpx;
      }
      view:first-child {
        border: 2rpx solid #18C2A5;
        color: #18C2A5;
        margin-right: 15rpx;
        background-color: #fff;
      }
    }
    image {
      width: 43rpx;
      height: 43rpx;
    }
  }
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
