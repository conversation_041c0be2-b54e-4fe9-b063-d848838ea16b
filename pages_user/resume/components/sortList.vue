<template>
  <view @touchmove.stop.prevent class="list">
    <view
      class="list_item_box"
      v-for="(item, index) in listArray"
      :key="index"
      :style="{
        top: listPosition[index].top + 'px',
        transition: curretnItemIndex === index ? 'initial' : '.3s',
      }"
      :class="{ activeClass: index == curretnItemIndex }"
    >
      <view
        class="list_item"
        @longtap="longtap($event, index, item)"
        @touchmove="onTouchmove"
        @touchend="onTouchend"
      >
        <view class="list_item_left">
          <image :src="setImg('images/resume/moves.png')" mode="widthFix" />
          <text @click="$emit('editTit', item, index)">{{
            item.moduleName
          }}</text>
          <image
            class="edit"
            @click="$emit('editTit', item, index)"
            v-if="!item.disable"
            :src="setImg('images/resume/bianji.png')"
            mode="widthFix"
          />
        </view>
        <image
          v-if="!item.disable"
          class="list_item_right"
          :src="setImg('images/resume/icon_delete.png')"
          @click="$emit('delete', index, item)"
          mode="widthFix"
        />
      </view>
    </view>
  </view>
</template>

<script>
import global from "@/common/global";
export default {
  props: {
    //列表
    list: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      listArray: [],
      // 所有元素定位位置
      listPosition: [],
      // 记录拖动前元素定位位置
      initListPosition: [],
      // 记录当前拖动元素的下标
      curretnItemIndex: -1,
      // 记录拖动前的位置
      recordPosition: {
        y: 0,
      },
      // 记录拖动前的定位位置
      recordCurrentPositionItem: {
        top: 0,
      },
      // 是否正在交换位置
      isChange: false,
      isDrag: false,
      isTouchMove: true,
      windowHeight: 0,
    };
  },
  watch: {
    list: function (val) {
      this.listArray = [...val];
      this.init();
    },
  },
  created() {
    let windowHeight = uni.getSystemInfoSync().windowHeight;
    // 状态栏高度
    let statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
    // 获取菜单按钮（右上角胶囊按钮）的布局位置信息
    const custom = wx.getMenuButtonBoundingClientRect();
    let navigationBarHeight =
      custom.height + (custom.top - statusBarHeight) * 2;

    let navHeight = navigationBarHeight + statusBarHeight;

    this.windowHeight = windowHeight - navHeight;
    // this.init();
    setTimeout(() => {
      this.init();
    }, 500);
  },
  methods: {
    setImg(url) {
      return global.STATIC_URL + url;
    },
    init() {
      this.listArray = [...this.list];
      let arr = [];
      const query = uni.createSelectorQuery().in(this);
      query.selectAll(".list_item_box").fields(
        {
          rect: true,
          size: true,
        },
        (data) => {
          data.forEach((item, index) => {
            arr.push({
              height: item.height,
              top: (item.height + 15) * index,
            });
          });
          this.listPosition = arr;
          this.initListPosition = [...this.listPosition];
        }
      );
      query.exec(); //执行所有请求
    },
    longtap(event, index, item) {
      if (item.disable) {
        uni.showToast({
          title: "该条目不可拖动",
          icon: "none",
        });
        return;
      }
      this.isTouchMove = false;
      this.isDrag = true;

      const { pageY } = event.touches[0];

      // 记录当前拖动元素的下标
      this.curretnItemIndex = index;
      // 记录拖动前的位置
      this.recordPosition = {
        y: pageY,
      };
      // 记录拖动前的定位位置
      this.recordCurrentPositionItem = this.listPosition[index];
    },
    onTouchstart(event, index) {
      // this.isTouchMove = false;
      // this.isDrag = true;
      // const { pageY } = event.touches[0];
      // // 记录当前拖动元素的下标
      // this.curretnItemIndex = index;
      // // 记录拖动前的位置
      // this.recordPosition = {
      //   y: pageY,
      // };
      // // 记录拖动前的定位位置
      // this.recordCurrentPositionItem = this.listPosition[index];
    },
    onTouchmove(event) {
      if (!this.isDrag) {
        return;
      }
      const { pageY } = event.touches[0];

      // 获取移动的差
      this.$set(this.listPosition, this.curretnItemIndex, {
        top:
          this.listPosition[this.curretnItemIndex].top +
          (pageY - this.recordPosition.y),
      });
      // 记录位置
      this.recordPosition = {
        y: pageY,
      };
      // 向下
      if (
        this.listPosition[this.curretnItemIndex].top >=
        this.listPosition[this.curretnItemIndex + 1]?.top -
          this.initListPosition[0].height / 2
      ) {
        if (this.isChange) return;
        this.isChange = true;
        let temp = this.listArray[this.curretnItemIndex];
        this.listArray[this.curretnItemIndex] =
          this.listArray[this.curretnItemIndex + 1];
        this.listArray[this.curretnItemIndex + 1] = temp;
        this.listPosition[this.curretnItemIndex + 1] =
          this.listPosition[this.curretnItemIndex];
        this.listPosition[this.curretnItemIndex] =
          this.recordCurrentPositionItem;
        this.curretnItemIndex = this.curretnItemIndex + 1;
        this.recordCurrentPositionItem =
          this.initListPosition[this.curretnItemIndex];
        this.isChange = false;
      }
      // 向上
      if (
        this.listPosition[this.curretnItemIndex].top <=
        this.listPosition[this.curretnItemIndex - 1]?.top +
          this.initListPosition[0].height / 2
      ) {
        if (this.listPosition[this.curretnItemIndex - 1]?.top == 0) {
          return;
        }
        if (this.isChange) return;
        this.isChange = true;
        let temp = this.listArray[this.curretnItemIndex];
        this.listArray[this.curretnItemIndex] =
          this.listArray[this.curretnItemIndex - 1];
        this.listArray[this.curretnItemIndex - 1] = temp;
        this.listPosition[this.curretnItemIndex - 1] =
          this.listPosition[this.curretnItemIndex];
        this.listPosition[this.curretnItemIndex] =
          this.recordCurrentPositionItem;
        this.curretnItemIndex = this.curretnItemIndex - 1;
        this.recordCurrentPositionItem =
          this.initListPosition[this.curretnItemIndex];
        this.isChange = false;
      }
    },
    onTouchend(event) {
      if (!this.isDrag) {
        return;
      }
      this.isTouchMove = true;
      this.isDrag = false;
      // 拖动元素归位
      this.listPosition[this.curretnItemIndex] =
        this.initListPosition[this.curretnItemIndex];
      this.curretnItemIndex = -1;
      this.$emit("change", [...this.listArray]);
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  background-color: #fff;
  &_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    font-size: 14px;
    // border-bottom: 1px solid #c3c3c3;

    &_left {
      display: flex;
      align-items: center;
      text {
        padding: 0 8rpx 0 10rpx;
      }
    }
    image {
      width: 38rpx;
    }
    .edit {
      width: 30rpx;
    }
  }
  &_item:last-child {
    border: 0;
  }
}

.list_item_box {
  padding: 0 24rpx;
  box-sizing: border-box;
}
.activeClass {
  box-shadow: 0 0px 50rpx #cfcfcf;
  z-index: 999;
}
</style>
