<template>
  <view class="titBar" :styles="styles">
    <view class="leftLine"></view>
    <text>{{ title }}</text>
  </view>
</template>
<script>
export default {
  mixins: [],
  props: {
    styles: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: "",
    },
  },
  components: {},
  data() {
    return {};
  },
  computed: {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.titBar {
  display: flex;
  .leftLine {
    width: 10rpx;
    height: 50rpx;
    background-color: #18C2A5;
    border-radius: 8rpx;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-right: 22rpx;
  }
  > text {
    font-size: 18px;
    font-weight: 600;
  }
}
</style>
