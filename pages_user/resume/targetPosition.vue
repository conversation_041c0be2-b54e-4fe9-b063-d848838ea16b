<template>  
  <view class="container education">
    <view class="mainForm">
      <u-form
        :model="form"
        :rules="rules"
        labelPosition="top"
        label-width="100"
        ref="uForm"
      >
        <u-form-item label="公司" borderBottom prop="companyName">
          <input
            v-model="form.companyName"
            placeholder="请输入意向公司名称"
            placeholder-class="custom-placeholder" 
          ></input>
        </u-form-item>
        <u-form-item
          label="岗位"
          borderBottom
          prop="jobName"
        >
          <input
            v-model="form.jobName"
            readonly
            placeholder="请输入意向岗位"
            placeholder-class="custom-placeholder" 
          ></input>
        </u-form-item>

        <u-form-item label="岗位要求" prop="remark">
          <view class="jobDemand">
            <textarea
              v-model="form.jobDemand"
              maxlength="-1"
              placeholder-class="custom-placeholder" 
              placeholder="请输入岗位详细要求"
            ></textarea>
            
          </view>
        </u-form-item>
      </u-form>
    </view>
    <view class="footbar">
      <button @click="cancel" class="fbtn0">{{ id>0 ? '删除' : '取消' }}</button>
      <button @click="submit" class="fbtn1">保存</button>
    </view>

  </view>
</template>

<script>
import { GET_TARGET_DATA,EDIT_TARGET_DATA,DELETE_TARGET_DATA,ADD_TARGET_DATA,GET_RESUME_DATA } from "@/api/resume.js";
// import eventBus from "@/utils/eventBus.js";
export default { // #ifdef MP-WEIXIN
  // #endif
  components: {},
  data() {
    return {
      userInfo: {},
      num: 0,
      targetText: "",
      form: {
        companyName: "",
        jobName: "",
        jobDemand: "",
      },
      id:'',
      rules: {
        companyName: [
          {
            required: true,
            message: "请输入公司名称",
            trigger: "blur",
          },
        ],
        jobName: [
          {
            required: true,
            message: "请输入岗位名称",
            trigger: "blur",
          },
        ],
        jobDemand: [
          {
            required: true,
            message: "请输入岗位详细要求",
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {},
  onLoad(option) {
  
    if(option?.id){
      this.id= option?.id;
     this.getUserInfo(option); 
    }
    
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  onShow() {

  },
  methods: {
    async getUserInfo(opt) {
      const result = await GET_TARGET_DATA(opt?.id);
      if (this.qUtil.validResp(result)) {
        this.form = result.data;
      }
    },
    async cancel(){
      if(this.id>0){
        const result = await DELETE_TARGET_DATA(this.id);
        if (this.qUtil.validResp(result)) {
          uni.navigateBack({
            delta: 1,
            success: () => {
              uni.$emit("updateResume");
            },
          });
          await GET_RESUME_DATA();
        }
   
      }else {
        uni.navigateBack({
          delta: 1,
        });
      }
    },
    submit(){
      this.$refs.uForm.validate().then(async (valid) => {
          if(valid){
            const data = {
              companyName: this.form.companyName,
              jobName: this.form.jobName,
              jobDemand: this.form.jobDemand,
            };
            if(this.id>0){
              const res = await EDIT_TARGET_DATA({
                ...data,
                id: this.id,
              });
              if (this.qUtil.validResp(res)) {
                uni.navigateBack({
                  delta: 1,
                  success: () => {
                    uni.$emit("updateResume");
                  },
                });
              }
            }else{
              const result = await ADD_TARGET_DATA(data);
              if (this.qUtil.validResp(result)) {
                uni.navigateBack({
                  delta: 1,
                  success: () => {
                    uni.$emit("updateResume");
                  },
                });
              }
            }

        }
      });
    }
  },
};
</script>
<style lang="scss" scoped>
page ,.container{
  height: 100vh;
  background: #fff;
}

.popupBox {
  padding: 30rpx;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.1);

  ::v-deep .u-checkbox-group {
    flex-wrap: wrap !important;

    .u-checkbox {
      margin: 20rpx 20rpx;
    }
  }

  .pop_tit {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }

  .inputbox {
    flex: 1;
    height: 24px;
    line-height: 24px;
    text-align: right;
    padding-right: 15rpx;
    display: flex;
    justify-content: flex-end;

    .ip {
      color: rgb(192, 196, 204);
      font-size: 16px;
    }

    .ic {
      color: #1c819f;
      font-size: 16px;
    }
  }

  .pop_tips {
    font-size: 26rpx;
    color: #999;
    text-align: left;
    padding: 15rpx 20rpx;
  }

  .pop_bt {
    padding-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    button {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 100rpx;
      font-size: 30rpx;
      color: #fff;
      margin: 0;
      padding: 0;

      &::after {
        display: none;
      }

      &.fbtn0 {
        background: -webkit-linear-gradient(top, #999, #666);
        width: 35%;
      }

      &.fbtn {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 100%;
      }

      &.fbtn1 {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 60%;
      }
    }
  }

  &.careerPop {
    width: 660rpx;
  }

}
.jobDemand {
  width: 100%;
  height: 200rpx;
  max-height: 300rpx;
  display: flex;
  align-items: center;
  background-color: #f5f6f7;
  border-radius: 10rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
  textarea {
    position: relative;
    width: 100%;
    height: 150rpx !important;
    max-height: 300rpx;
    line-height: 40rpx;
    white-space: pre-wrap; /* 保持换行符和空白符 */
    word-wrap: break-word; /* 自动换行 */
    overflow-y: scroll; /* 使textarea垂直滚动 */
  }
}
// ::v-deep.custom-placeholder {
//   color: #adacb1;
// }
::v-deep.custom-placeholder {
  color: #0000004D;
  font-weight: 600;
  // font-size: 16px;
}
</style>
