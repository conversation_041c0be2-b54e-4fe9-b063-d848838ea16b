<template>
	<view class="optMain">
		<u-navbar :title="title" @leftClick="backPage" fixed></u-navbar>
		<view class="main">
			<!-- 简历优化结果 -->
			<view class="topBox" v-if="Scene === 'S5'">
				<title-bar title="简历优化结果"></title-bar>
				<view class="download" @click="downLoad">下载简历</view>
				<view class="topBoxContent">
					<view class="topBoxDesc" @click="gotoUrl('webView')">
						<view class="topBoxDesc_left">
							<view class="topBoxDesc_left_tit">
								恭喜，简历优化完成！
								<view>AI助力，简历焕新</view>
							</view>
							<view class="look">
								去查看
								<image :src="setImg('images/resume/right_icon.png')" />
							</view>
						</view>
						<image
							mode="widthFix"
							class="topBoxDesc_img"
							:src="setImg('images/resume/ai_img.png')"
						/>
					</view>
				</view>
			</view>
			<!-- 简历优化建议 -->
			<view class="bottomBox" v-if="Scene === 'S5'">
				<title-bar title="简历优化建议"></title-bar>
				<view class="text texts">
					<zero-markdown-view
						type="optimize"
						:markdown="resultData.suggestion || ''"
					></zero-markdown-view>
				</view>
			</view>
			<!-- 笔试题 -->
			<view class="topBox" v-if="Scene === 'S8'">
				<title-bar title="笔试题"></title-bar>
				<view class="topBoxContent">
					<view
						class="topBoxDesc"
						@click="gotoUrl('markdownPage', 'exam')"
						style="height: 221rpx"
					>
						<view class="topBoxDesc_left">
							<view class="topBoxDesc_left_tit">
								笔试题列表
								<view>针对训练，全面提高</view>
							</view>
							<view class="look">
								去查看
								<image :src="setImg('images/resume/right_icon.png')" />
							</view>
						</view>
						<image
							style="width: 150rpx"
							class="topBoxDesc_img"
							mode="widthFix"
							:src="setImg('images/resume/written.png')"
						/>
					</view>
				</view>
			</view>
			<!-- 笔试指南 -->
			<view class="bottomBox" v-if="Scene === 'S8'">
				<title-bar title="笔试指南"></title-bar>
				<view class="topBoxContent" style="padding-bottom: 50rpx">
					<view class="longText">
						<view class="content" :class="{expanded: examExperienceShow}">
							<zero-markdown-view :markdown="resultData.examExperience || ''" />
						</view>
						<view>
							<view class="hintText" v-if="examExperienceShow">
								以上信息来源于网络整理，仅供参考。
							</view>
							<button @click="examExperienceShow = !examExperienceShow">
								<text>{{ !examExperienceShow ? '展开' : '收起' }}</text>
								<image
									:src="setImage('examExperienceShow')"
									:class="!examExperienceShow ? 'expandS' : 'expandH'"
								/>
							</button>
						</view>
					</view>
				</view>
				<!-- <div class="zeroView">
          <zero-markdown-view
            :markdown="resultData.examExperience || ''"
          ></zero-markdown-view>
          <view class="hintText">以上信息来源于网络整理，仅供参考。</view>
        </div> -->
			</view>
			<!-- 面试经验 自我介绍-->
			<view class="topBox" v-if="Scene === 'S7'">
				<title-bar title="自我介绍"></title-bar>
				<view
					class="copyIcon"
					@click="copy(resultData.data.selfIntroduce || '')"
				>
					<image
						class="copy"
						:src="setImg('images/chat/copy.png')"
						mode="widthFix"
					/>
				</view>
				<view class="topBoxContent">
					<view class="longText">
						<view
							class="content"
							:class="{expanded: isExpanded}"
							:style="{minHeight: '9em'}"
						>
							<view>{{ resultData.data.selfIntroduce || '' }}</view>
						</view>
						<button @click="isExpanded = !isExpanded">
							<text>{{ !isExpanded ? '展开' : '收起' }}</text>
							<image
								:src="setImage('isExpanded')"
								:class="!isExpanded ? 'expandS' : 'expandH'"
							/>
						</button>
					</view>
				</view>
			</view>
			<!-- 模拟面试问题 -->
			<view class="cardBox" v-if="Scene === 'S7'">
				<title-bar title="模拟面试问题"></title-bar>
				<view class="topBoxContent">
					<view
						class="topBoxDesc"
						style="height: 221rpx"
						@click="gotoUrl('markdownPage', 'interview')"
					>
						<view class="topBoxDesc_left">
							<view class="topBoxDesc_left_tit">
								面试题列表
								<view>精准备战，轻松过关</view>
							</view>
							<view class="look">
								去查看
								<image :src="setImg('images/resume/right_icon.png')" />
							</view>
						</view>
						<image
							class="topBoxDesc_img"
							mode="widthFix"
							:src="setImg('images/resume/simulation.png')"
						/>
					</view>
				</view>
			</view>
			<!-- 面试经验 展开收起 -->
			<view class="cardBox" style="flex: 1" v-if="Scene === 'S7'">
				<title-bar title="面试经验"></title-bar>
				<view class="topBoxContent" style="padding-bottom: 50rpx">
					<view class="longText">
						<view
							class="content"
							:style="{minHeight: '10.5em'}"
							:class="{expanded: InterviewExperience}"
						>
							<zero-markdown-view
								:markdown="resultData.data.interviewExperience || ''"
							/>
						</view>
						<view>
							<view class="hintText" v-if="InterviewExperience">
								以上信息来源于网络整理，仅供参考。
							</view>
							<button @click="InterviewExperience = !InterviewExperience">
								<text>{{ !InterviewExperience ? '展开' : '收起' }}</text>
								<image
									:src="setImage('InterviewExperience')"
									:class="!InterviewExperience ? 'expandS' : 'expandH'"
								/>
							</button>
						</view>
					</view>
				</view>
			</view>

			<!-- 行业分析报告 -->
			<view class="topBox" style="flex: 1" v-if="Scene === 'S4'">
				<title-bar title="行业分析报告"></title-bar>
				<view class="img" @click="gotoUrl('markdownPage', 'industry')">
					<image
						:src="setImg('images/resume/IndustryAnalysis.png')"
						mode="widthFix"
					/>
				</view>
			</view>
			<!-- 职业规划建议报告 -->
			<view class="topBox" style="flex: 1" v-if="Scene === 'S3'">
				<title-bar title="职业规划建议报告"></title-bar>
				<view class="img" @click="gotoUrl('markdownPage', 'careerPlan')">
					<image
						:src="setImg('images/resume/careerPlanning.png')"
						mode="widthFix"
					/>
				</view>
			</view>

			<!-- offer 建议 -->
			<view class="topBox" style="flex: 1" v-if="Scene === 'S2'">
				<title-bar title="建议"></title-bar>
				<view class="text">{{ resultData.suggestion || '' }}</view>
			</view>
		</view>
		<view class="footerBox" v-if="Scene === 'S5'">
			<view class="cancel" @click="backPage">返回</view>
			<view class="right">
				<view class="success" @click="submit">局部优化</view>
				<view class="tooltips">
					<view class="tooltips_content" v-if="show">
						<text>将持续通过AI对话对输出结果进行调整</text>
						<image
							@click="show = false"
							:src="setImg('images/resume/tipsClose.png')"
						/>
					</view>
					<image
						@click="show = !show"
						:src="setImg('images/resume/hint.png')"
					/>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
import global from '@/common/global'
import {GET_RESULT_DATA} from '@/api/resume.js'
import ChatScene from '@/model/enums/ChatScene'
import titleBar from '@/pages_user/resume/components/titleBar.vue'
export default {
	mixins: [],
	components: {
		titleBar,
	},
	data() {
		return {
			show: false,
			isExpanded: false,
			InterviewExperience: true,
			examExperienceShow: true,
			Scene: '',
			resultData: {},
			id: '',
			title: '',
			type: '',
		}
	},
	onLoad(opt) {
		this.Scene = opt.scene
		const title =
			ChatScene.filter((item) => item.key === opt.scene)[0].label + '结果'
		this.title = title
		if (opt.type) {
			this.type = opt.type
		}
		if (opt.id) {
			this.getLoadData(opt.id)
		}
	},
	onShow() {},
	computed: {},
	methods: {
		submit() {
			const text = `恭喜您的简历已完成整体优化，现在我们可以进行更精细的局部优化了。请说出您想要优化的具体模块和优化要求，小御将继续为您优化。例如，您可以这样描述需求："帮我优化'工作经验'模块，把'负责项目管理这句'修改为更具体和量化的描述。"`
			uni.redirectTo({
				url: `/pages_chat/chat/index?chatScene=${this.Scene}&assistantText=${text}`,
			})
		},
		downLoad() {
			wx.downloadFile({
				// 示例 url，并非真实存在
				url: this.resultData.url,
				success: function (res) {
					const filePath = res.tempFilePath
					wx.openDocument({
						filePath: filePath,
						showMenu: true,
						success: function (res) {
							console.log('打开文档成功')
						},
					})
				},
			})
		},
		showMore() {
			// #ifdef H5
			const content = document.querySelector('.textLong')
			// #endif
			console.log('展开')
		},
		setImage(name) {
			if (!this[name]) {
				return this.setImg('images/resume/top.png')
			} else {
				return this.setImg('images/resume/bottom.png')
			}
		},
		// 复制
		copy(text) {
			uni.setClipboardData({
				data: text,
				success: function () {
					uni.$u.toast('复制成功')
				},
			})
		},
		setImg(url) {
			return global.STATIC_URL + url
		},
		async getLoadData(id) {
			this.id = id
			const result = await GET_RESULT_DATA(id)
			if (this.qUtil.validResp(result) && result.code === 200) {
				this.resultData = result.data
				if (this.Scene === 'S8') {
					this.resultData.examExperience = result.data.data.examExperience
				}
			}
		},
		downLoadShow() {
			return this.systemInfo.platform === 'android' ? true : false
		},
		gotoUrl(type, markType) {
			if (type === 'webView') {
				if (this.systemInfo.platform === 'android') {
					let localData = this.storageUtil.getItem('localData') || {}
					const url = encodeURIComponent(
						this.staticBaseUrl +
							'h5/webView/?token=' +
							localData.vuex_token +
							'&clientid=' +
							this.clientId +
							'&type=download&resultId=' +
							this.id,
					)
					uni.navigateTo({
						url: `/pages_h5/webview/index?url=${url}&type=download`,
					})
				} else {
					uni.navigateTo({
						url:
							'/pages_h5/webview/index?url=' +
							this.resultData.url +
							'&type=download',
					})
				}
			} else if (type === 'markdownPage') {
				uni.navigateTo({
					url:
						'/pages_user/resume/markdownPage?id=' +
						this.id +
						'&type=' +
						markType,
				})
			}
		},
		backPage() {
			if (this.type === 'OA') {
				uni.redirectTo({
					url: `/pages_chat/chat/index?chatScene=${this.Scene}`,
				})
			} else {
				uni.navigateBack({
					delta: 1,
				})
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.optMain {
	display: flex;
	flex-direction: column;
	height: 100vh;
	display: flex;
	flex-direction: column;

	.main {
		flex: 1;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.topBox {
			background-color: #fff;
			padding-top: 30rpx;
			padding-bottom: 12rpx;
			position: relative;
			.download {
				position: absolute;
				top: 35rpx;
				right: 35rpx;
				color: #18c2a5;
				font-size: 14px;
			}
			.copyIcon {
				position: absolute;
				top: 37rpx;
				left: 175rpx;
				image {
					width: 30rpx;
				}
			}
			.topImg {
				width: 92%;
				height: 228rpx;
				margin: 32rpx 32rpx;
			}
			.img {
				box-sizing: border-box;
				padding: 28rpx 38rpx;
				image {
					width: 100%;
					height: 100%;
				}
			}

			.topImgTwo {
				width: 96%;
				height: 288rpx;
				margin-right: 18rpx;
				margin-left: 4rpx;
			}
			.topImgThree {
				width: 96%;
				height: 140rpx;
				margin-right: 18rpx;
				margin-left: 4rpx;
			}
			.topImgFour {
				width: 96%;
				height: 240rpx;
				margin-right: 18rpx;
				margin-left: 4rpx;
			}
		}
		.bottomBox,
		.cardBox {
			background-color: #fff;
			margin-top: 12rpx;
			padding-top: 46rpx;
			position: relative;

			.textLong {
				display: inline-block;
				font-size: 14px;
				line-height: 42rpx;
				padding: 32rpx 24rpx 32rpx 48rpx;
			}
			.downLoad {
				width: 216rpx;
				height: 104rpx;
				border-radius: 64rpx;
				border: 2rpx solid #18c2a5;
				color: #18c2a5;
				font-size: 16px;
				line-height: 104rpx;
				text-align: center;
				margin: 0 auto;
				margin-top: 24rpx;
				position: absolute;
				bottom: 32rpx;
				left: 50%;
				transform: translateX(-50%);
			}
			.texts {
				padding-left: 20rpx;
				padding-right: 48rpx;
			}
		}
		.bottomBox {
			flex: 1;
		}
		.text {
			padding-left: 48rpx;
			padding-right: 84rpx;
			padding-top: 24rpx;
			color: rgba(0, 0, 0, 0.9);
			font-size: 14px;
		}
		.topBoxContent {
			width: 100%;
			padding: 0 32rpx;
			box-sizing: border-box;
		}
		.topBoxDesc {
			width: 100%;
			height: 260rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			background: linear-gradient(
				to right,
				rgba(230, 238, 255, 1),
				rgba(241, 245, 253, 1)
			);
			border-radius: 16rpx;
			box-shadow: 0 4rpx 12rpx 0 rgba(0, 16, 74, 0.13);
			background-size: 100%;
			margin: 32rpx 0;
			&_left {
				width: 55%;
				padding: 40rpx 0 40rpx 56rpx;
				&_tit {
					font-size: 18px;
					color: rgba(0, 0, 0, 0.9);
					line-height: 48rpx;
					view {
						font-size: 14px;
						font-weight: 300;
						line-height: 42rpx;
						color: rgba(0, 0, 0, 0.9);
					}
				}
				.look {
					font-size: 14px;
					color: #18c2a5;
					padding-top: 34rpx;
					display: flex;
					font-weight: 600;
					align-items: center;
					image {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}
			&_img {
				width: 238rpx;
				height: 228rpx;
				margin: 22rpx 16rpx 10rpx 0;
			}
		}
	}
	.footerBox {
		position: relative;
		z-index: 8;
		width: 100%;
		height: 184rpx;
		background-color: #fff;
		display: flex;
		justify-content: center;
		padding: 32rpx 32rpx 0 32rpx;
		box-sizing: border-box;
		box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
		.cancel {
			width: 186rpx;
			height: 85rpx;
			border: 2rpx solid #18c2a5;
			color: #18c2a5;
			border-radius: 64rpx;
			text-align: center;
			line-height: 85rpx;
		}

		.right {
			height: 85rpx;
			margin-left: 57rpx;
			display: flex;
			align-items: center;
			.success {
				width: 320rpx;
				height: 100%;
				background: linear-gradient(135deg, #59bbfa, #889ff8, #b696f4);
				border-radius: 64rpx;
				color: #fff;
				text-align: center;
				line-height: 85rpx;
			}
			.tooltips {
				width: 32rpx;
				height: 32rpx;
				margin-left: 16rpx;
				image {
					width: 32rpx;
					height: 32rpx;
				}
				.tooltips_content {
					position: absolute;
					top: -95rpx;
					right: 54rpx;
					width: 400rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-radius: 8rpx;
					padding: 16rpx 16rpx;
					background-color: rgba(0, 0, 0, 0.75);
					text {
						font-size: 12px;
						color: #fff;
						line-height: 18px;
						padding-right: 14rpx;
					}
					image {
						min-width: 32rpx;
						width: 32rpx;
						height: 32rpx;
					}
				}
			}
		}
	}
}
.longText {
	position: relative;
	padding-bottom: 60rpx;
	.content {
		padding: 0 32rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 6;
		overflow: hidden;
		text-overflow: ellipsis;
		line-height: 1.5em;
		max-height: 10.5em;
		font-size: 14px;
		// max-height: 4.5em; /* 3行 * 1.5行高 */
		transition: all 0.3s ease-in-out;
	}
	.expanded {
		display: flex;
		-webkit-line-clamp: none;
		max-height: none !important;
	}
	button {
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		right: 0;
		box-shadow: 0 0 16rpx 0 rgba(0, 0, 0, 0.25);
		width: 110rpx;
		font-size: 12px;
		height: 44rpx;
		border-radius: 24rpx;
		color: #18c2a5;
		background-color: #fff;
		image {
			width: 16rpx;
			height: 8rpx;
			margin-left: 8rpx;
			transition: all linear 0.1s;
		}
		.expandH {
			transform: rotate(0deg);
		}
		.expandS {
			transform: rotate(-360deg);
		}
	}
}
.zeroView {
	padding: 0 32rpx 50rpx 32rpx;
	color: #000000e5;
}
.hintText {
	position: absolute;
	left: 37rpx;
	bottom: 20rpx;
	padding-left: 0;
	font-size: 14px;
	color: #666666;
}
</style>
