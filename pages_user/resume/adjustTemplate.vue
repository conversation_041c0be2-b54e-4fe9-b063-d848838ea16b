<template>
  <view class="container">
    <u-navbar title="调整模版" @leftClick="backPage" fixed></u-navbar>
    <view class="desc">
      <textarea
        class="desc_textArea"
        :auto-height="true"
        maxlength="-1"
        v-model="demand"
        placeholder-class="custom-placeholder"
        placeholder="对模版中的哪部分内容不满意，可详细描述，AI将根据您的诉求重新生成模版内容。"
      ></textarea>
    </view>
    <view class="footbar">
      <button @click="backPage" class="fbtn0">取消</button>
      <button @click="submit" class="fbtn1">确认</button>
    </view>
  </view>
</template>

<script>
import { EDIT_RESUME_TEMPLATE } from "@/api/resume.js";
export default {
  components: {},
  data() {
    return {
      demand: null,
      mode: "",
      submitType: "",
      moduleId: "",
      templateId: "",
      loading: false,
      historyInfo: {},
    };
  },
  onLoad(options) {
    this.historyInfo = options;
    this.templateId = options.id;
  },
  computed: {},
  methods: {
    async submit() {
      if (this.loading) {
        uni.$u.toast("模版生成中请勿返回！");
        return;
      }
      uni.showLoading({
        title: "模版生成中...",
      });
      this.loading = true;
      const param = {
        demand: this.demand,
        templateId: this.templateId * 1,
      };

      const result = await EDIT_RESUME_TEMPLATE(param);
      if (result.code === 200) {
        this.loading = false;
        uni.hideLoading();
        if (this.systemInfo.platform === "android") {
          let localData = this.storageUtil.getItem("localData") || {};
          const url = encodeURIComponent(
            this.staticBaseUrl +
              "h5/webView/?token=" +
              localData.vuex_token +
              "&clientid=" +
              this.clientId +
              "&type=temp&id=" +
              result.data.id +
              "&url=" +
              result.data.url
          );
          uni.navigateTo({
            url: `/pages_h5/webview/index?url=${url}&type=temp&id=${result.data.id}`,
          });
        } else {
          uni.navigateTo({
            url: `/pages_h5/webview/index?url=${encodeURIComponent(
              result.data.url
            )}&type=temp&id=${result.data.id}`,
          });
        }

        this.loading = false;
      } else {
        this.loading = false;
        uni.$u.toast("模版生成失败");
      }
    },
    backPage() {
      if (this.loading) {
        uni.$u.toast("模版生成中请勿返回！");
        return;
      } else {
        if (this.systemInfo.platform === "android") {
          let localData = this.storageUtil.getItem("localData") || {};
          const url = encodeURIComponent(
            this.staticBaseUrl +
              "h5/webView/?token=" +
              localData.vuex_token +
              "&clientid=" +
              this.clientId +
              "&type=temp&id=" +
              this.templateId
          );
          uni.navigateBack({
            url: `/pages_h5/webview/index?url=${url}&type=temp&id=${this.templateId}`,
            success: () => {
              uni.$emit("updateWebView", this.historyInfo);
            },
          });
        } else {
          uni.navigateBack({
            url: `/pages_h5/webview/index?url=${this.historyInfo.url}&type=temp&id=${this.templateId}`,
            success: () => {
              uni.$emit("updateWebView", this.historyInfo);
            },
          });
        }
      }
    },
  },
};
</script>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.container {
  width: 100%;
  background-color: #fff;
  height: calc(100% - 140rpx);
}
.desc {
  flex: 1;
  padding: 20rpx 31rpx;
  &_textArea {
    width: 100%;
    box-sizing: border-box;
    min-height: 200rpx !important;
    background-color: #f5f6f7;
    border-radius: 8rpx;
    padding: 28rpx 76rpx 31rpx 31rpx;
  }
}
// ::v-deep.custom-placeholder {
//   color: #adacb1;
//   font-size: 14px;
// }
::v-deep.custom-placeholder {
  color: #0000004d;
  font-weight: 600;
  // font-size: 16px;
}
</style>
