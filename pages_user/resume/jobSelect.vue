<template>
  <view class="container">
    <l-select-3
      @selectValue="submit"
      v-model="value"
      :data="dataList"
      :options="options"
    ></l-select-3>
  </view>
</template>

<script>
import { handleTree } from "@/utils";

export default {
  components: {},
  data() {
    return {
      dataList: [],
      value: null,
      options: {
        id: "code",
        leftTitle: "name",
        child: "child",
        label: "name",
      },
    };
  },
  onLoad(options) {
    const jobType = options.jobType;
    if (jobType) {
      this.value = [jobType];
    }
  },
  onShow() {
    this.init();
  },
  methods: {
    async init() {
      const resp = await this.uniRequest.get("/dict/jobType");
      if (this.qUtil.validResp(resp)) {
        const dataList = resp.data;
        this.dataList = handleTree(dataList, "code", "parentCode", "child");
      }
    },
    submit(value) {
      this.storageUtil.setItem("currJobType", value[0]);
      // this.navigateUtil.back();
      uni.navigateBack({
        delta: 1,
        success: () => {
          uni.$emit("updateJobPosition", value[0]);
        },
      });
    },
    cancel() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.container {
  width: 100%;
  height: calc(100% - 140rpx);
}
</style>
