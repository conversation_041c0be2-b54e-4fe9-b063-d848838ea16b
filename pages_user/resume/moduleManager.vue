<template>
  <view class="moduleManager">
    <view class="text">已有模块 <text>（长按模块拖动排序）</text></view>
    <sortList
      ref="sort"
      v-if="SelectedData.length > 0"
      :list="SelectedData"
      :lineHeight="lineHeight"
      @change="changeSort"
      @editTit="editTit"
      @delete="handleDelete"
    />
    <view class="text">可添加模块</view>
    <view class="list">
      <view class="list_item" v-for="(item, i) in UncheckedData" :key="i">
        <view class="list_item_left">
          <!-- <image src="" mode="widthFix" /> -->
          <text>{{ item.moduleName }}</text>
        </view>
        <image
          class="list_item_right"
          :src="setImg('images/resume/icon_adds.png')"
          mode="widthFix"
          @click="handleAdd(i, item)"
        />
      </view>
    </view>
    <EditModal ref="editModal" :show="editTitFlag" @save="handleTitSave" />
  </view>
</template>
<script>
import global from "@/common/global";
import EditModal from "./components/editModal.vue";
import sortList from "./components/sortList.vue";
import { GET_RESUME_DATA, SET_RESUME_SORT } from "@/api/resume.js";
export default {
  data() {
    return {
      SelectedData: [
        {
          moduleName: "个人信息",
          disable: true,
          id: 0,
        },
      ],
      UncheckedData: [],
      allData: [],
      id: "",
      startX: 0,
      startY: 0,
      currentIndex: -1,
      editTitFlag: false,
      editInd: null,
      lineHeight: 51,
    };
  },
  components: {
    EditModal,
    sortList,
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    setImg(url) {
      return global.STATIC_URL + url;
    },
    async loadUserInfo() {
      const userResp = await GET_RESUME_DATA();
      if (this.qUtil.validResp(userResp)) {
        this.id = userResp?.data?.id;
        let arr = userResp?.data?.modules || [];
        let SelectArr = [];
        let unArr = [];
        arr.forEach((item) => {
          if (item.show == 1) {
            SelectArr.push({
              ...item,
              id: item.moduleId,
              disable: false,
            });
          } else {
            unArr.push({
              ...item,
              id: item.moduleId,
            });
          }
        });
        this.allData = arr;
        this.UncheckedData = unArr;
        // this.SelectedData = [...this.SelectedData, ...SelectArr];
        this.SelectedData = [
          {
            moduleName: "个人信息",
            disable: true,
            id: 0,
          },
          ...SelectArr,
        ];
      }
    },
    handleTouchStart(event, index) {
      // console.log(event, "event", index);
      // this.currentIndex = index;
    },
    handleTouchMove() {},
    // 已选中移除
    handleDelete(ind, item) {
      this.UncheckedData.push({
        ...item,
        id: this.UncheckedData.length + 1,
      });
      const data = this.SelectedData.filter((item, i) => i !== ind);
      this.SelectedData = data;
      this.setArr("del", item);
    },
    changeSort(e) {
      const newData = e.filter((item) => !item.disable);
      // this.setListRes(newData);
      const AllData = [...newData, ...this.UncheckedData];
      this.setListRes(AllData, "sort");
      // console.log(e, "修改后的新排序");
    },
    //添加
    handleAdd(ind, item) {
      this.SelectedData.push({
        ...item,
        disable: false,
      });
      const data = this.UncheckedData.filter((item, i) => i !== ind);
      this.UncheckedData = data;
      this.setArr("add", item);
    },
    async setArr(type, item) {
      const reqModal = this.allData.map((el) => {
        if (el.moduleId === item.id) {
          return {
            ...el,
            show: type === "add" ? 1 : 2,
          };
        } else {
          return el;
        }
      });
      this.setListRes(reqModal);
    },
    async setListRes(data, type) {
      const result = await SET_RESUME_SORT({
        id: this.id,
        modules: data,
      });

      if (this.qUtil.validResp(result)) {
        uni.showToast({
          title: "调整成功",
          icon: "none",
        });
        if (type !== "sort") {
          this.$refs.sort.init();
          this.loadUserInfo();
        }
      }
    },
    editTit(item) {
      if (item.disable) return;
      this.editInd = item.id;
      this.$refs.editModal.showModal(item.moduleName);
    },
    //更改名称确定
    async handleTitSave(tit) {
      // uni.message({ title: "请输入名称", icon: "none" });
      this.allData.map((item) => {
        if (item.moduleId === this.editInd) {
          item.moduleName = tit;
        }
        return item;
      });
      this.SelectedData.map((item) => {
        if (item.id === this.editInd) {
          item.moduleName = tit;
        }
        return item;
      });
      const result = await SET_RESUME_SORT({
        id: this.id,
        modules: this.allData,
      });

      if (this.qUtil.validResp(result)) {
        uni.showToast({
          title: "修改成功",
          icon: "none",
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.moduleManager {
  padding: 0 32rpx;
  background-color: #f5f6f8;
  .text {
    font-size: 16px;
    color: #000;
    padding: 24rpx 0;
    text {
      color: #a8a8a8;
    }
  }
  .list {
    padding: 0 24rpx;
    background-color: #fff;
    &_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      font-size: 14px;
      // border-bottom: 1px solid #c3c3c3;
      &_left {
        display: flex;
        align-items: center;
        text {
          padding: 0 20rpx;
        }
      }
      image {
        width: 38rpx;
      }
    }
    &_item:last-child {
      border: 0;
    }
  }
}
</style>
