<template>
  <view class="container experience">
    <u-navbar title="创建简历" @leftClick="backPage" fixed></u-navbar>
    <view class="resume">
      <text class="resume_desc">选择求职意向，为您选择合适的模版</text>
      <!-- 求职意向 -->
      <view
        :class="
          'job_hunt' + (Object.keys(jobWant).length > 0 ? '' : ' job_hunt_none')
        "
        :style="Object.keys(jobWant).length > 0 || 'padding: 40rpx 32rpx;'"
        @click="goExpectationFn"
      >
        <view>
          <view>求职意向</view>
          <text class="job_hunt_text" v-if="wantArr.length > 0">
            <text v-for="(item, i) in wantArr" :key="i">
              {{ item + (wantArr.length - 1 === i ? "" : " | ") }}
            </text>
          </text>
        </view>
        <image :src="setImg('images/right_icon.png')" />
      </view>
    </view>
    <view class="footer">
      <button @click="backPage" class="fbtn0">取消</button>
      <button @click="nextFn" class="fbtn1">下一步</button>
    </view>
  </view>
</template>

<script>
import {
  GET_RESUME_DATA,
  CREATE_RESUME_TEMPLATE,
  GET_RESUME_TEMPLATE,
} from "@/api/resume.js";
import global from "@/common/global";
export default {
  data() {
    return {
      jobWant: {},
      wantArr: [],
      templateId: "",
      loading: false,
    };
  },
  onLoad(options) {
    this.loadUserInfo();
  },
  components: {},
  onReady() {},
  mounted() {
    this.loadUserInfo();
    uni.$on("updateResume", () => {
      this.loadUserInfo();
    });
  },
  methods: {
    setImg(url) {
      return global.STATIC_URL + url;
    },
    async nextFn() {
      if (this.wantArr.length === 0) {
        uni.$u.toast("请填写求职期望!");
        return;
      }
      if (this.loading) {
        uni.$u.toast("模版生成中请勿返回！");
        return;
      }
      this.loading = true;
      // 创建简历
      uni.showLoading({
        title: "模版生成中...",
      });
      if (this.templateId * 1 > 0) {
        const getTemplate = await GET_RESUME_TEMPLATE(this.templateId);

        if (getTemplate?.code === 200) {
          this.loading = false;
          if (this.systemInfo.platform === "android") {
            let localData = this.storageUtil.getItem("localData") || {};
            const url = encodeURIComponent(
              this.staticBaseUrl +
                "h5/webView/?token=" +
                localData.vuex_token +
                "&clientid=" +
                this.clientId +
                "&type=temp&id=" +
                getTemplate.data.id +
                "&url=" +
                getTemplate.data.url
            );
            this.navTo(
              `/pages_h5/webview/index?url=${url}&type=temp&id=${getTemplate.data.id}`
            );
          } else {
            this.navTo(
              `/pages_h5/webview/index?url=${encodeURIComponent(
                getTemplate.data.url
              )}&type=temp&id=${getTemplate.data.id}`
            );
          }
        }
      } else {
        const result = await CREATE_RESUME_TEMPLATE();
        if (result?.code === 200) {
          this.loading = false;
          if (this.systemInfo.platform === "android") {
            let localData = this.storageUtil.getItem("localData") || {};
            const url = encodeURIComponent(
              this.staticBaseUrl +
                "h5/webView/?token=" +
                localData.vuex_token +
                "&clientid=" +
                this.clientId +
                "&type=temp&id=" +
                result.data.id +
                "&url=" +
                result.data.url
            );
            this.navTo(
              `/pages_h5/webview/index?url=${url}&type=temp&id=${result.data.id}`
            );
          } else {
            this.navTo(
              `/pages_h5/webview/index?url=${encodeURIComponent(
                result.data.url
              )}&type=temp&id=${result.data.id}`
            );
          }
          uni.hideLoading();
        }
      }
    },
    goExpectationFn() {
      // 跳转期望岗位
      if (Object.keys(this.jobWant).length === 0) {
        this.navTo(`/pages_user/resume/expectation`);
      } else if (this.jobWant?.id) {
        this.navTo(`/pages_user/resume/expectation?id=${this.jobWant?.id}`);
      }
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
    setJobWant(want) {
      let wantArr = [];
      //求职期望列表展示
      if (Object.keys(want || {}).length !== 0) {
        if (want?.workType) {
          wantArr.push(want?.workType);
        }
        if (want?.jobTypeLabel) {
          wantArr.push(want?.jobTypeLabel);
        }
        if (want?.city) {
          wantArr.push(want?.city);
        }
        if (want?.startSalary && want?.endSalary) {
          wantArr.push(`${want?.startSalary}-${want?.endSalary}`);
        }
        if (want?.industryLabel) {
          wantArr.push(want?.industryLabel);
        }
      } else {
        wantArr = [];
      }

      this.wantArr = wantArr;
    },
    async loadUserInfo() {
      const userResp = await GET_RESUME_DATA();
      if (this.qUtil.validResp(userResp) && userResp.code === 200) {
        this.templateId = userResp.data.templateId || "";
        this.jobWant = userResp.data.jobWant || {};
        this.setJobWant(this.jobWant);
      }
    },
    backPage() {
      if (this.loading) {
        uni.$u.toast("模版生成中请勿返回！");
        return;
      } else {
        uni.navigateBack();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.resume {
  flex: 1;
  background-color: #fff;
  padding: 32rpx;
  box-sizing: border-box;
  &_desc {
    font-size: 16px;
    color: #000;
    display: inline-block;
  }
  .job_hunt {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 99%;
    // height: 128rpx;
    padding: 22rpx 32rpx;
    font-weight: 600;
    box-sizing: border-box;
    border-radius: 20rpx;
    margin: 47rpx auto auto;
    line-height: 48rpx;
    box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
    > image {
      width: 50rpx;
      height: 50rpx;
    }
    &_desc {
      display: flex;
      align-items: center;
    }
    &_desc text {
      background-color: #d9dff3;
      padding: 0 8rpx;
      border-radius: 4rpx;
      color: #18C2A5;
      font-size: 24rpx;
      line-height: 36rpx;
      margin-left: 16rpx;
    }
    &_text {
      display: inline-block;
      width: 550rpx;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      font-size: 12px;
      line-height: 36rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .job_hunt_none {
    background-color: #f0f0f0;
    &_text {
      color: #a8a8a8;
      font-size: 22rpx;
      font-weight: 400;
    }
  }
}

.footer {
  width: 100%;
  height: auto;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx 50rpx 30rpx;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.06);
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // z-index: 10;

  button {
    height: 80rpx;
    line-height: 80rpx;
    border: 0;
    border-radius: 4px;

    &::after {
      display: none;
    }
  }

  .fbtn0 {
    width: 48%;
    background: #f6f7f9;
    font-size: 30rpx;
    color: #333;
  }

  .fbtn {
    width: 100%;
    background: #18C2A5;
    font-size: 30rpx;
    color: #fff;
  }

  .fbtn1 {
    width: 48%;
    background: #18C2A5;
    font-size: 30rpx;
    color: #fff;
  }
}
</style>
