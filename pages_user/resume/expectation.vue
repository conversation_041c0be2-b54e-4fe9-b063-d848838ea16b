<template>
  <view class="page-expectation">
    <view class="mainForm">
      <u-form
        :model="form"
        :rules="rules"
        labelPosition="top"
        label-width="100"
        ref="uForm"
      >
        <!-- <u-form-item label="期望薪资" borderBottom prop="university" >
          <u--input v-model="form.university" border="none" placeholder="请输入"></u--input>
        </u-form-item> -->
        <!-- <text class="title">{{ id > 0 ? "编辑" : "添加" }}求职意向</text> -->
        <!-- <view class="subsection-box">
          <text>求职类型</text>
          <view class="subsection">
            <view
              v-for="(item, index) in list"
              :key="index"
              @click="subsectionFn(item, index)"
              :class="subsectionInd == index ? 'active' : ''"
              >{{ item.name }}</view
            >
          </view>
        </view> -->

        <u-form-item
          label="岗位类型"
          borderBottom
          prop="workTypeLabel"
          @click="workTypeShow = true"
        >
          <view class="form-inner">
            <text
              :class="form.workTypeLabel ? 'font-bold' : 'custom-placeholder'"
            >
              {{ form.workTypeLabel || "请选择" }}
            </text>
            <image
              class="right-icon"
              :src="setImg('images/right_icon.png')"
              mode="widthFix"
            />
          </view>
        </u-form-item>

        <u-picker
          title="岗位类型"
          :show="workTypeShow"
          :columns="[workTypeList]"
          :defaultIndex="[workTypeDefaultIndex]"
          @confirm="workTypeSelect"
          keyName="text"
          :immediateChange="true"
          confirmColor="#18C2A5"
          @cancel="workTypeShow = false"
        ></u-picker>

        <!-- <u-form-item
          label="期望薪资"
          borderBottom
          prop="salary"
          @click="salaryShow = true"
        >
          <view class="form-inner">
            <text :class="form.salary ? 'font-bold' : 'custom-placeholder'">{{
              form.salary || "请选择"
            }}</text>
            <image
              class="right-icon"
              :src="setImg('images/right_icon.png')"
              mode="widthFix"
            />
          </view>
        </u-form-item> -->
        <u-form-item
          label="期望城市"
          borderBottom
          prop="city"
          @click="openCity"
        >
          <view class="form-inner">
            <view class="w-full custom-placeholder" v-if="!form.city">
              请选择
            </view>
            <template v-else>
              <view class="flex flex-wrap gap-2">
                <view
                  class="tag sm"
                  v-for="(item, index) in cityList"
                  :key="index"
                  @click.stop="() => {}"
                >
                  <text>{{ item }}</text>
                  <text
                    class="iconfont icon-close text-primary"
                    @click.stop="deleteCity(index)"
                  ></text>
                </view>
                <view class="tag sm gray">
                  <text class="iconfont icon-add"></text>
                  <text>添加</text>
                </view>
              </view>
            </template>

            <!-- <text class="font-bold" v-else>
              {{ form.city }}
            </text> -->
            <image
              class="right-icon"
              :src="setImg('images/right_icon.png')"
              mode="widthFix"
            />
          </view>
        </u-form-item>
        <u-form-item
          label="期望职位"
          borderBottom
          prop="jobTypeLabel"
          @click="getJob"
        >
          <view class="form-inner">
            <text
              :class="form.jobTypeLabel ? 'font-bold' : 'custom-placeholder'"
              >{{ form.jobTypeLabel || "请选择" }}</text
            >
            <image
              class="right-icon"
              :src="setImg('images/right_icon.png')"
              mode="widthFix"
            />
          </view>
        </u-form-item>
        <u-form-item
          label="期望行业"
          borderBottom
          prop="industryLabel"
          @click="getIndustry"
        >
          <view class="form-inner">
            <text
              :class="form.industryLabel ? 'font-bold' : 'custom-placeholder'"
              >{{ form.industryLabel || "请选择" }}</text
            >
            <image
              class="right-icon"
              :src="setImg('images/right_icon.png')"
              mode="widthFix"
            />
          </view>
        </u-form-item>
        <u-form-item
          label="企业偏好"
          borderBottom
          prop="natureLabel"
          @click="natureShow = true"
        >
          <view class="form-inner">
            <text
              :class="form.natureLabel ? 'font-bold' : 'custom-placeholder'"
              >{{ form.natureLabel || "请选择" }}</text
            >
            <image
              class="right-icon"
              :src="setImg('images/right_icon.png')"
              mode="widthFix"
            />
          </view>
        </u-form-item>
      </u-form>
    </view>
    <view class="footbar">
      <button @click="cancel" class="fbtn0">
        {{ id > 0 ? "删除" : "取消" }}
      </button>
      <button @click="submit" class="fbtn1">保存</button>
    </view>
    <SalaryPop
      :show="salaryShow"
      @cancel="salaryShow = false"
      @confirm="salarySelect"
      :salaryVal="{
        startSalary: id > 0 ? form.startSalary : '',
        endSalary: id > 0 ? form.endSalary : '',
      }"
    />
    <barry-picker ref="cityPicker" @get-address="getCity"></barry-picker>

    <u-picker
      title="企业偏好"
      :show="natureShow"
      :columns="[natureList]"
      :defaultIndex="natureDefaultIndex"
      @confirm="natureSelect"
      :immediateChange="true"
      confirmColor="#18C2A5"
      @cancel="natureShow = false"
    ></u-picker>
  </view>
</template>

<script>
import storageUtil from "../../utils/storageUtil";
import { DELETE_JOBWANT_DATA, GET_JOB_WANT } from "@/api/resume.js";
import SalaryPop from "./components/salaryPop.vue";
import { checkLogin } from "@/utils/auth";
import global from "@/common/global";
export default {
  data() {
    return {
      // form: {},
      form: {
        salary: null,
        startSalary: null,
        endSalary: null,
        city: null,
        jobTypeLabel: null,
        jobType: null,
        industryLabel: null,
        industry: null,
        natureLabel: null,
        nature: null,
        workTypeLabel: null,
        workType: null,
        remark: null,
      },
      selectIndex: [0, 0],
      subsectionInd: 0,
      industryList: [],
      salaryShow: false,
      natureShow: false,
      natureList: [
        { id: 0, text: "不限" },
        { id: 10, text: "央国企优先" },
        { id: 20, text: "非央国企优先" },
      ],
      workTypeShow: false,
      workTypeList: [
        { id: 10, text: "校园招聘" },
        { id: 20, text: "实习" },
        { id: 0, text: "校园招聘+实习" },
      ],
      id: "",
      list: [
        {
          name: "全职",
        },
        {
          name: "兼职",
        },
      ],
      rules: {
        salary: [
          {
            required: true,
            message: "请选择期望薪资",
            trigger: "blur,change",
          },
        ],
        city: [
          {
            required: true,
            message: "请选择期望城市",
            trigger: "blur,change",
          },
        ],
        jobTypeLabel: [
          {
            required: true,
            message: "请选择期望职位",
            trigger: "blur,change",
          },
        ],
        industryLabel: [
          {
            required: true,
            message: "请选择期望行业",
            trigger: "blur,change",
          },
        ],
        natureLabel: [
          {
            required: false,
            message: "请选择企业偏好",
            trigger: "blur,change",
          },
        ],
        workTypeLabel: [
          {
            required: false,
            message: "请选择岗位类型",
            trigger: "blur,change",
          },
        ],
      },
    };
  },
  components: {
    SalaryPop,
  },
  computed: {
    cityList() {
      return this.form.city ? this.form.city.split(",") : [];
    },
    natureDefaultIndex() {
      if (!this.form.nature) {
        return [0];
      }
      const index = this.natureList.findIndex(
        (item) => item.id === this.form.nature
      );
      return [index >= 0 ? index : 0];
    },
    workTypeDefaultIndex() {
      if (!this.form.workType) {
        return [0];
      }
      const index = this.workTypeList.findIndex(
        (item) => item.id === this.form.workType
      );
      return [index >= 0 ? index : 0];
    },
  },
  async onLoad(options) {
    if (options && options.id) {
      this.loadData(options.id);
      this.id = options.id;
    }
  },
  onShow() {
    checkLogin(true);
    let industryIds = storageUtil.getItem("industryIds");
    let currJobType = storageUtil.getItem("currJobType");

    if (industryIds) {
      this.industryList = industryIds;
      this.form.industry = industryIds.map((v) => v.id).join(",");
      this.form.industryLabel = industryIds.map((v) => v.name).join(",");
      storageUtil.removeItem("industryIds");
    }
    if (currJobType) {
      this.form.jobType = currJobType.id;
      this.form.jobTypeLabel = currJobType.name;
      storageUtil.removeItem("currJobType");
    }
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    deleteCity(index) {
      const list = this.cityList.filter((v, i) => i !== index);
      this.form.city = list.join(",");
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    salarySelect(val) {
      if (val.endSalary === "面议") {
        this.form.salary = "面议";
        this.form.startSalary = "面议";
        this.form.endSalary = "面议";
      } else {
        this.form.salary =
          val.startSalary && val.endSalary
            ? val.startSalary + "k-" + val.endSalary + "k"
            : "";
        this.form.startSalary = val.startSalary || "";
        this.form.endSalary = val.endSalary || "";
      }
      this.salaryShow = false;
    },
    openCity() {
      this.$refs.cityPicker.show = true;
    },
    getCity(e) {
      if (this.cityList.includes(e)) {
        uni.showToast({
          title: `已存在城市：${e}`,
          icon: "none",
        });
        return;
      }
      if (this.form.city) {
        this.form.city += "," + e;
      } else {
        this.form.city = e;
      }
    },
    getJob() {
      uni.navigateTo({
        url: "/pages_user/resume/jobSelect?jobType=" + this.form.jobType,
      });
    },
    getIndustry() {
      uni.navigateTo({
        url:
          "/pages_user/resume/industrySelect?industry=" +
          (this.form.industry === null ? "" : this.form.industry),
      });
    },
    natureSelect(e) {
      console.log("natureSelect", e);
      const selected = e.value[0];
      if (selected) {
        this.form.nature = selected.id;
        this.form.natureLabel = selected.text;
      } else {
        this.form.nature = this.natureList[0].id;
        this.form.natureLabel = this.natureList[0].text;
      }
      this.natureShow = false;
    },
    workTypeSelect(e) {
      console.log(e);
      const selected = e.value[0];
      if (selected) {
        this.form.workType = selected.id;
        this.form.workTypeLabel = selected.text;
      } else {
        this.form.workType = this.workTypeList[0].id;
        this.form.workTypeLabel = this.workTypeList[0].text;
      }
      this.workTypeShow = false;
    },
    subsectionFn(data, index) {
      this.subsectionInd = index;
      this.form.workType = data.name;
    },
    async loadData(id) {
      if (id < 0) {
        return;
      }
      // uni.showLoading({
      //   title: "加载中",
      // });
      let res = await GET_JOB_WANT(id);
      if (this.qUtil.validResp(res)) {
        this.form = res.data;
        this.subsectionInd = res.data.workType === "全职" ? 0 : 1;
        if (res.data.startSalary == "面议" && res.data.endSalary == "面议") {
          this.form.salary = "面议";
        } else {
          this.form.salary =
            res.data.startSalary + "k-" + res.data.endSalary + "k";
        }
        if (res.data.workType) {
          const selectedWorkType = this.workTypeList.find(
            (item) => item.id == res.data.workType
          );
          if (selectedWorkType) {
            this.form.workTypeLabel = selectedWorkType.text;
            this.form.workType = Number(selectedWorkType.id);
          }
        }
      }
      // uni.hideLoading();
    },
    submit() {
      const that = this;
      this.$refs.uForm.validate().then(async (valid) => {
        if (valid) {
          let data = {
            id: this.form.id,
            startSalary: this.form.startSalary,
            endSalary: this.form.endSalary,
            city: this.form.city,
            jobType: this.form.jobType,
            industry: this.form.industry,
            remark: this.form.remark,
            nature: this.form.nature,
            workType: this.form.workType,
          };
          let resp = await that.uniRequest[data.id > 0 ? "put" : "post"](
            "/member/jobWant",
            data
          );
          if (that.qUtil.validResp(resp)) {
            uni.$u.toast("保存成功！");
            uni.navigateBack({
              delta: 1,
              success: () => {
                uni.$emit("updateResume");
              },
            });
          } else {
            uni.showToast({
              icon: "none",
              title: resp.msg || "提交失败！",
            });
          }
        } else {
        }
      });
    },
    async cancel() {
      if (this.id > 0) {
        const result = await DELETE_JOBWANT_DATA(this.id);
        if (this.qUtil.validResp(result)) {
          uni.navigateBack({
            delta: 1,
            success: () => {
              uni.$emit("updateResume");
            },
          });
        }
      } else {
        uni.navigateBack({
          delta: 1,
        });
      }
    },
  },
};
</script>

<style lang="scss">
page {
  background: #fff;
}
.right-icon {
  width: 32rpx;
}
.popupBox {
  padding: 30rpx;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.1);

  ::v-deep .u-checkbox-group {
    flex-wrap: wrap !important;

    .u-checkbox {
      margin: 20rpx 20rpx;
    }
  }

  .pop_tit {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }

  .inputbox {
    flex: 1;
    height: 24px;
    line-height: 24px;
    text-align: right;
    padding-right: 15rpx;
    display: flex;
    justify-content: flex-end;

    .ip {
      color: rgb(192, 196, 204);
      font-size: 16px;
    }

    .ic {
      color: #1c819f;
      font-size: 16px;
    }
  }

  .pop_tips {
    font-size: 26rpx;
    color: #999;
    text-align: left;
    padding: 15rpx 20rpx;
  }

  .pop_bt {
    padding-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    button {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 100rpx;
      font-size: 30rpx;
      color: #fff;
      margin: 0;
      padding: 0;

      &::after {
        display: none;
      }

      &.fbtn0 {
        background: -webkit-linear-gradient(top, #999, #666);
        width: 35%;
      }

      &.fbtn {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 100%;
      }

      &.fbtn1 {
        background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
        width: 60%;
      }
    }
  }

  &.careerPop {
    width: 660rpx;
  }
}
.title {
  font-size: 18px;
  font-weight: 600;
  padding-top: 32rpx;
}
.subsection-box {
  display: flex;
  align-items: center;
  padding: 38rpx 0 56rpx 0;
  justify-content: space-between;
}
.subsection {
  display: inline-block;
  background-color: #000;
  display: flex;
  border-radius: 8rpx;
  height: 64rpx;
  box-sizing: border-box;
  background-color: #fff;
  border: 2rpx solid #18c2a5;

  view {
    line-height: 64rpx;
    font-size: 14px;
    padding: 0 36rpx;
    color: #18c2a5;
  }
  .active {
    color: #fff;
    background-color: #18c2a5;
  }
}

.custom-placeholder {
  color: #bdbdbd;
  font-size: 32rpx;
}
.form-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
  margin-top: 4rpx;
}
</style>
