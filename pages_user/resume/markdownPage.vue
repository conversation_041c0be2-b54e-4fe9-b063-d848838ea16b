<template>
  <view class="markdownView">
    <zero-markdown-view
      :markdown="markdownContent"
      type="html"
    ></zero-markdown-view>
  </view>
</template>
<script>
import { GET_RESULT_DATA } from "@/api/resume.js";
export default {
  data() {
    return {
      markdownContent: "",
      type: "html",
    };
  },

  onLoad(opt) {
    if (opt.id) {
      this.getLoadData(opt.id, opt.type);
    }
  },
  methods: {
    async getLoadData(id, type) {
      this.id = id;
      this.type = "html";

      const result = await GET_RESULT_DATA(id);
      if (this.qUtil.validResp(result) && result.code === 200) {
        if (type === "industry") {
          // 行业
          uni.setNavigationBarTitle({
            title: "行业分析报告",
          });
          this.type = "industry";
          this.markdownContent = result.data.data.report.replace(/\n/g, "\n\n");
        } else if (type === "interview") {
          // 面试
          uni.setNavigationBarTitle({
            title: "面试题列表",
          });
          this.markdownContent = result.data.data.mockQuestions.replace(
            /\n/g,
            "\n\n  "
          );
        } else if (type === "exam") {
          // 笔试
          uni.setNavigationBarTitle({
            title: "笔试题列表",
          });
          this.markdownContent = result.data.data.examQuestions.replace(
            /\n/g,
            "\n\n"
          );
        } else if (type === "careerPlan") {
          // 职业规划
          uni.setNavigationBarTitle({
            title: "职业规划报告",
          });
          this.markdownContent = result.data.suggestion.replace(/\n/g, "\n\n");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.markdownView {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fff;
  padding: 15rpx 30rpx 40rpx 30rpx;
  box-sizing: border-box;
}
</style>
