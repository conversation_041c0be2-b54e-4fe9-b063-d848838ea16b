<template>
  <view :class="['jobMatchMain', { hiddenBox: popupShow }]">
    <u-navbar title="岗位匹配结果" @leftClick="backPage" fixed></u-navbar>
    <Empty
      v-if="jobData.length === 0 && noData"
      text="未匹配到岗位，请调整求职意向"
      textColor="#000"
      desc="本次匹配不消耗次数"
    />
    <view class="jobSearchDesc" v-if="jobData.length > 0 && !noData">
      <view>查询时间：{{ endTime }}</view>
      <view>匹配结果：{{ total }}条</view>
    </view>
    <view class="jobMatchList" v-if="jobData.length > 0 && !noData">
      <job-match-card
        v-for="(item, index) in jobData"
        :key="index"
        :item="item"
        :ListId="id"
        @load="collectionStatus"
        @popupFn="popupFn"
      />
    </view>
  </view>
</template>
<script>
import Empty from "@/components/empty/index.vue";
import { GET_RESULT_DATA } from "@/api/resume.js";
import JobMatchCard from "@/pages_user/resume/components/jobMatchCard.vue";
export default {
  mixins: [],
  props: {},
  components: { Empty, JobMatchCard },
  data() {
    return {
      total: 0,
      jobData: [],
      endTime: "",
      id: "",
      popupShow: false,
      noData: false,
    };
  },
  onLoad(opt) {
    if (opt.type) {
      this.type = opt.type;
    }
    if (opt.id) {
      this.id = opt.id;
      this.getJobList(opt.id);
    }
  },
  computed: {},
  methods: {
    async getJobList(id) {
      const result = await GET_RESULT_DATA(id);
      if (this.qUtil.validResp(result) && result.code === 200) {
        this.endTime = result.data.endTime || "";
        this.jobData = result.data?.data?.positions || [];
        this.total = this.jobData.length || 0;

        if (this.total === 0) {
          this.noData = true;
        }
      }
    },
    backPage() {
      if (this.type === "OA") {
        uni.redirectTo({
          url: `/pages_chat/chat/index?chatScene=S6`,
        });
      } else {
        uni.navigateBack({
          delta: 1,
        });
      }
    },
    popupFn(data) {
      this.popupShow = data;
    },
    collectionStatus(el) {
      this.jobData.map((item) =>
        item.id == el.id ? (item.collection = !item.collection) : item
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.jobMatchMain {
  height: 100vh;
  background-color: #f7f7f7;
  .jobSearchDesc {
    font-size: 12px;
    padding: 29rpx 29rpx;
    color: rgba(0, 0, 0, 0.9);
    > view:last-child {
      margin-top: 6rpx;
    }
  }
  .jobMatchList {
    padding-bottom: 50rpx;
  }
}
.hiddenBox {
  height: 100vh;
  overflow: hidden;
}
</style>
