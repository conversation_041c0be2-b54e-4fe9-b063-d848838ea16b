<template>
  <view class="RecordsMain">
    <records-card
      v-for="(item, index) in data"
      :Item="item"
      @currentFn="currentFn"
      :currentIndex="index"
      :key="index"
    />
    <text class="RecordsText">仅保留最近3次优化结果</text>
  </view>
</template>
<script>
import RecordsCard from "./components/RecordsCard.vue";
import { RESULT_LIST } from "@/api/resume.js";
export default {
  mixins: [],
  props: {},
  components: { RecordsCard },
  computed: {},
  data() {
    return {
      data: [
        {
          key: "S5",
          title: "简历优化结果",
          show: false,
          list: [],
        },
        {
          key: "S6",
          title: "岗位匹配结果",
          show: false,
          list: [],
        },
        {
          key: "S8",
          title: "笔试指导",
          show: false,
          list: [],
        },
        {
          key: "S7",
          title: "面试指导",
          show: false,
          list: [],
        },
        {
          key: "S4",
          title: "行业分析",
          show: false,
          list: [],
        },
        {
          key: "S3",
          title: "职业规划",
          show: false,
          list: [],
        },
        // {
        //   key: "S2",
        //   title: "offer分析",
        //   show: false,
        //   list: [],
        // },
      ],
    };
  },
  computed: {},
  methods: {
    // 查询数据
    async currentFn(ind) {
      let res = await RESULT_LIST(this.data[ind].key);
      if (this.qUtil.validResp(res) && res.code === 200) {
        this.data[ind].list = res.data;
        if (res.data.length > 0) {
          this.data[ind].show = !this.data[ind].show;
        } else {
          uni.$u.toast("暂无数据");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.RecordsMain {
  height: auto;
  min-height: 100vh;
  padding: 30rpx 32rpx;
  background-color: #fff;
  .RecordsText {
    width: 100%;
    display: inline-block;
    font-size: 12px;
    color: #b2b2b2;
    text-align: center;
    padding: 32rpx 0;
  }
}
</style>
