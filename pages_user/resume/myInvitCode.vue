<template>
  <view class="container my-inviteCode">
    <u-navbar leftText="我的邀请码" @leftClick="backPage" fixed></u-navbar>
    <view class="alert-box">
      <image
        class="alert-img"
        :src="setImg(`/images/myUser/notification.png`)"
        mode="widthFix"
      ></image>
      <view class="alert-text">
        加入官方社群，抢先体验最新功能，解锁更多用户特权！
      </view>
    </view>
    <view class="main-container">
      <view class="invite-code-box">
        <view class="invite-code-title">
          <text :class="{ 'text-primary': total > 0 }">{{ total }}</text>
          个可使用
        </view>
        <view
          class="invite-welfare mt-4"
          @click="navTo('/pages/match/index?scene=ExactMatch')"
          :style="{
            backgroundImage: `url(${setImg(`/images/myUser/welfare-bg.png`)})`,
          }"
        >
          <image class="icon" :src="setImg(`/images/home/<USER>"></image>
          <view class="details">
            <text>完成1次精准匹配，即可免费获得2个专属邀请码！</text>
            <view class="btn primary small">去匹配</view>
          </view>
        </view>
        <view class="invite-code-list mt-4">
          <view
            class="invite-code-item"
            v-for="(item, index) in inviteCodeList"
            :key="index"
          >
            <view class="invite-code-item-header">
              <text class="invite-code-item-label">邀请码</text>
              <text
                class="invite-code-item-status"
                :class="{ used: item.status !== 1 }"
              >
                {{
                  item.status === 1
                    ? "未使用"
                    : item.status === 2
                    ? "已使用"
                    : "已过期"
                }}
              </text>
            </view>
            <view class="invite-code-item-content">
              <view class="invite-code-item-info">
                <text
                  class="invite-code-item-text"
                  :class="{ used: item.status !== 1 }"
                >
                  {{ item.code }}
                </text>
                <text>到期时间: {{ item.expireTime }}</text>
                <text v-if="item.status === 2">
                  使用时间: {{ item.usedTime }}
                </text>
              </view>
              <view
                class="invite-code-item-btn"
                v-if="item.status === 1"
                @click="copyFn(item.code)"
              >
                复制
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import global from "@/common/global";
import { GET_INVITE_CODE } from "@/api/resume.js";
export default {
  data() {
    return {
      total: 2,
      inviteCodeList: [],
    };
  },
  onLoad(options) {
    this.getData();
  },
  methods: {
    getData() {
      uni.showLoading();
      GET_INVITE_CODE()
        .then((res) => {
          if (this.qUtil.validResp(res) && res.code === 200) {
            this.inviteCodeList = res.data;
          }
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    backPage() {
      uni.navigateBack();
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
    copyFn(code) {
      uni.setClipboardData({
        data: code,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #fff;
  padding: 30rpx 0;
  box-sizing: border-box;
}
.line {
  background: linear-gradient(90deg, #1d2129 0%, #3d3823 95.15%);
  color: #fff;
}
.alert-box {
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #fff7e8;
  color: #ff7d00;
  font-size: 28rpx;
  .alert-img {
    width: 32rpx;
    height: 32rpx;
  }
  .alert-text {
    flex: 1;
  }
}
.main-container {
  flex: 1;
  overflow: auto;
  padding: 32rpx;
}
.invite-code-title {
  color: #000;
  font-size: 32rpx;
  font-weight: 500;
}
.invite-code-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.invite-code-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  background: #f7f8fa;
  padding: 20rpx 32rpx;
  border-radius: 16rpx;
  box-sizing: border-box;

  &-header {
    display: flex;
    justify-content: space-between;
  }

  &-label {
    font-size: 28rpx;
    color: #86909c;
  }

  &-status {
    padding: 0 8rpx;
    font-size: 24rpx;
    line-height: 36rpx;
    color: #00b42a;
    background: #e8ffea;
    border-radius: 8rpx;
    &.used {
      color: #86909c;
      background: #f2f3f5;
    }
  }

  &-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-text {
    font-size: 40rpx;
    color: #1d2129;
    font-weight: 500;
    &.used {
      color: #86909c;
    }
  }

  &-info {
    display: flex;
    flex-direction: column;
    font-size: 24rpx;
    color: #86909c;
    gap: 8rpx;
  }

  &-btn {
    display: flex;
    width: 196rpx;
    padding: 7rpx 0;
    justify-content: center;
    align-items: center;
    gap: 8rpx;
    border-radius: 84rpx;
    border: 1px solid #18c2a5;
    background: #18c2a5;
    color: #fff;
    font-size: 24rpx;
  }
}
.invite-welfare {
  height: 184rpx;
  position: relative;
  padding: 20rpx 32rpx 20rpx 16rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
  background-size: cover;
  display: flex;
  align-items: center;
  gap: 8rpx;
  .icon {
    width: 128rpx;
    height: 128rpx;
  }
  .details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    font-size: 28rpx;
    font-weight: 500;
    .btn {
      width: 196rpx;
    }
  }
}
</style>
