<template>
  <view>
    <u-navbar
      :leftText="scene === 'collection' ? '我的收藏' : '我的投递'"
      @leftClick="backPage"
      fixed
    ></u-navbar>
    <template v-if="jobData.length === 0 && loaded">
      <Empty text="暂无数据" />
    </template>
    <view class="p-4 flex flex-col gap-4" v-else>
      <JobCard v-for="(item, index) in jobData" :key="index" :item="item" />
      <u-loadmore :status="loading ? 'loading' : loaded ? 'nomore' : ''" />
    </view>
  </view>
</template>
<script>
import Empty from "@/components/empty/index.vue";
import { COLLECT_LIST } from "@/api/resume.js";
// import JobMatchCard from "@/pages_user/resume/components/jobMatchCard.vue";
import JobCard from "@/components/jobCard.vue";
export default {
  mixins: [],
  props: {},
  components: { Empty, JobCard },
  data() {
    return {
      scene: "collection",
      jobData: [],
      pageNum: 1,
      pageSize: 10,
      totalPage: null,
      popupShow: false,
      loaded: false,
      loading: false,
    };
  },
  onLoad(options) {
    this.scene = options.scene || "collection";
    this.getJobList();
  },
  onReachBottom() {
    if (this.loaded || this.loading) {
      return;
    }
    this.getJobList(this.pageNum + 1);
  },
  onPullDownRefresh() {
    this.pageNum = 1;
    this.getJobList(1);
  },
  computed: {},
  methods: {
    backPage() {
      uni.navigateBack();
    },
    async getJobList(page = 1) {
      if (this.loading) return;
      try {
        if (page === 1) {
          this.jobData = [];
          this.totalPage = 0;
        }
        this.loading = true;
        const result = await COLLECT_LIST({
          pageNum: page,
          pageSize: this.pageSize,
          scene: this.scene,
        });
        if (this.qUtil.validResp(result) && result.code === 200) {
          this.pageNum = page;
          const dataList = result.rows || [];
          this.jobData = [...this.jobData, ...dataList];
          this.totalPage = result.total;
          this.loaded = this.jobData.length === this.totalPage;
        }
      } catch (error) {
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },
    popupFn(data) {
      this.popupShow = data;
    },
    collectionStatus(el) {
      this.jobData.map((item) =>
        item.id == el.id ? (item.collection = !item.collection) : item
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.jobMatchMain {
  height: 100vh;
  background-color: #f7f7f7;
  padding-top: 12rpx;
  .jobMatchList {
    padding-bottom: 50rpx;
  }
}
.hiddenBox {
  height: 100vh;
  overflow: hidden;
}
</style>
