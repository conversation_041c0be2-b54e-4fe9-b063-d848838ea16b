<template>
  <view :class="['container', 'myResume', { hiddenBox: workStatus.popupShow }]">
    <u-navbar title="我的简历" @leftClick="backPage" fixed></u-navbar>
    <view class="res_top">
      <view class="tl">
        <view
          class="name"
          @click="navTo('/pages_user/info/index?isResume=true')"
        >
          <text>{{ userInfo.realName || "未填写" }}</text>
          <image
            :src="setImg('images/resume/icon_xiugai.png')"
            mode="widthFix"
          ></image>
        </view>
        <view
          class="info"
          @click="navTo('/pages_user/info/index?isResume=true')"
        >
          <!--1年工作经验
          <text>·</text>-->
          <template v-if="userInfo.birthday">
            {{ $dayjs().diff(userInfo.birthday, "year") }}岁
          </template>
          <template v-else> 暂无年龄 </template>
          <text>·</text>
          {{ education || "" }}
        </view>
        <view
          class="tel"
          @click="navTo('/pages_user/info/index?isResume=true')"
        >
          <image
            src="/static/icon/icon_phone.svg"
            mode="widthFix"
            v-if="mobile"
          ></image>
          <text>{{ mobile || "" }}</text>
          <image
            v-if="userInfo.email"
            src="/static/icon/icon_mail.svg"
            mode="widthFix"
          ></image>
          <text>{{ userInfo.email || "" }}</text>
        </view>
      </view>
      <view class="tr">
        <button class="coverBtn_imgs">
          <image
            class="head_avatar"
            :src="userInfo.resumeAvatar || setImg('images/chat/headImg.png')"
            mode="aspectFill"
          ></image>
        </button>
        <!-- userInfo.resumeAvatar ||  -->

        <!-- <image
          class="head_avatar"
          :src="setImg('images/chat/headImg.png')"
          mode="aspectFill"
        ></image> -->
      </view>
    </view>
    <view class="res_box">
      <view class="res_tit">求职状态 </view>
      <view class="res_Status" @click="workStatus.popupShow = true">
        <view
          class="cd"
          :style="
            workStatus.currText
              ? ''
              : 'font-size:15px;font-weight:400;color:#0000004D;'
          "
          >{{ workStatus.currText || "请选择" }}</view
        >
        <view class="cr">
          <image :src="setImg('images/right_icon.png')" mode="widthFix"></image>
          <!-- <u-icon name="arrow-right" color="#999" size="16"></u-icon> -->
        </view>
      </view>
    </view>
    <view
      class="res_box"
      v-for="(item, i) in modules"
      :key="i"
      v-if="item.show == 1"
    >
      <view class="res_tit">
        <view class="tl">{{ item.moduleName }}</view>
        <view class="tr" @click="goToPage(item, 'add')">
          <image
            :src="
              item.templateId != 5
                ? setImg('images/resume/icon_add.png')
                : setImg('images/right_icon.png')
            "
            mode="widthFix"
          />
        </view>
      </view>
      <view
        v-if="item.templateId !== 5"
        v-for="el in item.items"
        :key="el.id"
        class="res_cell"
        @click="goToPage(item, 'edit', el.id)"
      >
        <view class="cl">{{ el.title || "" }}</view>
        <view class="cr">
          <text v-if="el.start && el.end"
            >{{ setTime(el.start) }}-{{ setTime(el.end) }}</text
          >
          <text v-else>继续填写，完善其他信息</text>
          <image
            style="width: 32rpx; display: inline-block"
            :src="setImg('images/right_icon.png')"
            mode="widthFix"
          />
          <!-- <u-icon name="arrow-right" color="#999" size="16"></u-icon> -->
        </view>
        <view class="cb" v-if="item.templateId == 1 || item.templateId == 2">
          {{ el.category || "" }}
          <text v-if="el.category && el.role">·</text>
          {{ el.role || "" }}
        </view>
        <view class="cf" v-if="item.templateId == 3">
          <view class="tit">{{ el.role || "" }}</view>
          <!-- <text v-if="el.content && el.role">·</text> -->
          <view style="color: #333" class="cc">{{ el.content || "" }}</view>
        </view>
        <view class="cc" v-if="item.templateId === 2">{{
          el.content || ""
        }}</view>
      </view>
      <view
        class="res_con"
        v-if="item.templateId == 5 && item.items.length > 0"
        @click="
          goToPage(
            item,
            item.items.length > 0 ? 'edit' : 'add',
            item.items.length > 0 ? item.items[0].id : ''
          )
        "
      >
        <!-- <zero-markdown-view
          :markdown="item.items[0].content || ''"
        ></zero-markdown-view> -->
        {{ item.items[0].content || "" }}
      </view>
    </view>

    <view class="footspace"></view>
    <view class="footnav">
      <view class="fitem" @click="navTo(`/pages_user/resume/moduleManager`)">
        <image :src="setImg('images/resume/model.png')" mode="widthFix" />
        <text>模块管理</text>
      </view>
      <view class="fitem" @click="preview('preview')">
        <image src="/static/icon/icon_preview.svg" mode="widthFix" />
        <text>预览简历</text>
      </view>
      <view class="fitem" @click="preview('download')">
        <image src="/static/icon/icon_download.svg" mode="widthFix" />
        <text>下载简历</text>
      </view>
    </view>
    <!--求职状态弹窗-->
    <u-popup
      :show="workStatus.popupShow"
      :round="10"
      :closeable="true"
      @close="workStatus.popupShow = false"
    >
      <view class="popupBox">
        <view class="pop_tit"> 求职状态 </view>
        <view class="pop_list">
          <view
            class="pop_item"
            @click="statusChange(itm.value)"
            v-for="(itm, idx) in workStatus.list"
            :key="idx"
          >
            <text>{{ itm.label }}</text>
            <image
              v-if="workStatus.curr === itm.value"
              :src="setImg('images/resume/selected.png')"
              mode="widthFix"
            />
            <image
              v-else
              class="default_img"
              :src="setImg('images/resume/default_select.png')"
              mode="widthFix"
            />
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import global from "@/common/global";
import { desensitizePhoneNumber, setTime } from "@/utils";
import {
  GET_RESUME_DATA,
  EDIT_INFO,
  RESUME_GENERATE_PDF,
} from "@/api/resume.js";
import { isMobile } from "@/utils/validate";
import { checkLogin } from "@/utils/auth";

export default {
  data() {
    return {
      status: "loadmore",
      userInfo: {},
      educationList: [],
      projectList: [],
      welfareList: [],
      jobWantList: [],
      workList: [],
      modules: [],
      workStatus: {
        popupShow: false,
        curr: null,
        currText: "请选择",
        list: [],
      },
      upload: {},
      historyInfo: {},
    };
  },
  onShow() {
    uni.startPullDownRefresh();
  },
  computed: {
    mobile() {
      const mobile = this.userInfo.mobile;
      return this.isMobile(mobile) ? desensitizePhoneNumber(mobile) : mobile;
    },
    wechat() {
      const wxNo = this.userInfo.wxNo;
      return this.isMobile(wxNo) ? desensitizePhoneNumber(wxNo) : wxNo;
    },
    education() {
      const education = this.educationList.sort(
        (a, b) => b.educationValue*1 - a.educationValue*1
      )[0];
      return education?.education || "暂无学历";
    },
    certificateList() {
      return this.userInfo?.certificate?.split(" ") || [];
    },
  },
  onPullDownRefresh() {
    this.init();
  },
  onLoad(options) {
    if (options) {
      this.historyInfo = options;
    }
  },
  methods: {
    setTime,
    isMobile,
    init() {
      if (checkLogin()) {
        this.loadData();
      }
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
    goToPage(item, type, id = "") {
      let url = "";
      let NewType = type;
      let NewId = id;
      //templateId 1 教育 2工作经历 3项目 4求职意向 5文本
      if (item.templateId === 1) {
        url = "education";
      } else if (item.templateId === 2) {
        url = "experience";
      } else if (item.templateId === 3) {
        url = "project";
      } else if (item.templateId === 5) {
        url = "qualification";
        if (item.items.length > 0) {
          NewType = "edit";
          NewId = item.items[0].id;
        } else {
          NewType = "add";
          NewId = "";
        }
      }
      this.navTo(
        `/pages_user/resume/${url}?mode=${
          item.module
        }&title=${encodeURIComponent(item.moduleName)}&moduleId=${
          item.moduleId
        }&type=${NewType}&id=${NewId}`
      );
    },
    async loadUserInfo() {
      const userResp = await GET_RESUME_DATA();
      if (this.qUtil.validResp(userResp)) {
        this.userInfo = userResp?.data?.baseInfo || {};
        this.modules = userResp?.data?.modules || [];
        // const userInfo = userResp.data;
        const educationList = userResp?.data.modules.filter(item => item.module == 'education')[0]?.items || [];
        this.setEducationSort(educationList)
        this.workStatus.curr = userResp?.data?.baseInfo?.workStatus || 0;
        this.workStatus.currText = this.workStatus.list.find(
          (w) => w.value === this.workStatus.curr
        )?.label;
        this.upload = userResp?.data?.upload || {};
      }
    },
    async setEducationSort(data = []){
      const education = await this.$store.dispatch(
        "getDict",
        "ai_member_education"
      );
      const educationList = data.map(item=>{
        return {
          education:item.category,
          educationValue: education.find(e => e.label == item.category)?.value
        }
      })
      this.educationList = educationList
    },
    async statusChange(val) {
      this.workStatus.currText = this.workStatus.list.find(
        (w) => w.value === val
      ).label;
      this.workStatus.curr = val;
      await EDIT_INFO({ workStatus: val });
      this.workStatus.popupShow = false;
    },
    async preview(type = "preview") {
      const PdfResult = await RESUME_GENERATE_PDF();
      if (!PdfResult.data?.url) {
        return uni.$u.toast("暂无PDF,请先使用AI顾问生成!");
      } else {
        if (type === "preview") {
          if (this.systemInfo.platform === "android") {
            let localData = this.storageUtil.getItem("localData") || {};
            const url = encodeURIComponent(
              this.staticBaseUrl +
                "h5/webView/?token=" +
                localData.vuex_token +
                "&clientid=" +
                this.clientId +
                "&type=preview"
            );
            this.navigateUtil.goto(
              `/pages_h5/webview/index?url=${url}&type=preview`
            );
          } else {
            this.navigateUtil.goto(
              `/pages_h5/webview/index?url=${PdfResult.data?.url}&type=preview`
            );
          }
        } else {
          wx.downloadFile({
            // 示例 url，并非真实存在
            url: PdfResult.data?.url,
            success: function (res) {
              const filePath = res.tempFilePath;
              wx.openDocument({
                filePath: filePath,
                showMenu: true,
                success: function (res) {
                  console.log("打开文档成功");
                },
              });
            },
          });
        }
      }
      // #ifdef MP-WEIXIN

      // #endif
    },
    download() {
      this.preview();
      // uni.$u.toast('功能开发中,暂不可用')
    },
    async loadData() {
      uni.showLoading({
        title: "加载中",
      });
      const workStatusList = await this.$store.dispatch(
        "getDict",
        "ai_member_work_status"
      );
      this.workStatus.list = workStatusList;
      uni.hideLoading();
      //数据查询完毕关闭下拉刷新
      uni.stopPullDownRefresh();
      this.loadUserInfo();
    },
    editFn() {
      this.editTit = true;
    },
    saveTit() {
      console.log(this.modalTit, "modalTit");
      this.editTit = false;
    },
    backPage() {
      if (this.historyInfo.navTo && this.historyInfo.navTo === "webView") {
        uni.redirectTo({
          url: `/pages_chat/chat/index?chatScene=S5&popFlag=true`,
        });
      } else {
        uni.navigateBack({
          delta: 1,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@mixin content {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 30rpx;
}

.myResume {
  .res_tit {
    font-size: 16px;
    color: #333;
    font-weight: 600;
    // margin-bottom:20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .edit {
      width: 30rpx;
    }
    .tl {
      flex: 1;
    }

    .tr {
      height: 43rpx;
      image {
        width: 43rpx;
        height: 43rpx;
      }
    }
  }

  .res_cell {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 0;
    border-bottom: 1rpx #eee solid;

    &:last-child {
      border: 0;
      padding-bottom: 0;
    }

    .cl {
      width: 370rpx;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行

      font-size: 28rpx;
      color: #333;
      font-weight: 600;
      // flex: 1;

      text {
        margin-right: 10rpx;
      }
    }

    .cr {
      display: flex;
      justify-content: flex-end;
      align-content: center;
      // margin: 5rpx 0 0 20rpx;

      text {
        font-size: 12px;
        color: #999;
        margin-right: 10rpx;
      }
      image {
        width: 43rpx;
      }
    }

    .cb {
      width: 100%;
      font-size: 12px;
      color: #666;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 20rpx 0 0 0;

      text {
        height: 30rpx;
        font-size: 50rpx;
        line-height: 0.6;
        color: #dbdbdb;
        margin: 0 10rpx;
      }
    }
    .cf {
      width: 100%;
      font-size: 12px;
      color: #666;
      padding: 20rpx 0 0 0;
      .tit {
        padding-bottom: 18rpx;
      }
    }

    .cc {
      width: 100%;
      font-size: 14px;
      line-height: 1.6;
      color: #333;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      padding-top: 20rpx;
    }
  }

  .res_Status {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 0;
    border-bottom: 1rpx #eee solid;

    &:last-child {
      border: 0;
      padding-bottom: 0;
    }

    .cd {
      font-size: 28rpx;
      color: #333;
      // font-weight: 600;
      flex: 1;

      text {
        margin-right: 10rpx;
      }
    }

    .cr {
      display: flex;
      justify-content: flex-end;
      align-content: center;
      // margin: 5rpx 0 0 20rpx;

      text {
        font-size: 12px;
        color: #999;
        margin-right: 10rpx;
      }
      image {
        width: 43rpx;
      }
    }

    .cb {
      width: 100%;
      font-size: 12px;
      color: #666;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 20rpx 0 0 0;

      text {
        height: 30rpx;
        font-size: 50rpx;
        line-height: 0.6;
        color: #dbdbdb;
        margin: 0 10rpx;
      }
    }
    .cf {
      width: 100%;
      font-size: 12px;
      color: #666;
      padding: 20rpx 0 0 0;
      .tit {
        padding-bottom: 18rpx;
      }
    }

    .cc {
      width: 100%;
      font-size: 14px;
      line-height: 1.6;
      color: #333;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      padding-top: 20rpx;
    }
  }
  .res_con {
    margin-top: 24rpx;
    display: flex;
    align-items: center;
    color: #333;
    justify-content: space-between;
    width: 100%;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    padding-top: 20rpx;
    text {
      font-size: 14px;
      color: #333;
      line-height: 1.6;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .tr {
      height: 43rpx;
      image {
        width: 43rpx;
        height: 43rpx;
      }
    }
  }

  .res_tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    padding-top: 20rpx;

    .tag {
      padding: 6px 8px;
      border-radius: 4px;
      background: #f5f5f5;
      font-size: 24rpx;
      color: #333;
      margin-right: 20rpx;
    }
  }
}
.hiddenBox {
  height: 100vh;
  overflow: hidden;
}

.res_top {
  @include content;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .tl {
    flex: 1;
    text-align: left;

    image {
      width: 30rpx;
      margin-right: 6rpx;
    }

    text {
      font-size: 12px;
      color: #666;
    }

    .name {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      text {
        font-size: 32rpx;
        color: #000;
        margin-right: 22rpx;
      }

      image {
        width: 43rpx;
      }
    }

    .info {
      font-size: 12px;
      color: #666;
      margin: 20rpx 0;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      text {
        font-size: 50rpx;
        line-height: 1;
        color: #dbdbdb;
        margin: 0 10rpx;
      }
    }

    .tel {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 20rpx 0 20rpx 0;

      text {
        margin: 0 20rpx 0 0;
      }
    }

    .mail {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
  .tr {
    .head_avatar {
      width: 100rpx;
      height: 118rpx;
    }
  }
}

.res_box {
  @include content;
  margin-top: 25rpx;
}

.footspace {
  height: 200rpx;
}

.footnav {
  width: 100%;
  height: auto;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 20rpx 0 30rpx 0;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;

  .fitem {
    width: 50%;
    border-right: 1rpx #eee solid;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 50rpx;
      margin-bottom: 10rpx;
    }

    text {
      font-size: 28rpx;
      color: #333;
    }

    &:last-child {
      border: none;
    }
  }
}

.popupBox {
  padding: 30rpx;

  .pop_tit {
    text-align: left;
    font-size: 32rpx;
    font-weight: bold;
    padding: 20rpx 0;
  }

  .pop_list {
    padding: 30rpx 0 20rpx 0;
    font-size: 28rpx;
    line-height: 2;

    ::v-deep .u-radio {
      padding: 10rpx 0 30rpx 0;
      border-bottom: 1rpx #eee solid;
    }
    .pop_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100rpx;
      border-bottom: 1rpx #eee solid;
      text {
        color: #606266;
        font-size: 15px;
      }
      image {
        width: 43rpx;
        height: 43rpx;
        // padding: 10rpx 8rpx 10rpx 10rpx;
      }
      .default_img {
        width: 45rpx;
        height: 45rpx;
        padding: 0;
      }
    }
    // .pop_item:last-child {
    //   border: none;
    // }
  }

  .pop_tips {
    font-size: 26rpx;
    color: #999;
    text-align: left;
    padding: 15rpx 20rpx;
  }
}
.coverBtn_imgs {
  position: absolute;
  right: 17px;
  width: 100rpx;
  height: 118rpx;
  border-radius: 0;
  .head_avatar {
    width: 100rpx;
    height: 118rpx;
  }
}
</style>
