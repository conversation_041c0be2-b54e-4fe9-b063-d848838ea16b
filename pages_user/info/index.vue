<template>
  <view class="container set">
    <view class="mainForm">
      <u-form
        :model="form"
        :rules="rules"
        labelPosition="top"
        label-width="100"
        ref="uForm"
      >
        <template v-if="isResume">
          <view class="viewBox">
            <u-form-item label="" labelWidth="0" class="coverBox" prop="resumeAvatar">
              <view style="height: 62px;" class="avatat_box"  >
                  <view class="avatat_text" >头像</view>
                  <button
                    class="coverBtn_imgs"
                    open-type="chooseAvatar"
                    @chooseavatar="onChooseAvatar"
                  >
                  <image
                    class="head_avatar"
                    :src="
                      form.resumeAvatar || setImg('images/chat/headImg.png')
                    "
                    mode="aspectFill"
                  ></image>
                  </button> 
              </view>

          </u-form-item> 
          </view>
     
          <view class="line"></view>
          <u-form-item label="姓名" borderBottom prop="realName">
            <input
              v-model="form.realName"
              border="none"
              placeholder="请输入姓名"
              @change="input"
              placeholder-class="custom-placeholder"
            ></input>
          </u-form-item>
          <u-form-item
            label="性别"
            borderBottom
            prop="gender"
            @click="genderShow = true"
          >
          <view class="flex">
            <input
              v-model="form.gender"
              readonly
              disabled
              border="none"
              placeholder="请选择"
              placeholder-class="custom-placeholder"
            ></input>
            <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
            <!-- <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
          </view>
          </u-form-item>
          <u-form-item
            label="参加工作时间"
            borderBottom
            prop="workTime"
            labelWidth="188"
            @click="workShow = true"
          >
          <view class="flex">
            <input
              v-model="form.workTime"
              placeholder="请选择"
              readonly
              border="none"
              disabled
              placeholder-class="custom-placeholder"
            />
            <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
          </view>
            <!-- <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
          </u-form-item>
          <u-form-item
            label="出生年月"
            borderBottom
            prop="birthday"
            @click="birthShow = true"
          >
          <view class="flex">
            <input
              v-model="form.birthday"
              placeholder="请选择"
              readonly
              disabled
              border="none"
              placeholder-class="custom-placeholder"
            />
            <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
            <!-- <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
          </view>
          </u-form-item>
          <u-form-item label="手机号" borderBottom prop="mobile">
            <input
              v-model="form.mobile"
              border="none"
              placeholder="请填写"
              placeholder-class="custom-placeholder"
            ></input>
          </u-form-item>
          <u-form-item label="微信号" borderBottom prop="wxNo">
            <input
              v-model="form.wxNo"
              border="none"
              placeholder="请填写"
              placeholder-class="custom-placeholder"
            ></input>
          </u-form-item>
          <u-form-item label="邮箱号" borderBottom prop="email">
            <input
              v-model="form.email"
              border="none"
              placeholder="请填写"
              placeholder-class="custom-placeholder"
            ></input>
          </u-form-item>
        </template>
        <template v-else>
          <u-form-item :label="''" labelWidth="0" labelPosition="left" prop="avatar" >
            <view style="height: 62px;" class="avatat_box" >
              <view class="avatat_text">头像</view>
              <button
                class="cover_img"
                open-type="chooseAvatar"
                @chooseavatar="onChooseAvatar"
              >
                <image
                class="head_avatar"
                :src="
                  form.avatar || setImg('images/chat/headImg.png')
                "
                mode="aspectFill"
              ></image>
              </button>
            </view>
    
          </u-form-item>
          <view class="line"></view>
          <u-form-item label="昵称" borderBottom prop="nickName">
            <input
              v-model="form.nickName"
              type="nickName"
              border="none"
              placeholder-class="custom-placeholder"
              placeholder="请输入昵称"
            ></input>
          </u-form-item>
          <u-form-item label="姓名" borderBottom prop="realName">
            <input
              v-model="form.realName"
              border="none"
              placeholder-class="custom-placeholder"
              placeholder="请输入姓名"
            ></input>
          </u-form-item>
          <u-form-item
            label="性别"
            borderBottom
            prop="gender"
            @click="genderShow = true"
          >
          <view class="flex">
            <input
              v-model="form.gender"
              readonly
              disabled
              border="none"
              placeholder="请选择"
              placeholder-class="custom-placeholder"
            ></input>
            <image style="width:32rpx;display: inline-block;" :src="setImg('images/right_icon.png')" mode="widthFix" />
          </view>
            <!-- <u-icon slot="right" color="#ccc" name="arrow-right"></u-icon> -->
          </u-form-item>
        </template>
      </u-form>
    </view>
    <view class="footbar">
      <button @click="cancel" class="fbtn0">取消</button>
      <button @click="submit" class="fbtn1">保存</button>
    </view>
    <u-picker
      :show="genderShow"
      :columns="[genderList]"
      keyName="label"
      :closeOnClickOverlay="true"
      @close="genderShow = false"
      @cancel="genderShow = false"
      @confirm="genderSelect"
      :selectIndex="sexIndex"
    ></u-picker>
    <u-datetime-picker
      :show="workShow"
      v-model="workTimeValue"
      :maxDate="$dayjs().valueOf()"
      :minDate="$dayjs().subtract(60, 'year').valueOf()"
      mode="year-month"
      :time="workTimeIndexs"
      :closeOnClickOverlay="true"
      @cancel="workShow = false"
      @confirm="workTimeConfirm"
    ></u-datetime-picker>
    <u-datetime-picker
      :show="birthShow"
      v-model="birthValue"
      :time="birthIndexs"
      :maxDate="$dayjs().valueOf()"
      :minDate="$dayjs().subtract(60, 'year').valueOf()"
      mode="year-month"
      :closeOnClickOverlay="true"
      @cancel="birthShow = false"
      @confirm="birthConfirm"
    ></u-datetime-picker>
  </view>
</template>

<script>
 import global from "@/common/global";
import { GET_RESUME_DATA, EDIT_INFO } from "@/api/resume.js";
export default {
  data() {
    return {
      isResume: false,
      form: {
        avatar: "",
        resumeAvatar: "",
        nickName: "",
        realName: "",
        gender: "",
        workTime: "",
        birthday: "",
        mobile: "",
        wxNo: "",
        email: "",
      },
      sexIndex: [0],
      genderShow: false,
      genderList: ["男", "女"],
      workShow: false,
      workTimeValue: Number(new Date()),
      workTimeIndexs: [""],
      birthIndexs: [""],
      birthShow: false,
      birthValue: Number(new Date()),
    };
  },
  computed: {
    rules() {
      if (this.isResume) {
        return {
          resumeAvatar: [
            {
              required: true,
              message: "请上传简历头像",
              trigger: ["blur", "change"],
            },
          ],
          realName: [
            {
              required: true,
              message: "请输入姓名",
              trigger: 'blur,change',
            },
            {
              min: 1,
              max: 30,
              message: "长度在1-30个字符之间",
            },
          ],
          mobile: [
            {
              required: true,
              message: "请输入手机号",
              trigger: ["change", "blur"],
            },
            {
              validator: (rule, value, callback) => {
                return this.$u.test.mobile(value);
              },
              message: "手机号码不正确",
              trigger: ["change", "blur"],
            },
          ],
          gender: [
            {
              required: true,
              message: "请选择性别",
              trigger: "blur,change",
            },
          ],
          birthday: [
            {
              required: true,
              message: "请选择出生年月",
              trigger: "blur,change",
            },
          ],
        };
      } else {
        return {
          avatar: [
            {
              required: true,
              message: "请上传用户头像",
              trigger: ["blur", "change"],
            },
          ],
          nickName: [
            {
              required: true,
              message: "请输入昵称",
              trigger: ["blur", "change"],
            },
            {
              min: 1,
              max: 30,
              message: "长度在1-30个字符之间",
            },
          ],
          realName: [
            {
              required: true,
              message: "请输入姓名",
              trigger: ["blur", "change","input"],
            },
            {
              min: 1,
              max: 30,
              message: "长度在1-30个字符之间",
            },
          ],
          gender: [
            {
              required: true,
              message: "请选择性别",
              trigger: "blur,change",
            },
          ],
        };
      }
    },
  },
  onLoad(options) {
    if (options.isResume) {
      this.isResume = options.isResume;
    }
    this.loadData();
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    input(){
      console.log(111)
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    onChooseAvatar(e) {
      const { avatarUrl } = e.detail;
      this.uniRequest.uploadFile({
        filePath: avatarUrl,
        formData: {},
        success: ({ data }) => {
          if (this.isResume) {
            this.form.resumeAvatar = data.url;
          } else {
            this.form.avatar = data.url;
          }
        },
      });
    },
    genderSelect(e) {
      this.form.gender = e.value[0];
      this.sexIndex = e.indexs;
      this.genderShow = false;
    },
    workTimeConfirm(e) {
      this.form.workTime = uni.$u.date(e.value, "yyyy-mm");
      this.workTimeIndexs = this.form?.workTime
        ? this.form?.workTime.split("-")
        : [""];
      this.workShow = false;
    },
    birthConfirm(e) {
      this.form.birthday = uni.$u.date(e.value, "yyyy-mm");
      this.birthIndexs = this.form?.birthday
        ? this.form?.birthday.split("-")
        : [""];
      this.birthShow = false;
    },
    async loadData() {
     const userInfo = await GET_RESUME_DATA();
      this.form = userInfo?.data?.baseInfo;
       this.workTimeIndexs = this.form?.workTime
        ? this.form?.workTime.split("-")
        : [""];
      this.birthIndexs = this.form?.birthday
        ? this.form?.birthday.split("-")
        : [""];
      this.sexIndex = this.form?.gender
        ? [this.genderList.findIndex((item) => item === this.form.gender)]
        : [0];
    },
    submit() {
      this.$refs.uForm
        .validate()
        .then(async (res) => {
          let data = {
            ...this.form,
          };
          let resp = await EDIT_INFO(data);
          if (this.qUtil.validResp(resp)) {
            uni.showToast({
              title: "提交成功！",
              icon: "success",
            });
            setTimeout(function () {
              uni.navigateBack({
                delta: 1,
              });
            }, 1400);
          } else {
            uni.showToast({
              icon: "none",
              title: resp.msg || "提交失败！",
            });
          }
        })
        .catch((errors) => {
          console.error(errors);
          // uni.$u.toast('校验失败')
        });
    },
    cancel() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
page {
  background: #fff;
}

.line {
  background: #f5f6f7;
  height: 30rpx;
  margin: 0 -30rpx;
}
// ::v-deep.custom-placeholder {
//   color: #adacb1;
// }
::v-deep.custom-placeholder {
  color: #0000004D;
  font-weight: 600;
  // font-size: 16px;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.viewBox {
  position: relative;
  background-color: #fff;
  height: 180rpx;
}
.cover_img {
  position: absolute;
  right: 17px;
  width: 100rpx;
  height: 118rpx;
  border-radius: 0;
  .head_avatar {
    width: 100rpx;
    height: 118rpx;
  }
}
.coverBtn_imgs {
  position: absolute;
  right: 0;
  width: 100rpx;
  height: 118rpx;
  border-radius: 0;
  .head_avatar {
    width: 100rpx;
    height: 118rpx;
  }
}
.avatat_box {
  display: flex;
  height: 62px;
  .avatat_text{
    font-size: 16px ;
    color: #00000099 ;
  }
}

</style>
