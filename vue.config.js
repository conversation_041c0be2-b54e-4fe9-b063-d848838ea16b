module.exports = {
    // 配置路径别名
    configureWebpack: {
        devServer: {
            // 调试时允许内网穿透，让外网的人访问到本地调试的H5页面
            disableHostCheck: true
        }
    },
    devServer: {
        port: 83,
        proxy: {
            '/front': {
                // headers: {
                //   "Referer": 'https://example.com'
                // },
                target: 'http://rxjh.lovesx.top:40080/api/front/', //代理地址，这里设置的地址会代替axios中设置的baseURL
                // target: 'http://*************:9599/api/', //代理地址，这里设置的地址会代替axios中设置的baseURL
                changeOrigin: true, // 如果接口跨域，需要进行这个参数配置
                //ws: true, // proxy websockets
                //pathRewrite方法重写url
                // rewrite: path => path.replace(/^\/api/, ''), // 将 /api 重写为空
                secure: false
            },
        },
    },
    // productionSourceMap: false,
}
