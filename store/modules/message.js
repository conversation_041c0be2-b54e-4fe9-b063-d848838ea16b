import Vue from 'vue';
import Vuex from 'vuex';
import storageUtil from '@/utils/storageUtil.js';
import Websocket from "@/utils/websocketUtil";

Vue.use(Vuex);

// 加载历史数据
let localMsg = storageUtil.getItem('localMsg') || {};
// 保存数据至localStorage
const saveLocalMsg = (key, value) => {
  let tmp = storageUtil.getItem('localMsg');
  tmp = tmp ? tmp : {};
  tmp[key] = value;
  storageUtil.setItem('localMsg', tmp);
}
// 创建store实例
const message= {
  state: {
    // websocket实例
    ws: null,
    // 聊天状态 0可以发送消息(等待用户发送or模型返回数据结束) 1不能发送消息(用户发送消息等待模型返回) 2不能发送消息(模型返回数据中)
    msgStatus: 0,
    // 聊天记录
    localMsg: localMsg || {}
  },
  getters:{
    ws: state => state.ws,
    msgStatus: state => state.msgStatus,
    localMsg: state => state.localMsg,
  },
  mutations: {
    INIT_WEBSOCKET(state, rootState) {
      if (rootState.user.hasLogin && !state.ws){
        // 创建ws实例,并且初始化
        state.ws = new Websocket();
        // state.ws.initSocket();
      }
    },
    SET_MSG_STATUS(state, msgStatus) {
      state.msgStatus = msgStatus;
    },
    SET_LOCAL_MSG(state, {key, value}) {
      state.localMsg[key] = value;
      saveLocalMsg(key, value);
    },
    CLOSE_WEBSOCKET(state) {
      state.ws?.closeSocket('小程序进入后台,关闭WS连接');
      setTimeout(() => {
        state.ws = null;
      }, 100)
    }
  },
  actions: {
    initWS({commit, rootState}){
      // 创建ws连接
      commit('INIT_WEBSOCKET', rootState)
    },
    setMsgStatus({commit}, payload){
      // 创建ws连接
      commit('SET_MSG_STATUS', payload)
    },
    setLocalMsg({commit}, payload){
      commit('SET_LOCAL_MSG', payload)
    },
    closeSocket({commit}){
      commit('CLOSE_WEBSOCKET')
    }
  },
}

export default message
