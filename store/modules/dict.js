import Vue from 'vue';
import Vuex from 'vuex';
import uniRequest from '@/utils/request.js';
import qUtil from '@/utils/queryUtil.js';

Vue.use(Vuex);

// 创建store实例
const dict= {
  // namespaced: true,
  state: {
    dict: []
  },
  getters:{
    getState(state){
      return state;
    }
  },
  mutations: {
    SET_DICT(state, payload) {
      state.dict.push(payload);
    },
    REMOVE_DICT(state, key) {
      const index = state.dict.findIndex(item => item.key === key);
      if (index !== -1) {
        state.dict.splice(index, 1);
      }
    },
    CLEAN_DICT(state) {
      state.dict = [];
    }
  },
  actions: {
    async getDict({ state, commit }, key) {
      if (key == null || key === '') {
        return null;
      }
      try {
        for (let i = 0; i < state.dict.length; i++) {
          if (state.dict[i].key === key) {
            return state.dict[i].value;
          }
        }
        // 如果本地没有数据，则从后台接口请求数据
        const resp = await uniRequest.get(`/system/dict/data/type/${key}/member`);
        if (qUtil.validResp(resp)){
          const data = resp.data.map(p => ({
            label: p.dictLabel,
            value: p.dictValue,
            listClass: p.listClass,
            cssClass: p.cssClass
          }));

          // 将获取到的数据提交到 state 中
          commit('SET_DICT', { key, value: data });
          return data;
        }
        return null;
      } catch (ignore) {
        return null;
      }
    }
  }
}

export default dict
