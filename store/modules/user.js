import Vue from "vue";
import Vuex from "vuex";
import uniRequest from "@/utils/request.js";
import qUtil from "@/utils/queryUtil.js";
import storageUtil from "@/utils/storageUtil.js";
import { CHAT_SYNC } from "@/api/resume.js";

Vue.use(Vuex);

// 加载历史数据
let localData = storageUtil.getItem("localData") || {};
// 需要永久存储，且下次APP启动需要取出的，在state中的变量名
let saveStateKeys = ["vuex_user", "vuex_token"];
// 保存数据至localStorage
const saveLocalData = (key, value) => {
  if (saveStateKeys.indexOf(key) != -1) {
    let tmp = storageUtil.getItem("localData");
    tmp = tmp ? tmp : {};
    tmp[key] = value;
    storageUtil.setItem("localData", tmp);
  }
};
// 创建store实例
const user = {
  // namespaced: true,
  state: {
    vuex_user: localData.vuex_user ? localData.vuex_user : { name: "游客" },
    vuex_token: localData.vuex_token ? localData.vuex_token : "",
    hasLogin: !!localData.vuex_token,
    device: "",
  },
  getters: {
    device: (state) => state.device,
    hasLogin: (state) => state.hasLogin,
    token: (state) => state.vuex_token,
    userInfo: (state) => state.vuex_user,
  },
  mutations: {
    SET_DEVICE(state, device) {
      state.device = device;
    },
    SET_TOKEN(state, payload) {
      state.vuex_token = payload;
      state.hasLogin = true;
      localData.vuex_token = payload;
      saveLocalData(saveStateKeys[1], payload);
    },
    SET_USER(state, payload) {
      state.vuex_user = payload;
      state.hasLogin = true;
      localData.vuex_user = payload;
      saveLocalData(saveStateKeys[0], payload);
    },
    LOGOUT(state) {
      console.log("LOGOUT");
      state.vuex_user = {};
      state.vuex_token = "";
      state.hasLogin = false;
      let localMsgSearchKey = "localMsg";
      const keys = storageUtil.getAllKeys();
      const talkId = storageUtil.getItem("talkId");
      const localMsg = keys
        .filter((key) => key.startsWith(localMsgSearchKey))
        .map((key) => {
          if (key) {
            return {
              key,
              value: storageUtil.getItem(key),
            };
          }
        });
      storageUtil.clean();
      localMsg.map((m) => {
        storageUtil.setItem(m.key, m.value);
      });
      storageUtil.setItem("talkId", talkId);
      storageUtil.setItem("confirmLogin", true);
    },
  },
  actions: {
    // 登录
    login({ commit, rootState }, payload) {
      return new Promise((resolve, reject) => {
        uniRequest
          .post("/auth/login", payload)
          .then(async (resp) => {
            if (!qUtil.validResp(resp)) {
              reject(resp.msg || "授权登录失败");
            } else {
              const info = resp.data;
              if (info.access_token) {
                // 登录成功,设置token相关功能
                commit("SET_TOKEN", resp.data.access_token);
                // 创建ws连接
                commit("INIT_WEBSOCKET", rootState, { root: true });
              }
              resolve(resp);
              const talkId = storageUtil.getItem("talkId");
              const result = await CHAT_SYNC({ talkId });
              if (this.qUtil.validResp(result) && result.code === 200) {
                if (result.data.talkId != talkId) {
                  storageUtil.setItem("talkId", result.data.talkId);
                }
              }
            }
          })
          .catch((e) => {
            reject("授权登录失败");
          });
      });
    },
    // 退出
    logout({ commit }) {
      commit("LOGOUT");
    },
    refreshInfo({ commit }) {
      return new Promise((resolve, reject) => {
        uniRequest
          .get("/member/detail")
          .then((resp) => {
            // 用户信息
            if (!qUtil.validResp(resp)) {
              reject(resp.msg || "登录信息过期");
            } else {
              commit("SET_USER", resp.data);
              resolve(resp.data);
            }
          })
          .catch((e) => {
            reject("请重新登录");
          });
      });
    },
  },
};

export default user;
