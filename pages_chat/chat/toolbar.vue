<template>
  <view>
    <!-- 底部输入栏 -->
    <view
      id="inputMain"
      class="input-main input-style"
      :style="chatStyle"
      @click="enableInput"
    >
      <view
        class="input-box"
        :style="!hidePopupLayer ? 'padding-top:15rpx' : ''"
      >
        <view class="em" @click="changeType">
          <image
            :src="
              recordShow
                ? setImg('images/chat/keyboardIcon.png')
                : setImg('images/chat/microphone.png')
            "
            mode="widthFix"
          ></image>
        </view>
        <!--中间输入框 聊天内容输入 -->
        <view class="textbox">
          <view class="text-mode">
            <view
              :class="'box' + (middleLen ? ' boxs' : '')"
              v-if="!recordShow"
            >
              <textarea
                class="textarea"
                v-model="textMsg"
                cursor-spacing="70"
                placeholder="有问题尽管找我"
                placeholder-style="color:#0000004D;fontSize:28rpx;"
                auto-height
                maxlength="-1"
                :fixed="true"
                @input="inputFn"
                confirm-type="send"
                :show-confirm-bar="false"
                :adjust-position="false"
                :disable-default-padding="true"
                :disabled="loading"
                @confirm="sendText"
                :hold-keyboard="true"
                v-if="!loading"
                @keyboardheightchange="keyboardheightchange"
              ></textarea>
            </view>
            <speech
              style="width: 100%"
              @speechCallback="speechCallback"
              @loadingFn="loadingFn"
              @pushList="$emit('pushList')"
              v-else
            />
          </view>
        </view>
        <view class="send">
          <image
            :src="setImg('images/chat/chatSend.png')"
            v-if="!loading"
            @click="sendText"
          />
          <image
            v-else
            @click="stopMsg"
            :src="setImg('images/chat/stop.png')"
          />
        </view>
      </view>
      <view class="edit-input-box" v-if="editShow">
        <view class="editBox" :style="editStyle">
          <image
            @click="closeEdit"
            mode="widthFix"
            :src="setImg('images/chat/editClose.png')"
          />
          <view class="box">
            <textarea
              class="textarea"
              v-model="editMsg"
              :cursor="editMsg.length"
              cursor-spacing="70"
              placeholder="有问题尽管找我"
              placeholder-style="color:#0000004D;fontSize:28rpx;"
              auto-height
              maxlength="-1"
              :fixed="true"
              :auto-focus="true"
              confirm-type="send"
              :show-confirm-bar="false"
              :adjust-position="false"
              :disable-default-padding="true"
              @confirm="sendText('edit')"
              :hold-keyboard="true"
              @keyboardheightchange="editkeyboardheight"
            ></textarea>
          </view>
          <image
            @click="sendText('edit')"
            :src="setImg('images/chat/editOk.png')"
          />
        </view>
      </view>
      <view class="mask" v-if="!hidePopupLayer"></view>
      <u-safe-bottom
        v-if="hidePopupLayer"
        :style="{ background: '#fff' }"
      ></u-safe-bottom>
    </view>
  </view>
</template>
<script>
import { STOP_MSG } from "@/api/resume.js";
import ChatScene from "@/model/enums/ChatScene";
import { checkLogin } from "@/utils/auth.js";
import Speech from "@/pages_chat/chat/components/Speech.vue";
import global from "@/common/global";
export default {
  mixins: [],
  components: { Speech },
  data() {
    return {
      bottomHeight: 0,
      KeyboardHeight: 0, //检测键盘高度
      //文字消息
      textMsg: "",
      editMsg: "",
      isVoice: false,
      recordShow: false,
      popupLayerClass: "",
      hidePopupLayer: true,
      sceneActive: false,
      maskShow: false,
      editShow: false,
      chatStyle: "bottom: 40rpx;",
      editStyle: "bottom: 80rpx;",
      stopFlag: false,
    };
  },
  props: {
    currScene: {
      type: String,
      default: "",
    },
    disable: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    chatScene() {
      return ChatScene.filter((i) => i.key !== this.currScene && !i.hidden);
    },
    loading() {
      return this.$store.getters.msgStatus != 0;
    },
  },
  mounted() {
    uni.onKeyboardHeightChange((res) => {
      this.KeyboardHeight = res.height;
      if (!res.height) {
        this.editShow = false;
      }
      // this.bottomHeight = this.KeyboardHeight > 0 ? 30 : 0;
    });
  },
  methods: {
    changeMenu(key) {
      if (this.$store.getters.msgStatus !== 0) {
        uni.showModal({
          title: "温馨提示",
          content: "对话内容正在接收中,切换场景会影响数据正确响应,是否继续切换",
          confirmText: "继续",
          success: (res) => {
            if (res.confirm) {
              this.$emit("changeScene", key);
            }
          },
        });
      } else {
        this.$emit("changeScene", key);
      }
      this.sceneActive = false;
    },
    closeEdit(){
      this.editShow= false;
      this.hidePopupLayer = true;
      this.chatStyle = `bottom: 40rpx;`;
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
    //更多功能(点击+弹出)
    showMore() {
      this.sceneActive = false;
      this.isVoice = false;
      if (this.hidePopupLayer) {
        this.hidePopupLayer = false;
        this.popupLayerClass = "showLayer";
      } else {
        this.sceneActive = false;
        this.hideDrawer();
      }
    },
    // 隐藏抽屉
    hideDrawer() {
      this.popupLayerClass = "";
      this.$nextTick(() => {
        this.hidePopupLayer = true;
      });
    },
    inputFn(e) {
      if (e.detail.value.includes('\n')){
        this.sendText();
      }
      
    },
    //语音识别文字并发送
    speechCallback(text) {
      this.$store.dispatch("setMsgStatus", 2);
      if (text === "") return;
      this.sendMsg(text, "text");
      // this.$emit("scrollBottom");
    },
    // 麦克风权限
    getAuthorize() {
      wx.getSetting({
        success(res) {
          if (!res.authSetting["scope.record"]) {
            // 没有权限，发起授权请求
            wx.authorize({
              scope: "scope.record",
              success() {
                console.log("授权成功");
                // 用户已同意小程序使用麦克风，可以在这里执行开启麦克风的操作
              },
              fail() {
                console.log("用户拒绝了小程序的麦克风权限请求");
                // 引导用户到设置页面开启权限
                wx.showModal({
                  title: "提示",
                  content: "请在小程序设置中打开麦克风权限",
                  success(modalRes) {
                    if (modalRes.confirm) {
                      wx.openSetting({
                        success(settingRes) {
                          if (settingRes.authSetting["scope.record"]) {
                            console.log("打开了麦克风权限");
                          }
                        },
                      });
                    }
                  },
                });
              },
            });
          }
        },
      });
    },
    // 发送文字消息
    sendText(type = "none") {
      let msg = "";

      if (!checkLogin(false)) {
        return uni.showModal({
          showCancel: false,
          title: "提示",
          content: "请先授权登陆后再进行操作!",
        });
      }

      this.hideDrawer(); //隐藏抽屉
      if (
        (type !== "edit" && this.textMsg) ||
        (type !== "edit" && !this.loading)
      ) {
        if (this.textMsg === "") {
          uni.$u.toast("发送内容不能为空");
          return;
        }
        msg = this.textMsg;
        this.chatStyle = `bottom: 40rpx;`;
      } else if (type === "edit" && this.editMsg) {
        if (this.editMsg === "") {
          uni.$u.toast("发送内容不能为空");
          return;
        }
        msg = this.editMsg;
        this.editShow = false;
        this.$emit("deleteMessage");
      }
      this.sendMsg(msg, "text");
      this.textMsg = ""; //清空输入框
    },
    // 发送消息
    sendMsg(content, type, variables = {}) {
      this.$store.getters.ws.resumeSending();
      this.$store.dispatch("setMsgStatus", 1);
      const msg = {
        type,
        content,
        scene: this.currScene,
        variables,
      };
      this.$emit("sendMsg", msg);
    },
    async stopMsg() {
      console.log(this.disable);
      if (this.disable) {
        uni.$u.toast("内容输出中，请稍后重试");
        return;
      }
      this.stopFlag = true;
      this.$store.dispatch("setMsgStatus", 0);
      this.$emit("setMsgList");
      await STOP_MSG({
        scene: this.currScene,
      });

      // uni.$off("message", this.pushAssistantMsg);
    },
    enableInput(){
      this.sceneActive = false
      if(this.$store.getters.msgStatus !== 0){
        // uni.$u.toast("顾问回复中，暂无法提问");
        return;
      }
    },
    //键盘调起
    keyboardheightchange() {
      if (this.KeyboardHeight > 0) {
        this.hidePopupLayer = false;
        this.chatStyle = `bottom: ${this.KeyboardHeight + 8}px;`;
      } else {
        this.hidePopupLayer = true;
        this.chatStyle = `bottom: 40rpx;`;
      }
    },
    loadingFn(flag) {
      this.$emit("loadingFn", flag);
    },
    editkeyboardheight() {
      this.editStyle = `bottom: ${this.KeyboardHeight}px;`;
      if (this.KeyboardHeigh <= 0) {
        this.editShow = false;
      }
    },
    // 显示更多
    showMore() {
      this.moreFlag = true;
    },
    // 隐藏更多
    hideMore() {
      this.moreFlag = false;
    },
    // 显示抽屉
    showDrawer() {
      this.drawerFlag = true;
    },
    showEditModal(text) {
      this.editShow = true;
      this.editMsg = text;
    },
    changeType() {
      this.recordShow = !this.recordShow;
      this.isVoice = true;
      if (this.recordShow) {
        this.hidePopupLayer = true;
        this.chatStyle = `bottom: 40rpx;`;
        this.getAuthorize();
        this.$emit("setMainHeight", this.KeyboardHeight, "blur");
      }
    },
  },
};
</script>
<style lang="scss">
@import "@/static/style/chat.scss";
// .input-style {
//   transition: bottom 0.2s ease-in-out;
// }
.mask {
  width: 100%;
  height: 389px;
  position: fixed;
  background-color: #f6f6f6;
}
</style>
