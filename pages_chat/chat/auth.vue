<template>
  <view>
    <view class="row" v-for="(row, index) in msgList" :key="index">
      <block v-if="row.type == 'assistant'">
        <!-- 别人发出的消息 -->
        <view class="other">
          <!-- 左-头像 -->
          <view class="left">
            <image :src="setImg(`images/avatar_bot.png`)"></image>
          </view>
          <!-- 右-用户名称-时间-消息 -->
          <view class="right">
            <view class="username">
              <view class="name"> 简历顾问 </view>
            </view>
            <!-- 文字消息 -->
            <view v-if="row.msg.type == 'text'" class="bubble">
              <rich-text :nodes="row.msg.content"></rich-text>
            </view>
            <!-- 授权登陆 -->
            <view v-if="row.msg.type == 'auth'" class="bubble auth">
              <rich-text :nodes="row.msg.content"></rich-text>
              <!--<button @click="openIdLogin" class="btn">授权信息</button>-->
              <button
                @getphonenumber="getPhoneNumber"
                open-type="getPhoneNumber"
                class="btn"
              >
                授权信息
              </button>
            </view>
          </view>
        </view>
      </block>
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <zero-privacy></zero-privacy>
    <!-- #endif -->
  </view>
</template>
<script>
import oauth from "@/mixins/oauth";

export default {
  mixins: [oauth],
  components: {},
  data() {
    return {
      msgList: [],
    };
  },
  mounted() {
    this.loadCode();
    this.initMsgList();
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    // this.$dayjs().format('MM-DD HH:mm')
    // 加载初始页面消息
    initMsgList() {
      // 消息列表
      let list = [
        {
          type: "assistant",
          msg: {
            type: "text",
            content: "亲爱的同学,开始之前请先告诉我该如何称呼你",
          },
        },
        {
          type: "assistant",
          msg: {
            type: "auth",
            content: "请点击下方按钮，为我授权你的信息",
          },
        },
      ];
      this.msgList = list;
      // 滚动到底部
      this.$nextTick(function () {
        //进入页面滚动到底部
        this.scrollTop = 9999;
        this.$nextTick(function () {
          this.scrollAnimation = true;
        });
      });
    },
    authFail() {
      this.loadCode();
      this.msgList.push({
        type: "assistant",
        msg: {
          type: "auth",
          content: "授权失败了,请重新来过!",
        },
      });
    },
    authSuccess() {
      this.msgList.push({
        type: "assistant",
        msg: {
          type: "text",
          content: "授权成功,让我们继续之前的操作吧!",
        },
      });
      this.$emit("authSuccess");
    },
  },
};
</script>
<style lang="scss"></style>
