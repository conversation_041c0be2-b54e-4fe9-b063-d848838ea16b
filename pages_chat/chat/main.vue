<template>
  <view>
    <template v-for="(row, index) in msgList">
      <view v-if="row.show" class="row" :key="index" :id="`msg${index}`">
        <!-- ai消息 -->
        <block v-if="row.role == 'assistant'">
          <view>
            <view class="other">
              <!-- 左-头像 -->
              <view class="left">
                <image :src="headImg" />
                <!-- <image :src="setImg('images/avatar_bot.png')"/> -->
              </view>
              <!-- 右-用户名称-时间-消息 -->
              <view class="right">
                <view class="username">
                  <view class="name">{{ currSceneLabel }}顾问</view>
                </view>
                <!-- 文字消息 -->
                <view v-if="row.type == 'text'" class="bubble">
                  <view
                    v-if="
                      (row.content.length > 1 && !row.errorShow) ||
                      (row.loadingStatus && !row.errorShow)
                    "
                  >
                    <view
                      class="loadingBox"
                      v-if="
                        row.loadingStatus && !row.stopShow && !row.errorShow
                      "
                    >
                      <image
                        class="loading"
                        :src="
                          row.loadingStatus === 2
                            ? setImg('images/chat/loadZhiStop.png')
                            : setImg('images/chat/loadingZhi.gif')
                        "
                        mode="widthFix"
                      />
                      <text
                        :style="{
                          color:
                            row.loadingStatus === 2 ? '#9a9a9a' : '#666666',
                        }"
                        >{{ row.loadingText }}</text
                      >
                    </view>
                    <zero-markdown-view
                      v-if="row.content.length > 1"
                      :markdown="row.content"
                    ></zero-markdown-view>
                    <view
                      style="color: #999; font-size: 12px"
                      v-if="row.stopShow"
                    >
                      (用户停止)
                    </view>
                    <image
                      v-if="row.copyShow && row.content.length > 1"
                      class="copy"
                      :src="setImg('images/chat/copy.png')"
                      mode="widthFix"
                      @click="copyMsg(row.content)"
                    />
                  </view>
                  <template v-else>
                    <loadingPulse
                      v-if="!row.stopShow && !row.errorShow && !row.netErr"
                      bgColor="#111"
                    ></loadingPulse>
                    <view
                      style="color: #999; font-size: 12px"
                      v-if="row.stopShow && !row.errorShow && !row.netErr"
                    >
                      (用户停止)
                    </view>
                    <view
                      class="error"
                      v-if="row.errorShow && !row.stopShow && !row.netErr"
                      @click="retryMsg(index)"
                      >我遇到了网络问题，无法及时回复。只需点我一下，我将尝试重新连接。</view
                    >
                    <view
                      class="error"
                      @click="retryMsg(index)"
                      v-if="row.netErr && !row.stopShow && !row.errorShow"
                      >网络出现问题，请检查网络后稍等片刻</view
                    >
                  </template>
                </view>
              </view>
            </view>
            <view class="guideBox" v-if="row.guideShow">
              <view class="guideBox_list">
                <view
                  class="item"
                  v-for="(item, i) in row.questions"
                  @click="setGuideMsg(item, index)"
                  :key="i"
                  >{{ item }}</view
                >
              </view>
            </view>
          </view>
        </block>
        <!-- 用户消息 -->
        <block v-if="row.role == 'user'">
          <!-- 自己发出的消息 -->
          <view class="my">
            <!-- 左-消息 -->
            <view class="left">
              <!-- 文字消息        + (row.flag ? ' bubbleEdit' : '')-->
              <view
                v-if="row.type === 'text'"
                class="bubble"
                @click="editMessage(index, row)"
                @longpress="longPress(row)"
                :data-index="index"
                :data-content="row.content"
                :data-type="row.type"
                :data-role="row.role"
                :data-id="row.id"
                :data-scene="row.scene"
                :data-variables="row.variables"
                :data-time="row.time"
                :data-show="row.show"
              >
                <view :class="row.content && row.flag ? 'bubbleEdit' : ''">
                  <view v-if="row.content.length > 0" style="padding: 4rpx 4rpx 0 4rpx;" >{{ row.content }}</view>
                  <loadingPulse
                    :noNetwork="true"
                    v-if="audioLoading && row.content === ''"
                    bgColor="#fff"
                    @overtime="userDelete"
                  ></loadingPulse>
                </view>
              </view>
              <!-- 图片消息 -->
              <view
                v-if="row.type === 'image'"
                class="bubble img"
                @tap="showImage(row.content)"
              >
                <image :src="row.content" mode="widthFix"></image>
              </view>
              <!-- 文件消息 -->
              <view v-if="row.type === 'file'" class="bubble resume">
                <u-tag
                  @click.stop="showFile(row.variables.tempFileUrl)"
                  :text="row.variables.fileName"
                  type="warning"
                  icon="file-text"
                ></u-tag>
              </view>
            </view>
          </view>
        </block>
      </view>
    </template>
  </view>
</template>
<script>
import chatScene from "@/model/enums/ChatScene";
import { GET_HELLO_MSG,CHAT_SYNC } from "@/api/resume.js";
import loadingPulse from "./components/loadingPulse.vue";
export default {
  mixins: [],
  components: { loadingPulse },
  props: {
    audioLoading: {
      type: Boolean,
      default: false,
    },
    currScene: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      userInfo: {},
      msgList: [],
      msgImgList: [],
      memberId: 0,
      chatScene,
      sceneKey: "",
      // 是否显示重试文案
      showRetry: false,
      userList: [],
      delIndex: null,
      questionsList: [],
    };
  },
  // watch(){
  //   $store.getters.
  // },
  computed: {
    currSceneLabel() {
      return chatScene.find((m) => m.key === this.sceneKey)?.label;
    },
    headImg(){
      return chatScene.find((m) => m.key === this.sceneKey)?.headImg;
    },
    loading() {
      return this.$store.getters.msgStatus != 0;
    },
  },
  mounted() {
    this.loadUserInfo();
    this.$nextTick(() => {
      uni.setNavigationBarTitle({
        title: this.currSceneLabel,
      });
      this.$emit("changeTitle", this.currSceneLabel);
      uni.$on("message", this.pushAssistantMsg);
    });
  },
  beforeDestroy() {
    uni.$off("message", this.pushAssistantMsg);
    this.localMsgHandle();
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    // 预览图片
    showImage(url) {
      uni.previewImage({
        indicator: "none",
        current: url,
      });
    },
    userDelete() {
      this.$emit("loadingFn", false);
      const ind = this.msgList.length - 1;
      if (
        this.msgList[ind].role === "user" &&
        this.msgList[ind].content === ""
      ) {
        uni.$u.toast("未识别到声音,请重试");
        this.msgList.pop();
        this.$store.dispatch("setMsgStatus", 0);
      }
    },
    showFile(url) {
      // #ifdef MP-WEIXIN
      wx.openDocument({
        filePath: url,
        success: (res) => {
          console.log("打开文档成功");
        },
      });
      // #endif
    },
    // 引导触发
    setGuideMsg(text, index) {
      this.localMsgHandle();
      this.$nextTick(() => {
        this.$emit("loadHello", { content: text, show: true, isNoSend: false });
        this.msgList.map((item, i) =>
          item.role == "assistant" ? (item.guideShow = false) : item
        );
      });
    },
    async loadUserInfo() {
      uni.showLoading({
        title: "加载中",
      });
      this.userInfo = await this.$store.dispatch("refreshInfo");
      uni.hideLoading();
    },
    repeatAction(times, action) {
      let i = 0;
      while (i < times) {
        action(i);
        i++;
      }
    },
    // 复制
    copyMsg(text) {
      uni.setClipboardData({
        data: text,
        success: function () {
          uni.$u.toast("复制成功");
        },
      });
    },
    // 添加AI助理消息
    pushAssistantMsg({ data }) {
      const index = this.msgList.length - 1;
      const resp = this.baseUtil.toJson(data) || "";
      // 消息接收是否完成
      if (
        (resp && resp?.type === "DONE") ||
        "[DONE]" === data ||
        "{}" === data ||
        "" === data
      ) {
        // gpt输出已经结束
        if (this.msgList[index]) {
          this.msgList[index].copyShow = true;
          if (this.msgList[index].content.length == 0) {
            this.msgList[index].errorShow = true;
          }
        }

        this.$emit("scrollBottom");
        this.$store.dispatch("setMsgStatus", 0);
      }
      // 优化状态 是否展示状态条
      if (resp.type === "FINISH") {
        this.$emit("setTopTipsShow", resp);
      }
      if (resp.type === "STATUS" && this.msgList[index]) {
        this.msgList[index].loadingText = resp.content;
        this.msgList[index].loadingStatus = resp.status;
      }
      if (
        resp.content != undefined &&
        this.msgList[index].content.length >= 1
      ) {
        this.msgList[index].loadingSuccess = "success";
      }
      //保存questions数据
      if (
        resp.type === "questions" &&
        resp.questions &&
        resp.questions.length > 0
      ) {
        this.questionsList = resp.questions;
        this.msgList[index].guideShow = false;
        this.msgList[index].questions = this.questionsList;
      }
      // 单独处理文字推送结束后 出引导的状态
      if (
        this.msgList.length > 0 &&
        this.msgList[index].questions &&
        this.msgList[index].questions.length > 0 &&
        !this.msgList[index].guideShow &&
        this.msgList[index].content.length > 0 &&
        this.$store.getters.msgStatus === 0
      ) {
        this.msgList[index].guideShow = true;
        this.$nextTick(() => {
          this.$emit("scrollBottom");
        });
      }
      if (!resp.content) {
        return;
      }
      // 切换场景
      if (resp.type === "changeScene") {
        // 模型调用接口,请求切换场景,后台返回的数据
        this.$emit("changeScene", resp.scene);
        // this.storageUtil.setItem(`localMsg-${resp.scene}-time`, new Date().getTime());
        this.$store.dispatch("setLocalMsg", {
          key: `${resp.scene}-time`,
          value: new Date().getTime(),
        });
        this.msgList.push({
          role: "assistant",
          type: "text",
          show: true,
          content: "",
          questions: [],
        });
        return this.$store.getters.ws.sendMsg({
          type: "text",
          content: resp.content,
          scene: resp.scene,
          variables: {},
        });
      }
      // 改为可以聊天状态 消息拼接
      this.$store.dispatch("setMsgStatus", 2);
      this.joinUser(resp, index);
    },
    joinUser(resp, index) {
      if (resp.type !== "ERROR" && resp.type !== "STATUS") {
        if (this.msgList[index]?.role === "assistant") {
          this.msgList[index].content += resp.content;
          this.$nextTick(() => {
            this.$emit("scrollBottom");
          });
          // this.startTyping(this.msgList[index].content, index);
        } else {
          this.msgList.push({
            role: "assistant",
            type: "text",
            show: true,
            content: resp.content,
            questions: resp.questions,
          });
          this.$store.dispatch("setMsgStatus", 0);
        }
      }
      // 缓存接受的消息
      this.localMsgHandle();
    },
    // 添加用户消息
    pushMsg({
      scene,
      show = true,
      content,
      type = "text",
      variables = {},
      isNoLoading = false,
    }) {
      this.msgList.map((item, i) => (item.flag = false));
      this.msgList.map((item, i) =>
        item.role == "assistant" ? (item.guideShow = false) : item
      );
      this.showRetry = false;
      // 判断是否是重复发送的消息
      if (this.msgList.length > 0) {
        const index = this.msgList.length - 1;
        if (
          this.msgList[index].role == "user" &&
          this.msgList[index].content == ""
        ) {
          this.msgList[index].content = content;
          this.msgList[index].flag = true;
        } else {
          this.msgList.push({
            role: "user",
            content: content,
            type: type,
            show,
            scene,
            variables,
            flag: true,
          });
        }
      } else {
        this.msgList.push({
          role: "user",
          content: content,
          type: type,
          show,
          scene,
          variables,
          flag: true,
        });
      }

      // push一条空的助理消息,等待后台返回真实数据,loading效果会在此条记录中显示
      if (!isNoLoading) {
        this.msgList.push({
          role: "assistant",
          type: "text",
          show: true,
          content: "",
          questions: [],
        });
      }

      // 上传头像不再经过大模型
      if (type != "image") {
        this.$store.dispatch("setMsgStatus", 1);
      } else {
        this.loadUserInfo();
      }
      // 缓存发送的消息
      this.localMsgHandle();
      this.$nextTick(() => {
        this.$emit("scrollBottom");
      });
    },
    // 未响应消息重发
    async retryMsg(ind) {
      this.showRetry = false;
      const data = this.msgList.filter((item) => item.content != "");
      const arr = this.msgList.filter((_, i) => i != ind);
      this.msgList = arr;
      if (data.length === 0) {
        this.msgList.push({
          role: "assistant",
          type: "text",
          show: true,
          content: "",
          questions: [],
        });
        this.$store.dispatch("setMsgStatus", 2);
        const talkId = this.storageUtil.getItem("talkId") || "";
        this.$store.getters.ws.checkConnect();
        const result = await GET_HELLO_MSG({
          scene: this.sceneKey,
          talkId,
        });
        this.errNetFn();
        if (this.qUtil.validResp(result) && result.code === 200) {
          this.storageUtil.setItem("talkId", result.data.talkId);
        } else {
          if (this.msgList[this.msgList.length - 1].role === "assistant")
            this.msgList[this.msgList.length - 1].netErr = true;
          this.$store.dispatch("setMsgStatus", 0);
        }
      } else {
        const lastUserMsg = this.msgList
          .slice()
          .reverse()
          .find((m) => m.role === "user" && m.content !== "**你好");
        if (lastUserMsg) {
          this.msgList.push({
            role: "assistant",
            type: "text",
            show: true,
            content: "",
            questions: [],
          });
          this.$store.dispatch("setMsgStatus", 2);
          this.$store.getters.ws.sendMsg({
            ...lastUserMsg,
            scene: this.sceneKey,
          });
        }
      }
    },
    //打字机效果
    startTyping(text, index) {
      let str = "";

      let currentIndex = 0;
      const typingSpeed = 100; // 打字速度，单位：毫秒

      const timer = setInterval(() => {
        // this.msgList[index].content
        str += text[currentIndex];
        // this.msgList[index].content = str;
        currentIndex++;

        if (currentIndex >= text.length) {
          clearInterval(timer);
        }
        this.$emit("scrollBottom");
      }, typingSpeed);
      this.$emit("scrollBottom");
    },
    // 本地消息记录以及回显处理
    localMsgHandle(action) {
      const msgList = this.msgList;
      let msgKey = this.sceneKey;
      if (action === "init") {
        const localMsg = this.$store.getters.localMsg[msgKey];
        // const localMsg = this.storageUtil.getItem(`localMsg-${msgKey}`);
        console.log("localMsg:", localMsg);
        this.msgList = localMsg || [];
      } else {
        if (msgList.length > 0) {
          uni.$u.debounce(() => {
            this.$store.dispatch("setLocalMsg", {
              key: `${msgKey}`,
              value: msgList,
            });
            this.$store.dispatch("setLocalMsg", {
              key: `${msgKey}-time`,
              value: new Date().getTime(),
            });
          }, 400);
        }
      }
    },
    // 加载初始页面消息
    async initMsgList(sceneKey, text = "", noHello = false) {
      uni.$u.throttle(async() => {
        console.log("加载初始页面消息.initMsgList ", sceneKey);
        this.sceneKey = sceneKey;
        this.showRetry = false;
        this.localMsgHandle("init");
        this.$nextTick(async () => {
          uni.setNavigationBarTitle({
            title: this.currSceneLabel,
          });
          // this.questionsList = [];
          this.$emit("changeTitle", this.currSceneLabel);
          // 如果存在未响应,则重新发送
          const index = this.msgList.length - 1;
          // 最后一条 是否返回信息是超出两个字
          const talkId = this.storageUtil.getItem('talkId')
          const result = await CHAT_SYNC({talkId})
          if (this.qUtil.validResp(result) && result.code === 200) {
            if( result.data.talkId != talkId){
              this.storageUtil.setItem("talkId", result.data.talkId);
            } 
          }
          if (
            this.msgList[index]?.role === "assistant" &&
            this.msgList[index]?.content.length < 1
          ) {
            if (text && text != "") {
              return;
            }
            const data = this.msgList.filter((item) => item.content != "");
            if (data.length === 0) {
              this.msgList = data;
            }
            if (this.msgList.length === 0 && !noHello) {
              this.msgList.push({
                role: "assistant",
                type: "text",
                show: true,
                content: "",
                questions: [],
              });
              this.$store.dispatch("setMsgStatus", 2);
              const talkId = this.storageUtil.getItem("talkId") || "";
              this.$store.getters.ws.checkConnect();
              const result = await GET_HELLO_MSG({
                scene: this.sceneKey,
                talkId,
              });
              this.errNetFn();
              if (this.qUtil.validResp(result) && result.code === 200) {
                if( result.data.talkId != talkId){
                  this.storageUtil.setItem("talkId", result.data.talkId);
                } 
              } else {
                if (this.msgList[this.msgList.length - 1].role === "assistant")
                  this.msgList[this.msgList.length - 1].netErr = true;
                this.$store.dispatch("setMsgStatus", 0);
              }
            }

            // this.retryMsg();
          } else {
            if (text && text != "") {
              return;
            }

            if (
              this.msgList.length > 0 &&
              text != "" &&
              this.msgList.length < 2
            ) {
              this.$emit("loadHello", {
                content: "**你好",
                show: false,
                isNoSend: true,
              });
            }
            if (this.msgList.length === 0 && !noHello) {
              this.msgList.push({
                role: "assistant",
                type: "text",
                show: true,
                content: "",
                questions: [],
              });
              this.$store.dispatch("setMsgStatus", 2);
              const talkId = this.storageUtil.getItem("talkId") || "";
              this.$store.getters.ws.checkConnect();

              const result = await GET_HELLO_MSG({
                scene: this.sceneKey,
                talkId,
              });
              this.errNetFn();
              if (this.qUtil.validResp(result) && result.code === 200) {
                if( result.data.talkId != talkId){
                  this.storageUtil.setItem("talkId", result.data.talkId);
                } 
              } else {
                if (this.msgList[this.msgList.length - 1].role === "assistant")
                  this.msgList[this.msgList.length - 1].netErr = true;
                this.$store.dispatch("setMsgStatus", 0);
              }
            }

            // }
          }

          this.userList = this.msgList.filter((item) => item.role === "user");
        });
      }, 1000);
    },
    errNetFn() {
      uni.onNetworkStatusChange((res) => {
        if (this.msgList[this.msgList.length - 1].role === "assistant") {
          this.msgList[this.msgList.length - 1].netErr = true;
          this.$store.dispatch("setMsgStatus", 0);
        }
      });
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType == "none") {
            if (this.msgList[this.msgList.length - 1].role === "assistant") {
              this.msgList[this.msgList.length - 1].netErr = true;
              this.$store.dispatch("setMsgStatus", 0);
            }
          }
        },
      });
    },
    editMessage(i, row) {
      if (this.$store.getters.msgStatus !== 0) {
        uni.$u.toast("回复中，暂不支持编辑");
        return;
      }
      if (row.role === "user" && row.flag) {
        this.delIndex = i;
        this.$emit("editMessageFnOpen", row.content);
      }
    },
    deleteMessage() {
      let newArray = this.msgList.filter((_, i) => i < this.delIndex);

      this.msgList = newArray;
    },
  },
};
</script>
<style lang="scss" scoped>
zero-loading {
  height: 50rpx;
  width: 160rpx;
  position: relative;
}
.loadingBox {
  display: flex;
  align-items: center;
  text {
    font-size: 12px;
    color: #666666;
    padding-left: 10rpx;
  }
}
.loading {
  width: 16px;
  height: 16px;
}
</style>
