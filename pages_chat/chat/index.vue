<template>
  <view class="container chat" :style="{ height: '100vh' }">
    <u-navbar :title="pageTitle" :titleStyle={fontWeight:800} @leftClick="backPage" fixed></u-navbar>
    <top-tips
      v-if="currScene != 'S10' && currScene != 'S1' && currScene != 'S9'"
      :show="tipsObj.show"
      :type="tipsObj.type"
      :statusImg="tipsObj.image"
      :btnText="tipsObj.btnText"
      :desc="tipsObj.content"
      :bgColor="tipsObj.bgColor"
      :color="tipsObj.color"
      :btnColor="tipsObj.btnColor"
      :btnShow="tipsObj.btnShow"
      @moveHide="tipsMoveHide"
      @backFn="tipsBackFn"
      :top="tipsObj.show ? tipsTop : ''"
    />
    <view class="content" @touchstart="hideDrawer">
      <scroll-view
        :scroll-with-animation="true"
        id="scrollView"
        :scroll-top="scrollTop"
        :scroll-y="show ? false : true"
        :style="{ height: chatStyle }"
        :class="!tabBar ? 'msg-list noTabBar' : 'msg-list'"
        @scrolltoupper="loadHistory"
      >
        <chat-auth v-if="!isLogin" @authSuccess="authSuccess"></chat-auth>
        <view v-if="tipsObj.show" style="height: 35px;width: 100%;" ></view>
        <chat-main
          v-show="isLogin"
          ref="chatMain"
          :audioLoading="audioLoading"
          @changeTitle="(e) => (pageTitle = e)"
          @loadHello="sendMsg"
          @changeScene="changeScene"
          @scrollBottom="scrollBottom"
          @editMessageFnOpen="editMessageFnOpen"
          :currScene="currScene"
          @setTopTipsShow="setTopTipsShow"
          @loadingFn="loadingFn"
        ></chat-main>
      </scroll-view>
    </view>
    <view>
      <chat-tabbar
        ref="tabBarRef"
        v-if="tabBar"
        :currScene="currScene"
        @changeScene="changeScene"
        @optimization="optimization"
        @stopOpt="stopOpt"
      ></chat-tabbar>

      <chat-toolbar
        ref="ToolBar"
        :currScene="currScene"
        :disable="voiceGetStatus()"
        @sendMsg="sendMsg"
        @changeScene="changeScene"
        @scrollBottom="scrollBottom"
        @loadingFn="loadingFn"
        @pushList="pushList"
        @setMsgList="setMsgList"
        @deleteMessage="deleteMessage"
      ></chat-toolbar>
    </view>
    <option-popup
      ref="optionPopRef"
      :show="show"
      @close="showFn"
      @showTips="showTipsFn"
      :Scene="currScene"
      @setDuration="setDuration"
      @initMain="changeScene"
    />
  </view>
</template>
<script>
import dayjs from "dayjs";
import OptionPopupList from "@/model/enums/optionPopupList";
import ChatAuth from "@/pages_chat/chat/auth";
import ChatMain from "@/pages_chat/chat/main";
import ChatToolbar from "@/pages_chat/chat/toolbar";
import ChatTabbar from "@/pages_chat/chat/tabbar";
import TopTips from "@/components/topTips/index";
import OptionPopup from "@/pages_chat/chat/components/optionPopup/optionPopup.vue";
import { checkLogin } from "@/utils/auth.js";
import {
  EDIT_TARGET_DATA,
  ADD_TARGET_DATA,
  GET_RESULT_LASTEST,
} from "@/api/resume.js";
export default {
  mixins: [],
  components: {
    ChatAuth,
    ChatMain,
    ChatToolbar,
    ChatTabbar,
    OptionPopup,
    TopTips,
  },
  data() {
    return {
      // more参数
      hideMore: true,
      show: false,
      scrollTop: 0,
      currScene: null,
      pageTitle: "",
      optimizeText: "",
      resumeShow: false,
      chatStyle: "calc(100% - 430rpx);",
      tabBar: true,
      audioLoading: false,
      IndexText: "",
      tipsTop: "",
      tipsObj: {
        content: "",
        btnText: "",
        type: "",
        show: false,
        image: "",
        id: "",
      },
      duration: 0,
    };
  },
  computed: {
    isLogin() {
      return checkLogin(false);
    },
  },
  created() {
    let windowHeight = uni.getSystemInfoSync().windowHeight;
    // 状态栏高度
    let statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
    // 获取菜单按钮（右上角胶囊按钮）的布局位置信息
    const custom = wx.getMenuButtonBoundingClientRect();
    let navigationBarHeight =
      custom.height + (custom.top - statusBarHeight) * 2;

    let navHeight = navigationBarHeight + statusBarHeight;
    this.windowHeight = windowHeight - navHeight;
    if (this.systemInfo.platform === "android") {
      this.tipsTop = `${navHeight - 5}px`;
      this.chatStyle = this.windowHeight - 115 + "px";
    } else if (this.systemInfo.platform === "devtools") {
      this.tipsTop = `${navHeight}px`;
    } else {
      this.tipsTop = `${navHeight}px`;
      this.chatStyle = this.windowHeight - 135 + "px";
    }
  },
  onLoad(option) {
    if (option.chatScene) {
      this.changeScene(option.chatScene, option.text);
      if (option.assistantText && option.assistantText != "") {
        this.pushList("assistant", option.assistantText);
      }
    }
    if (option.popFlag) {
      this.show = true;
      this.tipsObj.show = false;
      this.tipsMoveHide();
    }
    // this.stopLastMsg();
    setTimeout(() => {
      this.scrollBottom();
    }, 900);
  },
  onShow() {
    // 判断是否发送已完成测评报告消息 从webview页返回数据
    const evaluationFinish =
      this.storageUtil.getItem("evaluationFinish") || false;
    if (evaluationFinish) {
      this.$nextTick(() => {
        this.sendMsg({ content: "用户测评已完成,请获取测评报告", show: false });
        this.storageUtil.removeItem("evaluationFinish");
      });
    }
    this.$refs.optionPopRef.loadUserInfo();
  },
  methods: {
    checkLogin,
    showFn() {
      this.show = false;
    },
    async changePopTarget(obj, data) {
      if (obj) {
        const res = await EDIT_TARGET_DATA({
          ...data,
          id: obj.id,
        });
        if (this.qUtil.validResp(res)) {
          this.$refs.optionPopRef.loadUserInfo();
        }
      } else {
        const result = await ADD_TARGET_DATA(data);
        if (this.qUtil.validResp(result)) {
          this.$refs.optionPopRef.loadUserInfo();
        }
      }
    },
    stopLastMsg() {
      if (
        this.$refs.chatMain.msgList &&
        this.$refs.chatMain.msgList.length > 0
      ) {
        const index = this.$refs.chatMain.msgList.length - 1;
        if (
          this.$refs.chatMain.msgList[index].role == "assistant" &&
          this.$refs.chatMain.msgList[index].content.length === 0
        ) {
          this.$refs.chatMain.msgList[index].stopShow = true;
          this.$refs.chatMain.localMsgHandle();
        }
      }
    },
    editMessageFnOpen() {
      this.$refs.ToolBar.editMessageFnOpen();
    },
    voiceGetStatus() {
      if (
        this.$refs.chatMain.msgList &&
        this.$refs.chatMain.msgList.length > 0
      ) {
        const ind = this.$refs.chatMain.msgList.length - 1;
        if (
          this.$refs.chatMain.msgList[ind].role === "user" &&
          this.$refs.chatMain.msgList[ind].content === "" &&
          this.audioLoading
        ) {
          return true;
        }
      }

      return false;
    },
    loadingFn(bool) {
      this.audioLoading = bool;
    },
    deleteMessage(sceneKey, msgId) {
      this.$refs.chatMain.deleteMessage(sceneKey, msgId);
    },
    // topTips 操作
    tipsBackFn() {
      if (
        this.tipsObj.type === "error" ||
        this.tipsObj.type === "noEvaluation"
      ) {
        this.show = true;
        this.$refs.optionPopRef.loading = false;
      } else if (this.tipsObj.type === "submitEnd") {
        this.show = true;
      } else if (this.tipsObj.type === "networkError") {
        uni.redirectTo({
          url: `/pages_chat/chat/index?chatScene=${this.currScene}`,
        });
      } else if (this.tipsObj.type === "success") {
        if (this.currScene !== "S6") {
          uni.redirectTo({
            url: `/pages_user/resume/resumeOptimized?scene=${this.currScene}&id=${this.tipsObj.id}&type=OA`,
          });
        } else {
          uni.redirectTo({
            url: `/pages_user/resume/jobMatch?id=${this.tipsObj.id}&scene=${this.currScene}&type=OA`,
          });
        }
      }
    },
    backPage() {
      if (this.$store.getters.msgStatus !== 0) {
        uni.showModal({
          title: "温馨提示",
          content: "对话内容正在接收中,退出该页面会影响数据接收,是否继续退出",
          confirmText: "继续",
          success: (res) => {
            if (res.confirm) {
              this.stopOpt();
              this.navigateUtil.back();
            }
          },
        });
      } else {
        this.navigateUtil.back();
      }
    },
    stopOpt() {
      this.$refs.ToolBar.stopMsg();
      this.stopLastMsg();
    },
    scrollBottom() {
      const query = uni.createSelectorQuery();
      query.select("#scrollView").scrollOffset();
      query.select("#chatMain");
      query.exec((res) => {
        this.scrollTop = res[0].scrollHeight;
      });
    },
    changeScene(sceneKey, text = "", type = "") {
      this.currScene = sceneKey;
      this.$refs.chatMain.sceneKey = sceneKey;
      this.$refs.tabBarRef.initList(sceneKey);
      console.log(sceneKey)
      // 弹窗初始化展开
      if (
        sceneKey !== "S10" &&
        sceneKey !== "S1" &&
        sceneKey !== "S9" &&
        sceneKey !== "S2"
      ) {
        const popupOpt = this.storageUtil.getItem("popupOpt") || "";
        if (!popupOpt[sceneKey]) {
          this.show = true;
          this.tipsObj.show = false;
          this.tipsMoveHide();
          const obj = {};
          obj[sceneKey] = 1;
          this.storageUtil.setItem("popupOpt", { ...popupOpt, ...obj });
          this.$refs.chatMain.initMsgList(sceneKey, text, true);
        } else {
          this.$refs.chatMain.initMsgList(sceneKey, text);
          this.show = false;
        }
      } else {
        this.$refs.chatMain.initMsgList(sceneKey, text);
        this.show = false;
        this.tipsObj.show = false;
        this.tipsMoveHide();
      }
      // 首页引导消息发送
      if (text && text !== "") {
        this.sendMsg({
          scene: sceneKey,
          content: text,
          show: true,
          isNoSend: false,
        });
      }
      if (type == "tabBar") {
        this.$refs.optionPopRef.loadUserInfo();
      }
    },
    tipsMoveHide() {
      if (
        this.currScene !== "S10" &&
        this.currScene !== "S1" &&
        this.currScene !== "S9"
      ) {
        const localTopTips = this.storageUtil.getItem("localTopTips") || "";
        if (!localTopTips[this.currScene]) {
          const obj = {};
          obj[this.currScene] = true;
          if (
            this.tipsObj.type !== "submitEnd" ||
            this.tipsObj.type !== "noEvaluation"
          ) {
            this.storageUtil.setItem("localTopTips", {
              ...localTopTips,
              ...obj,
            });
          }
          // OptionPopupList[this.currScene].show = false;
          this.tipsObj.show = false;
        }
        this.tipsObj.show = false;
      }
    },
    optimization(obj) {
      this.tipsObj.show = false;
      this.currScene = obj?.sceneKey;
      this.show = true;
      this.tipsMoveHide();
      this.$refs.tabBarRef.initList(this.currScene);
    },
    authSuccess() {
      //聊天页内登录,获取问候语或者读取历史聊天记录;
      this.$refs.chatMain.initMsgList(this.currScene);
    },
    // topTips展示信息
    showTipsFn(type, id, time) {
      const topTipObjs = this.storageUtil.getItem("localTopTips") || "";
      if (this.currScene === "S10" || this.currScene === "S1") return;
      if (type == "noResult") {
        this.tipsObj.show = false;
        return;
      }
      setTimeout(()=>{
        if(!topTipObjs[this.currScene]){
          this.tipsObj.show = true; 
        }
       
      },300)
      
      // OptionPopupList[this.currScene].show = true;
      this.getTime(this.duration);

      
      if (topTipObjs && topTipObjs[this.currScene]) {
        this.tipsObj.show = false;
        // OptionPopupList[this.currScene].show = false;
      }
      if (type === "success") {
        this.tipsObj.id = id;
      }
      this.tipsObj.type = type;
      this.tipsObj.content =
        OptionPopupList[this.currScene][type].content +
        (type === "success" ? `${this.getTime(this.duration)}。` : "");
      this.tipsObj.btnText = OptionPopupList[this.currScene][type].btnText;
      // this.$refs.optionPopRef.loading = true;
      if (type !== "submitEnd") {
        this.tipsObj.image = OptionPopupList[this.currScene][type].img;
        this.tipsObj.btnShow = false;
      }else{
        this.tipsObj.btnShow = true;
      }
    },
    // 转换时常
    getTime(data) {
      const date = dayjs(data);
      // const hour = date.hour();
      const minute = date.minute();
      const second = date.second();
      return `${minute}分${second}秒`;
    },
    // 发送消息
    async sendMsg(msg) {
      if (this.isLogin) {
        if (msg.type != "image") {
          this.$nextTick(() => {
            if (msg.content == "") {
              return;
            }
            if (msg.isNoSend) {
              return;
            }
            this.$store.getters.ws.sendMsg({ scene: this.currScene, ...msg });
          });
        }
        this.$refs.chatMain.pushMsg({
          ...msg,
          isNoLoading: msg.isNoSend ? msg.isNoSend : false,
        });
      }
    },
    pushList(type = "user", text) {
      this.$refs.chatMain.msgList.map((item) =>
        item.role == "assistant" ? (item.guideShow = false) : item
      );
      if (type === "assistant") {
        this.$store.dispatch("setMsgStatus", 1);
        //局部优化文案
        this.$refs.chatMain.msgList.push({
          role: "assistant",
          type: "text",
          show: true,
          content: "",
          questions: [],
        });
        this.startTyping(text);
      } else {
        //语音创建一个loading
        if (this.$store.getters.msgStatus == 0) {
          this.$refs.chatMain.msgList.push({
            role: "user",
            content: "",
            type: "text",
            show: true,
            scene: this.currScene,
            variables: {},
            flag: true,
          });
          this.loadingFn(true);
          this.$store.dispatch("setMsgStatus", 1);
        }
      }
    },
    //打字机效果
    startTyping(text) {
      let currentIndex = 0;
      let lastInd = this.$refs.chatMain.msgList.length - 1;
      const typingSpeed = 2000 / text.length; // 打字速度，单位：毫秒
      const timer = setInterval(() => {
        if (currentIndex == text.length - 1) {
          this.$refs.chatMain.msgList[lastInd].copyShow = true;
          this.$store.dispatch("setMsgStatus", 0);
          this.$refs.chatMain.localMsgHandle();
          this.scrollBottom();
        }
        this.$refs.chatMain.msgList[lastInd].content += text[currentIndex];
        currentIndex++;
        if (currentIndex >= text.length) {
          clearInterval(timer);
        }
      }, typingSpeed);
    },
    //设置用户暂停
    setMsgList() {
      if (this.$refs.chatMain.msgList.length > 0) {
        const index = this.$refs.chatMain.msgList.length - 1;
        if (
          this.$refs.chatMain.msgList[index] &&
          this.$refs.chatMain.msgList[index].role == "assistant"
        ) {
          this.$refs.chatMain.msgList[index].stopShow = true;
          this.$refs.chatMain.msgList[index].guideShow = false;
        }
      }
      this.$refs.chatMain.localMsgHandle();
    },
    // 隐藏抽屉
    hideDrawer() {
      this.popupLayerClass = "";
      this.$nextTick(() => {
        this.hideMore = true;
      });
    },
    //触发滑动到顶部(加载历史信息记录)
    loadHistory(e) {
      if (this.isHistoryLoading) {
        return;
      }
      this.isHistoryLoading = true; //参数作为进入请求标识，防止重复请求
    },
    // 麦克风loading
    loadingFn(flag) {
      this.audioLoading = flag;
      // this.isHistoryLoading = flag;
    },
    editMessageFnOpen(text) {
      this.$refs.ToolBar.showEditModal(text);
    },
    deleteMessage() {
      this.$refs.chatMain.deleteMessage();
    },
    // 推送优化状态
    async setTopTipsShow(data) {
      if (this.currScene === data.scene) {
        if (data.success) {
          const result = await GET_RESULT_LASTEST(this.currScene);
          if (
            this.qUtil.validResp(result) &&
            result.code === 200 &&
            result.data
          ) {
            this.setDuration(result.data.duration);
            this.showTipsFn("success", result.data.id);
            this.$refs.optionPopRef.setTopTipsStatus(result.data)
          }
        } else {
          this.showTipsFn("error");
          this.$refs.optionPopRef.loading = false;
        }
      }
    },
    // 保存优化时长
    setDuration(data) {
      this.duration = data;
    },
  },
  beforeDestroy() {
    if (this.$store.getters.msgStatus !== 0) {
      this.stopOpt();
    }
  },
  onHide() {
    // 小程序切换到后台时会调用
    if (this.$store.getters.msgStatus !== 0) {
      this.stopOpt();
      console.log(this.$refs.chatMain.msgList, "this.$refs.chatMain.msgList");
    }
  },
  onUnload() {
    // 页面被关闭时会调用
    if (this.$store.getters.msgStatus !== 0) {
      this.stopOpt();
      console.log(this.$refs.chatMain.msgList, "this.$refs.chatMain.msgList");
    }
  },
};
</script>
<style lang="scss">
@import "@/static/style/chat.scss";

.msg-list {
  background-color: #fff;
}
.none-tabbar {
  width: 100%;
  height: 100rpx;
  position: fixed;
  bottom: 0;
  background-color: #f6f6f6;
}
</style>

<!-- [ { Scene:"S5", status:"success", show:true }, { Scene:"S5", status:"load",
show:true }, { Scene:"S5", status:"error", show:true } ] -->

<!-- {
  "S5":true
} -->
