<template>
  <view class="waveform-loader">
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
    <view class="bar"></view>
  </view>
</template>

<style lang="scss" scoped>
.waveform-loader {
  display: flex;
  align-items: center;
}

.bar {
  width: 2px;
  height: 20px;
  background-color: #fff;
  border-radius: 2px;
  margin-right: 4px;
  animation: wave 1s infinite ease-in-out;
  animation-delay: calc(0.1s * var(--i));
}

/* 具体的动画效果 */
@keyframes wave {
  0%,
  100% {
    height: 5px;
  }
  50% {
    height: 24px;
  }
}

/* 给每个bar设置不同的动画延迟 */
.bar:nth-child(1) {
  --i: 1;
}
.bar:nth-child(2) {
  --i: 2;
}
.bar:nth-child(3) {
  --i: 3;
}
.bar:nth-child(4) {
  --i: 4;
}
.bar:nth-child(5) {
  --i: 5;
}
.bar:nth-child(6) {
  --i: 6;
}
.bar:nth-child(7) {
  --i: 7;
}
.bar:nth-child(8) {
  --i: 8;
}
.bar:nth-child(9) {
  --i: 9;
}
.bar:nth-child(10) {
  --i: 1;
}
</style>
