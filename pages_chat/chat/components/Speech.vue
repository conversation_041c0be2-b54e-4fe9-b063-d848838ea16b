<template>
  <view
    class="downBox"
    @touchstart="streamRecord"
    @touchend="endStreamRecord"
    @touchmove.stop.prevent="handleTouchMove"
  >
    <view :class="'down ' + (noSpeak ? '' : 'show')">
      <view>按住 说话</view>
    </view>
    <view :class="'maskSpeech ' + (!noSpeak ? '' : 'show')">
      <view :class="'maskText ' + (!isSending ? 'cancelText' : '')">{{
        !isSending ? "松手取消" : "松手发送，上移取消"
      }}</view>
      <view :class="'speech ' + (!isSending ? 'cancel' : '')">
        <speech-animation />
        <speech-animation />
        <speech-animation />
        <speech-animation />
      </view>
    </view>
  </view>
</template>

<script>
import speechAnimation from "./speechAnimation.vue";
var plugin = requirePlugin("WechatSI");
let manager = plugin.getRecordRecognitionManager();

export default {
  props: {},
  data() {
    return {
      currentText: "",
      animation: false,
      noSpeak: true,
      startPoint: {},
      isSending: true,
      sendLock: true, //发送锁，当为true时上锁，false时解锁发送
      hasSound: false,
      recorderManager: null,
      microphoneFlag: false,
      text: "",
    };
  },
  components: {
    speechAnimation,
  },
  methods: {
    streamRecord: function (e) {
      const _this = this;
      if (this.$store.getters.msgStatus !== 0) {
        uni.$u.toast("内容输出中，请稍后重试");
        return;
      }
      this.text = "";
      wx.getSetting({
        success(res) {
          if (!res.authSetting["scope.record"]) {
            _this.microphoneFlag = false;
            wx.showModal({
              title: "提示",
              content: "请在小程序设置中打开麦克风权限",
              success(modalRes) {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success(settingRes) {
                      if (settingRes.authSetting["scope.record"]) {
                        console.log("打开了麦克风权限");
                      }
                    },
                  });
                }
              },
            });
          } else {
            _this.microphoneFlag = true;
            _this.startPoint = e.touches[0];
            console.log("开始");
            _this.noSpeak = false;
            _this.isSending = true;
            manager.start({
              lang: "zh_CN",
            });
          }
        },
      });
      // this.$emit("loadingFn", true);
    },
    endStreamRecord: function () {
      //   this.animation = false;
      if (!this.microphoneFlag) return;
      this.noSpeak = true;
      manager.stop();
      this.recorderManager.stop();
      console.log("结束");
      if (!this.isSending) return;
      this.$emit("pushList");
    },
    initRecord: function () {
      //有新的识别内容返回，则会调用此事件
      manager.onRecognize = (res) => {
        let text = res.result;
        this.currentText = text;
        manager.stop();
      };
      // 识别结束事件
      manager.onStop = (res) => {
        if (!this.microphoneFlag) return;
        let text = res.result;
        this.text = res.result;
        if (text == "") {
          this.$emit("loadingFn", false);
          return;
        }
        if (!this.isSending) return;
        this.$emit("speechCallback", text);
        this.$emit("loadingFn", false);
        this.currentText = text;
      };
      manager.onStart = function (res) {
        console.log("录音状态==", res);
      };
    },
    handleTouchMove(e) {
      //touchmove时触发
      var moveLenght = this.startPoint.clientY - 50; //移动距离
      if (e.touches[e.touches.length - 1].clientY < moveLenght) {
        this.isSending = false;
        console.log("松开手指,取消发送");
      } else {
        this.isSending = true;
        console.log("正在录音，上划取消发送");
      }
    },
  },
  onReady() {
    this.recorderManager = uni.getRecorderManager();
    this.initRecord();
  },
};
</script>
<style lang="scss" scoped>
.downBox {
  width: 100%;
  min-height: 80rpx;
  position: relative;
}
.down {
  margin: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  font-weight: 500;
  color: #333333;
  font-size: 18px;
}
.maskSpeech {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background: rgba(0, 0, 0, 0.45);
}
.maskText {
  position: absolute;
  bottom: 220rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 8;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}
.speech {
  position: absolute;
  bottom: 114rpx;
  width: 718rpx;
  height: 96rpx;
  z-index: 8;
  margin: 0 18rpx;
  border-radius: 16rpx;
  background-color: #416bff;
  display: flex;
  align-items: center;
  padding-left: 128rpx;
  box-sizing: border-box;
  image {
    width: 48rpx;
    height: 48rpx;
  }
}
.cancel {
  background-color: #e83324;
}
.cancelText {
  color: #e83324;
}
.show {
  display: none;
}
</style>

<!-- <template>
  <view class="container">
    <canvas
      canvas-id="audioCanvas"
      style="width: 700rpx; height: 80rpx"
    ></canvas>
    <button class="start" @tap="startRecording">开始录音</button>
    <button @tap="stopRecording">停止录音</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      recorderManager: null,
      audioContext: null,
      canvasWidth: 0,
      canvasHeight: 0,
      ctx: null,
      isRecording: false,
      audioData: [], // 用于存储音频数据
      animationId: null,
      drawInterval: null,
    };
  },
  onReady() {
    this.recorderManager = uni.getRecorderManager();
    this.audioContext = uni.createInnerAudioContext();
    this.canvasWidth = uni.upx2px(100); // 以750px为基准，转换为px
    this.canvasHeight = uni.upx2px(80); // 以300rpx为基准，转换为px
    this.ctx = uni.createCanvasContext("audioCanvas", this);
  },
  methods: {
    startRecording() {
      // this.recorderManager = uni.getRecorderManager();
      // if (!this.isRecording) {
      // this.recorderManager.onStart(() => {
      //   this.isRecording = true;
      //   this.drawAnimation();
      // });
      // this.recorderManager.start({
      //   format: "mp3",
      // });
      // }

      this.recorderManager.onFrameRecorded((res) => {
        // const arr = new Uint8Array(res.frameBuffer);
        // this.drawWaveform(arr);
        this.audioData = new Uint8Array(res.frameBuffer);
        // const audioBuffer = new Float32Array(res.frameBuffer);
        // this.audioData = Array.from(audioBuffer).map((value) =>
        //   Math.abs(value * 100)
        // ); // 简单放大音频数据
        // this.drawWaveform();
        this.startDrawing();
      });

      this.recorderManager.start({
        duration: 600000, // 最大录制时长
        sampleRate: 44100, // 采样率
        numberOfChannels: 1, // 单声道
        encodeBitRate: 192000, // 编码码率
        format: "pcm", // 音频格式
        frameSize: 0.02, // 每帧20ms
      });

      // uni
      //   .createSelectorQuery()
      //   .select("#audioCanvas")
      //   .fields({
      //     node: true,
      //     size: true,
      //   })
      //   .exec((res) => {
      //     console.log(res, "----");
      //   });
      // this.initCanvas();
      console.log(this.ctx, "ctx");
    },
    initCanvas() {
      const query = uni.createSelectorQuery().in(this);
      query
        .select("#audioCanvas")
        .fields({ node: true, size: true })
        .exec((res) => {
          console.log(res, "res", this.ctx);
          if (!res[0]) return;
          const canvas = res[0].node;
          this.canvasContext = canvas.getContext("2d");
          this.canvasContext.clearRect(0, 0, canvas.width, canvas.height);
        });
    },
    startDrawing() {
      this.drawInterval = setInterval(this.drawWaveform, 0); // 每秒30帧
    },
    stopRecording() {
      console.log(37777);
      this.recorderManager = uni.getRecorderManager();
      // if (this.isRecording) {
      this.recorderManager.stop();

      if (this.drawInterval) {
        clearInterval(this.drawInterval);
        this.drawInterval = null;
      }
      // this.isRecording = false;
      // }
    },
    drawWaveform() {
      // 清空画布
      // console.log(this.audioData, "this.audioData");
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      const barWidth = (this.canvasWidth / this.audioData.length) * 2.5;
      let barHeight;
      let x = 0;

      this.ctx.fillStyle = "rgba(255, 255, 255, 0)";
      this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

      for (let i = 0; i < this.audioData.length; i++) {
        barHeight = Math.round(this.audioData[i] / 2);
        this.ctx.fillStyle = "#fff";
        this.ctx.fillRect(
          x,
          this.canvasHeight - barHeight,
          barWidth,
          barHeight
        );
        x += barWidth + 1;
      }

      // for (let i = 0; i < this.audioData.length; i++) {
      //   barHeight = this.audioData[i];
      //   this.ctx.fillStyle = "rgb(" + (barHeight + 100) + ",50,50)";
      //   this.ctx.fillRect(x, this.canvasHeight - barHeight, 5, barHeight);
      //   x += barWidth + 1;
      // }
      this.ctx.stroke();
      this.ctx.closePath();
      this.ctx.draw();
      // return;

      // // 绘制波形图
      // this.ctx.beginPath();
      // this.ctx.setStrokeStyle("#ff0000");
      // this.ctx.setLineWidth(2);
      // const step = this.canvasWidth / this.audioData.length;
      // for (let i = 0; i < this.audioData.length; i++) {
      //   const x = i * step;
      //   const y = (1 - this.audioData[i] / 255) * this.canvasHeight;
      //   if (i === 0) {
      //     this.ctx.moveTo(x, y);
      //   } else {
      //     this.ctx.lineTo(x, y);
      //   }
      // }
      // this.ctx.stroke();
      // this.ctx.closePath();

      // // 绘制完成后提交绘制
      // this.ctx.draw();
    },
  },
};
</script>

<style>
.container {
  position: fixed;
  bottom: 100rpx;
  left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
canvas {
  /* border: 1px solid #000; */
  background-color: #416bff;
  height: 96rpx;
  z-index: 8;
  margin: 0 18rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  padding-left: 128rpx;
  box-sizing: border-box;
}

button {
  position: fixed;
  bottom: 200rpx;
  background-color: #007aff;
  color: white;
  border: none;
  /* border-radius: 5px;
  margin-right: 20rpx; */
}
.start {
  position: fixed;
  left: 30rpx;
}
</style> -->

<!-- 

<template>
  <view class="container">
    <canvas canvas-id="audioCanvas" style="width: 100%; height: 50px"></canvas>
    <button @click="startVisualization">111</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      audioContext: null,
      analyser: null,
      canvasContext: null,
      animationId: null,
    };
  },
  methods: {
    startVisualization() {
      this.initAudio();
    },
    initAudio() {
      // 获取音频上下文
      this.audioContext = uni.createInnerAudioContext();
      this.audioContext.src = "YOUR_AUDIO_SOURCE_URL"; // 替换为实际的音频文件URL
      this.audioContext.autoplay = true;

      // 创建分析器
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      const bufferLength = this.analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      // 获取canvas上下文
      const query = uni.createSelectorQuery().in(this);
      query
        .select("#audioCanvas")
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;
          this.canvasContext = canvas.getContext("2d");
          this.canvasContext.clearRect(0, 0, canvas.width, canvas.height);

          const draw = () => {
            this.animationId = requestAnimationFrame(draw);
            this.analyser.getByteFrequencyData(dataArray);
            this.canvasContext.fillStyle = "rgb(0, 0, 0)";
            this.canvasContext.fillRect(0, 0, canvas.width, canvas.height);

            const barWidth = (canvas.width / bufferLength) * 2.5;
            let barHeight;
            let x = 0;

            for (let i = 0; i < bufferLength; i++) {
              barHeight = dataArray[i];
              this.canvasContext.fillStyle =
                "rgb(" + (barHeight + 100) + ",50,50)";
              this.canvasContext.fillRect(
                x,
                canvas.height - barHeight / 2,
                barWidth,
                barHeight / 2
              );

              x += barWidth + 1;
            }
          };

          draw();
        });

      this.audioContext.start();
    },
  },
  onUnload() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    if (this.audioContext) {
      this.audioContext.stop();
    }
  },
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
canvas {
  border: 1px solid #000;
}
button {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 5px;
}
</style> -->

<!-- 

export class SoundDanceAudio {
  canvasWidth = 0; // 画布宽度
  canvasHeight = 0; // 画布高度

  audioContext = null; // 音频上下文
  analyser = null; // 音频分析器
  audioSource = null; // 音频资源节点
  sourceCache = new Set(); // 音频资源缓存，防止GC后，音频中断

  /**
   * 创建音频频谱canvas动画
   * @param {String} canvasId canvas的id
   * @param {Object} options 可选配置
   * {
   *  barWidth,
   *  barHeightScale,
   *  barMargin,
   *  horizonPadding,
   *  fftSize,
   *  onStop,
   *  onError
   * }
   */
  constructor(canvasId, options = {}) {
    this.canvasId = canvasId;
    this.canvas = null;
    this.ctx = null;
    this.animationId = null; // 动画ID

    this.barWidth = options.barWidth || 10; // 音波柱状条宽度
    this.barHeightScale = options.barHeightScale || 100; //音波柱子高度缩放值
    this.barMargin = options.barMargin || 8; // 音波柱状条左右间距
    this.horizonPadding = options.horizonPadding || 5; //水平方向上左右那边距
    this.fftSize = options.fftSize || 1024; // 音频FFT大小 [32, 32768]

    this.fs = wx.getFileSystemManager(); // 文件管理器，用于读取本地音频文件
    this.onStop = options.onStop || null; // 音频or音波动画结束
    this.onError = options.onError || null; //任意报错

    this.createCanvas(this.canvasId);
  }

  /**
   * 创建canvas绘制相关
   */
  createCanvas() {
    const dpr = wx.getWindowInfo().pixelRatio;

    // 创建动画上下文
    wx.createSelectorQuery()
      .select(this.canvasId)
      .fields({
        node: true,
        size: true,
      })
      .exec((res) => {
        // Canvas 对象
        let canvas = res[0].node;
        // 渲染上下文
        let ctx = canvas.getContext("2d");

        // Canvas 画布的实际绘制宽高
        const renderWidth = res[0].width;
        const renderHeight = res[0].height;
        this.canvasWidth = renderWidth;
        this.canvasHeight = renderHeight;

        // 初始化画布大小，以dpr缩放更清晰
        canvas.width = renderWidth * dpr;
        canvas.height = renderHeight * dpr;
        ctx.scale(dpr, dpr);

        // 坐标系转换(画布正中心为原点)
        ctx.translate(renderWidth / 2, renderHeight / 2);
        ctx.scale(1, -1);

        this.canvas = canvas;
        this.ctx = ctx;

        // 绘制测试
        // this.ctx.fillStyle = this.getBarColor(60);
        // let drawH = 10
        // let drawW = 20
        // this.ctx.fillRect(
        //     -drawW / 2,
        //     -drawH / 2,
        //     drawW,
        //     drawH
        // );
        // setTimeout(() => {
        //     this.drawCircle(0, 0, 100)
        // }, 2000);
      });
  }

  /**
   * 创建Web音频上下文控制相关
   * @param {Number} fftSize
   */
  createWebAudioCtx(fftSize = 128) {
    // 创建音频上下文
    this.audioContext = wx.createWebAudioContext();

    // 创建音频分析器
    this.analyser = this.audioContext.createAnalyser();
    this.analyser.fftSize = fftSize; // 设置FFT大小
  }

  /**
   * 开始播放音频
   * @param {String} url 音频地址
   * @param {Boolean} is_remote 是否是在线地址
   * onlineUrl = 'https://website/audio/1698635338898_92102.mp3';
   * localUrl = '/resources/audio/test_audio.mp3';
   */
  startAudio(url, is_remote = false) {
    // !!! 使用的时候再创建，因为在多端应用模式中，会出现调用audioSource.start()不会播放的问题
    this.createWebAudioCtx(this.fftSize);
    let { audioContext, analyser, onStop } = this;

    this.loadAudio(url, is_remote)
      .then((buffer) => {
        let audioSource = audioContext.createBufferSource();
        audioSource.buffer = buffer;
        audioSource.connect(analyser);
        audioSource.connect(audioContext.destination);
        this.sourceCache.add(audioSource); // Tips：缓存住 source，防止被GC掉，GC掉的话音频会中断

        audioSource.onended = () => {
          // 结束动画
          this.stopAnimate();

          // 执行【onStop】回调函数
          onStop && onStop(buffer);
        };
        this.audioSource = audioSource;

        // 开始播放
        try {
          this.audioSource.start();
          // 开始动画
          this.startAnimate();
        } catch (err) {
          console.error(err);
        }
      })
      .catch((err) => {
        console.log("fail", err);
        this.handleError(err);
      });
  }

  /**
   * 停止播放音频
   */
  stopAudio() {
    // 停止音频播放
    this.audioSource.stop();

    // 停止动画
    this.stopAnimate();
  }

  /**
   * 开始动画
   */
  startAnimate() {
    let {
      ctx,
      canvas,
      canvasWidth,
      canvasHeight,
      analyser,
      barWidth,
      barHeightScale,
      barMargin,
      horizonPadding,
      normalizedBuffer,
    } = this;

    // 获取音频数据
    let bufferLength = analyser.frequencyBinCount;
    let dataArray = new Uint8Array(bufferLength);

    // 动画函数
    const animate = () => {
      ctx.clearRect(
        -canvasWidth / 2,
        -canvasHeight / 2,
        canvasWidth,
        canvasHeight
      );

      // 获取音频数据
      analyser.getByteFrequencyData(dataArray);
      let normalizedArr = normalizedBuffer(dataArray);
      // normalizedArr = normalizedArr.filter(item => item > 0.3)

      const barCount = Math.ceil(canvasWidth / (barWidth + barMargin));
      const halfBarCount = Math.floor(barCount / 2);
      const barStep = Math.floor(bufferLength / barCount);

      // 绘制音波柱状条
      for (let i = -halfBarCount; i <= halfBarCount; i++) {
        let index = Math.abs(i) * barStep;
        let item = normalizedArr[index];
        let barHeight = Math.round(item * barHeightScale); // 占位高度
        let x = i * (barMargin + barWidth) - (barMargin + barWidth) / 2;
        let y = Math.ceil(-barHeight / 2); //垂直居中

        // 排除左右边距范围内的渲染
        if (
          x > -canvasWidth / 2 + horizonPadding &&
          x < canvasWidth / 2 - horizonPadding
        ) {
          this.drawItem(x, y, barWidth, barHeight);
        }
      }

      // 继续下一帧动画
      this.animationId = canvas.requestAnimationFrame(animate);
    };

    // 开始动画循环
    animate();
  }

  /**
   * 结束动画
   */
  stopAnimate() {
    const { ctx, canvas, canvasWidth, canvasHeight, animationId, sourceCache } =
      this;
    if (animationId) {
      ctx.clearRect(
        -canvasWidth / 2,
        -canvasHeight / 2,
        canvasWidth,
        canvasHeight
      );
      canvas.cancelAnimationFrame(animationId);
      sourceCache.delete(this.audioSource); // Tips：播放完之后，再清掉source缓存
    }
  }

  drawItem(x, y, w, h, opacity = 1) {
    let baseFixedY = 0.5;
    let baseFixedW = 0.5;
    let radius = w / 2 - baseFixedW;
    opacity = Math.max(0.1, opacity);
    this.drawCircle(
      x,
      h / 2 - baseFixedY,
      radius,
      0,
      Math.PI,
      this.getBarColor(opacity)
    );
    this.drawRect(x, y, w, h, this.getBarColor(opacity));
    this.drawCircle(
      x,
      -h / 2 + baseFixedY,
      radius,
      Math.PI,
      2 * Math.PI,
      this.getBarColor(opacity)
    );
  }

  drawCircle(
    x,
    y,
    radius,
    startAngle = 0,
    endAngle = 2 * Math.PI,
    color = "#ffffff"
  ) {
    this.ctx.beginPath();
    this.ctx.strokeStyle = color;
    this.ctx.fillStyle = color;
    this.ctx.arc(x, y, radius, startAngle, endAngle, false);
    this.ctx.stroke();
    this.ctx.fill();
    this.ctx.closePath();
  }

  drawRect(x, y, w, h, color = "#ffffff") {
    this.ctx.strokeStyle = color; //this.getBarColor(opacity);
    this.ctx.fillStyle = color; //this.getBarColor(opacity);
    this.ctx.fillRect(x - w / 2, y, w, h);
  }

  /**
   * 加载音频文件buffer数据
   * @param {String} url 音频地址
   * @param {Boolean} is_remote 是否是在线地址
   */
  loadAudio(url, is_remote = false) {
    const { audioContext } = this;
    return new Promise((resolve, reject) => {
      if (is_remote) {
        // 处理在线文件
        wx.request({
          url,
          responseType: "arraybuffer",
          success: (res) => {
            audioContext.decodeAudioData(
              res.data,
              (buffer) => {
                resolve(buffer);
              },
              (err) => {
                console.error("decodeAudioData fail", err);
                reject(err);
              }
            );
          },
          fail: (err) => {
            console.error("request fail", err);
            reject(err);
          },
        });
      } else {
        // 处理本地文件
        this.fs.readFile({
          filePath: url,
          success: (res) => {
            // console.log('加载音频数据：', res.data)
            audioContext.decodeAudioData(
              res.data,
              (buffer) => {
                // console.log('音频数据解码：', buffer)
                resolve(buffer);
              },
              (err) => {
                console.error("decodeAudioData fail", err);
                reject(err);
              }
            );
          },
          fail: (err) => {
            console.error("err:", err);
            reject(err);
          },
        });
      }
    });
  }

  getBarColor(opacity = 1) {
    return `rgba(255, 100 ,230, ${opacity})`;
  }

  normalizedBuffer(data = []) {
    let copyData = [...data];
    // 找到音频数据的最大值和最小值
    const max = Math.max(...copyData);
    const min = Math.min(...copyData);

    // 计算音频数据的范围
    const range = max - min;
    // console.log(min, max, range)

    // 对音频数据进行归一化处理，音频数据范围在 0 到 1 之间
    return copyData.map((sample) => (sample - min) / range || 0);
  }

  handleError(err) {
    this.onError && this.onError(err);
  }
} -->
