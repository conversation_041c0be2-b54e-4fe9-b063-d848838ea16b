<template>
  <view class="search" v-if="show">
    <view class="search_input" :class="{ focus: isFocused }">
      <input
        type="text"
        placeholder="请输入感兴趣的公司"
        @input="onSearch"
        @change="onChange"
        @focus="handleFocus"
        @blur="handleBlur"
        v-model="searchQuery"
        :disabled="disabled"
        placeholder-class="custom-placeholder"
      />
      <image
        :class="{ showImage: !isFocused }"
        :src="setImg('images/chat/editClose.png')"
        type="widthFix"
        @click="clearSearch"
      />
    </view>
    <!-- <view class="resultList" v-if="resultFlag">
      <view
        class="result_item"
        v-for="(item, index) in filteredList"
        :key="index"
        @click="handleSelect(item)"
      >
        <rich-text :nodes="highlight(item.name)"></rich-text>
      </view>
    </view> -->
  </view>
</template>

<script>
import global from "@/common/global";
export default {
  props: {
    val: {
      type: String,
      default: "",
    },
    show: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchQuery: "",
      isFocused: false,
      resultFlag: false,
      list: [
        // ... 更多公司名称数据
      ],
    };
  },
  computed: {
    filteredList() {
      return this.list.filter((item) =>
        item.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    },
  },
  methods: {
    setImg(url) {
      return global.STATIC_URL + url;
    },
    onSearch(event) {
      this.searchQuery = event.target.value;
    },
    isOnlySpaces(str) {
        return /^ +$/.test(str);
    },
    onChange(event) {
      if (!event.target.value||this.isOnlySpaces(event.target.value)) {
        uni.$u.toast("请输入感兴趣的公司！");
        return;
      }
      this.$emit("SelectChange", event.target.value);
    },
    highlight(text) {
      const highlightedText = text.replace(
        new RegExp(this.searchQuery, "gi"),
        (match) => {
          return `<span style="color: #18C2A5;">${match}</span>`;
        }
      );
      // 使用Vue的v-html指令来渲染HTML
      return highlightedText;
    },
    handleFocus(e) {

      this.isFocused = true;
    },
    handleBlur(e) {
      if (!e.target.value||this.isOnlySpaces(e.target.value)) {
        uni.$u.toast("请输入感兴趣的公司！");
        return;
      }
      this.isFocused = false;
    },
    clearSearch() {
      this.searchQuery = "";
      this.$emit("SelectChange", "");
      this.resultFlag = false;
    },
    handleSelect(item) {
      this.$emit("SelectChange", item);
      this.resultFlag = false;
    },
    setInput(val) {
      this.searchQuery = val;
      this.onSearch(val);
    },
  },
};
</script>
<style lang="scss" scoped>
.search {
  margin-top: 48rpx;
  &_input {
    padding: 40rpx 32rpx;
    box-sizing: border-box;
    height: 100%;

    border-radius: 16rpx;
    border: 2rpx solid rgba(0, 0, 0, 0.15);
    transition: border-color 0.3s;
    display: flex;
    align-items: center;
  }
  input {
    width: 95%;
  }
  .focus {
    border-color: #18C2A5;
  }
  image {
    width: 32rpx;
    height: 32rpx;
    transform: display 0.3s;
  }
  .showImage {
    display: none;
  }
  ::v-deep.custom-placeholder {
    color: #0000004d;
    font-weight: 600;
    // font-size: 16px;
  }
  .resultList {
    margin-top: 4rpx;
    border-radius: 16rpx;
    padding: 0 32rpx;
    box-sizing: border-box;
    box-shadow: 0 0 16rpx 0 rgba(0, 0, 0, 0.15);
    .result_item {
      font-size: 14px;
      padding: 32rpx 0;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.15);
    }
    .result_item:last-child {
      border-bottom: none;
    }
  }
}
</style>
