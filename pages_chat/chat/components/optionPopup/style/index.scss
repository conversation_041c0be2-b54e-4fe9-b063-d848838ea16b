.optimization_modal {
  height: 1250rpx;
  // height: 80%;
  padding-top: 32rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  .header {
    padding: 0 32rpx;
    padding-left: 42rpx;
  }
  .middle {
    padding-bottom: 20rpx;
    margin: 32rpx 32rpx;
  }
  > text {
    display: inline-block;
    color: #1a1a1a;
    font-size: 16px;
    padding-bottom: 30rpx;
  }
  .resume {
    // width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    &_left {
      color: #fff;
      width: 480rpx;
      height: 270rpx;
      background: url("https://testapi.yujian.chat/static/images/optpop/fileBox.png")
        no-repeat center center;
      background-size: contain;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      margin-right: 12rpx;

      .file_icon {
        width: 50rpx;
        height: 50rpx;
        margin-right: 20rpx;
      }
      .none {
        width: 64rpx;
        height: 64rpx;
        // background-color: #fff;
        margin-left: 38rpx;
        margin-right: 23rpx;
      }
      .tipsText {
        font-size: 16px;
        padding-top: 20rpx;
        > view {
          font-size: 600;
        }
        > text {
          font-size: 24rpx;
        }
      }
    }
    &_right {
      transform: translateY(8px);
      width: 190rpx;
      height: 240rpx;
      // border: 2rpx dashed #b2b2b2;
      border-radius: 18rpx;
      background: url("https://testapi.yujian.chat/static/images/optpop/uploadBox.png")
        no-repeat center center;
      background-size: contain;
      // margin-left: 20rpx;
      // font-size: 16px;
      // padding: 0 40rpx;
      color: #b2b2b2;
      box-sizing: border-box;
      &_box {
        margin: 70rpx auto;
        width: 143rpx;
        font-size: 16px;
        position: relative;
        image {
          position: absolute;
          left: -6rpx;
          top: -11rpx;
          width: 48rpx;
          height: 48rpx;
          transform: translateY(10rpx);
        }
        text {
          display: inline-block;
          text-indent: 2ch;
          letter-spacing: 3rpx;
          width: 143rpx;
          font-weight: 600;
        }
      }
    }
    &_rights {
      transform: translateY(8px);
      width: 190rpx;
      height: 240rpx;
      display: flex;
      justify-content: center;
      border-radius: 18rpx;
      background: url("https://testapi.yujian.chat/static/images/optpop/dashedBg.png")
        no-repeat center center;
      background-size: contain;
      color: #333333;
      box-sizing: border-box;
      &_boxs {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        image {
          width: 64rpx;
          height: 64rpx;
          margin-bottom: 10rpx;
        }
      }
    }
  }
  .job_hunt {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 99%;
    // height: 128rpx;
    padding: 40rpx 32rpx;
    font-weight: 600;
    box-sizing: border-box;
    border-radius: 20rpx;
    margin: 47rpx auto auto;
    line-height: 48rpx;
    box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
    > image {
      width: 50rpx;
      height: 50rpx;
    }
    &_desc {
      display: flex;
      align-items: center;
    }
    &_desc_tit {
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏超出部分 */
      text-overflow: ellipsis; /* 显示省略号 */
      width: 450rpx; /* 设置容器宽度 */
    }
    &_desc text {
      background-color: #d9dff3;
      padding: 0 8rpx;
      border-radius: 4rpx;
      color: #18C2A5;
      font-size: 24rpx;
      line-height: 36rpx;
      margin-left: 16rpx;
    }
    &_text {
      display: inline-block;
      width: 550rpx;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      font-size: 12px;
      line-height: 36rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      &_disable {
        color: #a6a6a6;
        font-size: 24rpx;
        font-weight: 400;
      }
    }
  }
  .job_hunt_none {
    background-color: #f0f0f0;
    box-shadow: none;
    &_text {
      color: #a8a8a8;
      font-size: 22rpx;
      font-weight: 400;
    }
  }
  .p4 {
    padding: 40rpx 32rpx !important;
  }
  .optimization_textarea {
    width: 100%;
    height: 288rpx;
    max-height: 300rpx;
    border: 2rpx solid #d9d9d9;
    display: flex;
    align-items: center;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-top: 47rpx;
    box-sizing: border-box;
    textarea {
      position: relative;
      width: 100%;
      height: 230rpx !important;
      max-height: 300rpx;
      line-height: 40rpx;
      white-space: pre-wrap; /* 保持换行符和空白符 */
      word-wrap: break-word; /* 自动换行 */
      overflow-y: scroll; /* 使textarea垂直滚动 */
    }
  }

  .footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    padding: 20rpx 32rpx 0 32rpx;
    .none_btn {
      color: #E3FFFB;
      border: 2rpx solid #E3FFFB;
      padding: 28rpx 44rpx;
      border-radius: 64rpx;
    }
    .btn_border {
      color: #18C2A5;
      border: 2rpx solid #18C2A5;
      padding: 28rpx 44rpx;
      border-radius: 64rpx;
    }
    .btn {
      color: #E3FFFB;
      padding: 29rpx 134rpx;
      border-radius: 64rpx;
      color: #fff;
      position: relative;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      background: linear-gradient(
        to right,
        rgba(89, 187, 250, 1),
        rgba(136, 159, 248, 1),
        rgba(182, 150, 244, 1)
      );
      &_num {
        position: absolute;
        right: 42rpx;
        top: -18rpx;
        background-color: #fed605;
        font-size: 12px;
        border-radius: 16rpx;
        border-bottom-left-radius: 0;
        color: rgba(0, 0, 0, 0.75);
        padding: 4rpx 22rpx;
        box-shadow: 2rpx 6rpx 8rpx 0 rgba(0, 0, 0, 0.15);
      }
    }
    .btn_none {
      background: linear-gradient(
        to right,
        rgba(89, 187, 250, 0.5),
        rgba(136, 159, 248, 0.5),
        rgba(182, 150, 244, 0.5)
      );
    }
  }
}
.list {
  height: calc(1250rpx - 244rpx);
}

.optimization_modal_loading {
  color: rgba(0, 0, 0, 0.3);
  .header {
    color: rgba(0, 0, 0, 0.3);
  }
  .job_hunt_text {
    color: rgba(0, 0, 0, 0.3);
  }

  .footer {
    position: relative;
    .btn_loading {
      width: 406rpx;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .u-button--primary {
    border-color: #f5f6f7 !important;
    background-color: #f5f6f7 !important;
    color: #fff !important;
  }
  .u-button {
    height: 104rpx !important;
  }
}

.p2 {
  padding: 22rpx 32rpx !important;
}
.careerPlan_text {
  color: #999999;
  font-size: 14px;
  padding-top: 20rpx;
  display: inline-block;
}
// ::v-deep.custom-placeholder {
//   color: #adacb1;
//   font-size: 16px;
//   // line-height: 40rpx;
// }
::v-deep.custom-placeholder {
  color: #0000004d;
  font-weight: 600;
  // font-size: 16px;
}

.df {
  justify-content: center !important;
}

.disable_text {
  color: #0000004d !important;
}
.disable_view {
  box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
}
