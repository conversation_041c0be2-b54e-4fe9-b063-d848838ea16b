<template>
  <view>
    <u-popup
      :show="show"
      mode="bottom"
      zIndex="10070"
      round="16"
      @close="close('close')"
    >
      <view class="optimization_modal">
        <text :class="['header', { disable_text: loading }]">{{
          currSceneText.title
        }}</text>
        <scroll-view
          :scroll-with-animation="true"
          id="scrollView"
          :scroll-top="scrollTop"
          :scroll-y="true"
          class="list"
          :style="scrollMiddleHeight"
          @scrolltoupper="loadHistory"
        >
          <view class="middle" v-if="Scene !== 'S2'">
            <!-- 上传简历 -->
            <view v-if="Scene !== 'S4'">
              <view class="resume" v-if="!showFile">
                <view class="resume_left">
                  <image class="none" :src="setImg('images/uploadIcon.png')" />
                  <view class="tipsText" @click="goToResume('uploadResume')">
                    <view>选择简历上传</view>
                    <text>支持PDF，JPG，PNG等格式</text>
                  </view>
                </view>
                <view
                  class="resume_rights"
                  v-if="initShowFile"
                  @click="goToResume('showResume')"
                >
                  <view class="resume_rights_boxs">
                    <image :src="setImg('images/optpop/fileIcon.png')" />
                    <text>在线简历</text>
                  </view>
                </view>
                <view
                  v-else
                  class="resume_right"
                  @click="goToResume('resumePage')"
                >
                  <view class="resume_right_box">
                    <image :src="setImg('images/add.png')" />
                    <text>无简历 点此创建</text>
                  </view>
                </view>
              </view>
              <!-- 简历回显 -->
              <view class="job_hunt p2" v-else>
                <view @click="goToResumeFile">
                  <view
                    :class="['job_hunt_desc_tit', { disable_text: loading }]"
                  >
                    {{
                      formData.fileName || formData.realName + "的简历"
                    }}</view
                  >
                  <text :class="['job_hunt_text', { disable_text: loading }]">
                    {{ formData.updateTime|| createTime || formData.createTime }}

                    <!-- {{ formData.updateTime || "" }} -->
                    上传</text
                  >
                </view>
                <view
                  class="close_icon"
                  style="
                    width: 30px;
                    height: 30px;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                  "
                >
                  <image
                    style="width: 28rpx; height: 28rpx"
                    :src="
                      !loading
                        ? setImg('images/close.png')
                        : setImg('images/close_disable.png')
                    "
                    @click="deleteResume"
                  />
                </view>
              </view>
            </view>
            <!-- 求职意向 -->
            <view
              v-if="Scene !== 'S7' && Scene !== 'S8'"
              :class="
                'job_hunt p2' + (wantArr.length > 0 ? '' : ' job_hunt_none p4')
              "
              @click="goExpectationFn"
            >
              <view>
                <view :class="[{ disable_text: loading }]">求职意向</view>
                <text
                  :class="['job_hunt_text', { disable_text: loading }]"
                  v-if="wantArr.length > 0"
                >
                  <text v-for="(item, i) in wantArr" :key="i">
                    {{ item + (wantArr.length - 1 === i ? "" : " | ") }}
                  </text>
                </text>
              </view>
              <image
                :src="
                  Object.keys(jobWant).length > 0 && !loading
                    ? setImg('images/right_icon.png')
                    : setImg('images/right_icon_disable.png')
                "
              />
            </view>
            <!-- 目标岗位 -->
            <view
              v-if="Scene !== 'S4' && Scene !== 'S3' && Scene !== 'S6'"
              :class="
                'job_hunt p2' +
                (Object.keys(jobTarget).length > 0 || loading
                  ? ''
                  : ' job_hunt_none')
              "
              @click="
                !loading &&
                  navTo('/pages_user/resume/targetPosition?id=' + jobTarget.id)
              "
            >
              <view>
                <view :class="['job_hunt_desc', { disable_text: loading }]">
                  <view>目标岗位</view>
                  <text>推荐</text>
                </view>
                <text
                  :class="['job_hunt_text', { disable_text: loading }]"
                  v-if="jobTargetArr.length > 0"
                >
                  <text v-for="(item, i) in jobTargetArr" :key="i">{{
                    item + (jobTargetArr.length - 1 === i ? "" : " | ")
                  }}</text>
                </text>
                <text
                  :class="['job_hunt_text_disable', { disable_text: loading }]"
                  v-else
                  >填写本项可根据特定岗位{{ setTargetText(Scene) }}</text
                >
              </view>
              <image
                :src="
                  Object.keys(jobTarget).length > 0 && !loading
                    ? setImg('images/right_icon.png')
                    : setImg('images/right_icon_disable.png')
                "
              />
            </view>
            <!-- 优化描述 -->
            <view class="optimization_textarea" v-if="Scene === 'S5'">
              <textarea
                :cursorSpacing="164"
                v-model="demand"
                height="164"
                border="none"
                :show-confirm-bar="false"
                @blur="handleBlur"
                :disabled="loading"
                maxlength="-1"
                placeholder="如有具体的简历优化要求请在此处填写"
                placeholderStyle="color:#b9b9b9;fontSize:32rpx"
                textareaStyle="borderColor:#00000026;borderRadius:16rpx;"
              ></textarea>
            </view>
            <!-- 面试指导 面试轮次 job_hunt_none -->
            <view
              :class="
                'job_hunt' + (interviewVal || loading ? '' : ' job_hunt_none')
              "
              @click="interviewShowFn"
              v-if="Scene === 'S7'"
            >
              <view :class="['job_hunt_desc', { disable_text: loading }]">
                {{ interviewVal || "面试轮次" }}
              </view>
              <image
                :src="
                  interviewVal && !loading
                    ? setImg('images/right_icon.png')
                    : setImg('images/right_icon_disable.png')
                "
              />
            </view>
            <!-- 行业规划 公司名称搜索 -->
            <view v-if="Scene === 'S4'">
              <view
                v-if="companyFlag"
                class="job_hunt"
                @click="showSearchCompanyFn"
              >
                <view :class="['job_hunt_desc', { disable_text: loading }]">
                  {{ analysisCompanyName || "" }}
                </view>
              </view>
              <search-input
                ref="searchInputRef"
                :show="!companyFlag"
                :disabled="loading"
                @SelectChange="handleSelectChange"
                :val="analysisCompanyName"
              ></search-input>
            </view>
            <!-- 职业规划 查看测评结果 -->
            <view v-if="Scene === 'S3'">
              <view class="optimization_textarea">
                <textarea
                  :cursorSpacing="164"
                  v-model="optimizeText"
                  height="144"
                  maxlength="-1"
                  border="none"
                  @blur="handleBlur"
                  :show-confirm-bar="false"
                  placeholder="职业期望：可具体描述您对理想工作的展望，工作环境，期望城市等等。"
                  textareaStyle="borderColor:#00000026;borderRadius:16rpx;"
                  placeholderStyle="color:#b9b9b9;fontSize:32rpx"
                />
              </view>
              <text class="careerPlan_text"
                >规划分析前，将先进行职业测评，请预留10-30分钟</text
              >
            </view>
          </view>
          <!-- <option-form v-else @submit="optimizationFn" ref="optionForm" /> -->
        </scroll-view>
        <view class="footer df" v-if="loading">
          <view class="btn btn_loading">
            <u-loading-icon size="16" />
            <text style="padding-left: 10rpx">{{
              currSceneText.loadingText
            }}</text>
            <view class="btn_num" v-if="isVipInfo.isVip&&isVipInfo.levelId" >无限次数</view>
            <view class="btn_num" v-else>当前剩余{{remainingTimes}}次</view>
          </view>
          <!-- none_btn -->
        </view>
        <view class="footer" v-else>
          <view class="btn_border" @click="close('close')">{{
            currSceneText.noText
          }}</view>
          <view
            class="btn"
            @click="optimizationFn"
          >

            <view class="btn_num" v-if="isVipInfo.isVip&&isVipInfo.levelId" >无限次数</view>
            <view class="btn_num" v-else >当前剩余{{ remainingTimes }}次</view>
            {{ currSceneText.submitText }}
          </view>
        </view>
      </view>
      <view class="bottomSecure" v-if="isAndroid" ></view>
    </u-popup>
    <resume-popup
      :show.async="resumeShow"
      @close="resumeClose('resume')"
      @getDataOrUrl="getDataOrUrl"
    />
    <interview-status-popup
      :show.async="interviewShow"
      @confirm="interviewChange"
      @close="resumeClose('interview')"
    />
  </view>
</template>
<script>
import {
  GET_RESUME_DATA,
  OPTIMIZATION_OPT,
  POSITION_ONCLICK,
  OFFER_ONCLICK,
  INTERVIEW_ONCLICK,
  GET_RESULT_LASTEST,
  INDUSTRY_ONCLICK,
  CAREER_ONCLICK,
  EXAM_ONCLICK,
} from "@/api/resume.js";
import global from "@/common/global";
import OptionPopupList from "@/model/enums/optionPopupList";
import ResumePopup from "@/components/resumePopup.vue";
import SearchInput from "@/pages_chat/chat/components/searchInput.vue";
import OptionForm from "@/pages_chat/chat/components/optionPopup/optionForm.vue";
import InterviewStatusPopup from "@/pages_chat/chat/components/InterviewStatusPopup.vue";
export default {
  mixins: [],
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    Scene: {
      type: String,
      default: "",
    },
  },
  components: { ResumePopup, InterviewStatusPopup, SearchInput, OptionForm },
  data() {
    return {
      resumeShow: false,
      interviewShow: false,
      scrollMiddleHeight: "height: calc(1250rpx - 244rpx)",
      keyboardFlag: false,
      scrollTop: 0,
      KeyboardHeight: 0, //检测键盘高度
      jobWant: {},
      jobTarget: {},
      jobTargetArr: [],
      wantArr: [],
      showFile: false,
      initShowFile: false,
      companyFlag: false,
      preViewShow: false,
      demand: "",
      isAndroid:true,
      interviewVal: "",
      formData: {
        target: "",
        tempFileUrl: "",
        fileName: "",
      },
      isVipInfo:{
        isVip:0,
        levelId:''
      },
      analysisCompanyName: "",
      loading: false,
      fileDetail: false,
      fileUrl: "",
      createTime: "",
      remainingTimes:0,
    };
  },
  computed: {
    currSceneText() {
      return OptionPopupList[this.Scene];
    },
  },
  mounted() {
    this.loadUserInfo();
    uni.$on("updateResume", () => {
      this.loadUserInfo();
    });
  },
  beforeDestroy() {
    uni.$off("updateResume");
  },
  created() {
    if (this.systemInfo.platform === "android") {
  
      this.isAndroid = true;
    } else {
      this.isAndroid = false;
    }
  },
  methods: {

    setImg(url) {
      return global.STATIC_URL + url;
    },
    interviewShowFn() {
      if (!this.loading) {
        this.interviewShow = true;
      }
    },
    getLocalHello() {
      const popupOpt = this.storageUtil.getItem("popupOpt") || "";
      if (popupOpt[this.Scene] === 1) {
        this.$emit("initMain", this.Scene);
        const obj = {};
        obj[this.Scene] = 2;
        this.storageUtil.setItem("popupOpt", { ...popupOpt, ...obj });
      }
    },
    setTargetText(Scene) {
      if (Scene === "S5") {
        return "优化简历";
      } else if (Scene === "S8") {
        return "生成笔试题";
      } else if (Scene === "S7") {
        return "生成面试题";
      }
    },
    // 关闭
    close(type = "close") {
      this.getLocalHello();
      this.$emit("close");
      if (type !== "close") {
        this.$emit("showTips", type);
      }
    },
    //行业分析 搜索选中回显
    handleSelectChange(data) {
      this.companyFlag = true;
      this.analysisCompanyName = data;
    },
    showSearchCompanyFn() {
      if (this.loading) return;
      this.companyFlag = false;
    },
    // 简历操作
    goToResume(type) {
      if (this.loading) {
        return;
      }
      if (type === "resumePage") {
        this.navTo("/pages_user/resume/createResume");
      } else if (type === "initPage") {
        this.navTo("/pages_user/resume/index");
      } else if (type === "showResume") {
        this.showFile = true;
        this.initShowFile = false;
      } else {
        this.resumeShow = true;
      }
    },
    navTo(url) {
      this.navigateUtil.goto(url);
    },
    // 面试轮次选择
    interviewChange(data) {
      this.interviewVal = data;
    },
    //简历显示隐藏
    resumeClose(type) {
      if (type === "resume") {
        this.resumeShow = false;
      } else {
        this.interviewShow = false;
      }
    },
    // 初始化数据加载
    async loadUserInfo() {
      const userResp = await GET_RESUME_DATA();
      let localData = this.storageUtil.getItem("localData") || {};
      this.isVipInfo = {
        isVip:localData.vuex_user.isVip,
        levelId:localData.vuex_user.levelId||'',
      }
      this.loading = false;
      if (this.qUtil.validResp(userResp) && userResp.code === 200) {
        this.formData = {
          ...this.formData,
          ...userResp.data,
          ...userResp.data?.baseInfo,
        };
        this.showFile = userResp.data?.id ? true : false;
        const userInfo = userResp.data;
        const want = userInfo?.jobWant || {};
        this.jobWant = want;
        this.jobTarget = userInfo?.jobTarget || {};
        if (userInfo?.upload) {
          this.fileUrl = userInfo?.upload?.url;
        }
        this.setJobArr();
      }
      const statusRes = await GET_RESULT_LASTEST(this.Scene);
      if (
        this.qUtil.validResp(statusRes) &&
        statusRes.code === 200 &&
        statusRes.data
      ) {
        this.remainingTimes = statusRes.data.remainingTimes||0;
        if(!this.show){
          this.setTopTipsStatus(statusRes.data);
        }
      } else {
        this.loading = false;
        this.$emit("showTips", "noResult");
      }
    },

    setTopTipsStatus(obj) {
      if (obj.status == 3) {
        this.loading = false;
        this.$emit("setDuration", obj.duration || 0);
        this.$emit("showTips", "success", obj.id, obj.duration);
      } else if (obj.status == 4) {
        this.loading = false;
        this.$emit("showTips", "error");
      } else if (obj.status == 1 || obj.status == 2) {
        if (obj.status == 1 && this.Scene === "S3") {
          this.loading = false;
          this.$emit("showTips", "noEvaluation");
          return;
        }
        this.loading = true;
        this.$emit("showTips", "submitEnd");
      }
    },
    // 求职期望展示
    setJobArr() {
      let wantArr = [];
      let jobTargetArr = [];
      //求职意向列表展示
      if (Object.keys(this.jobTarget || {}).length !== 0) {
        if (this.jobTarget?.companyName) {
          jobTargetArr.push(this.jobTarget?.companyName);
        }
        if (this.jobTarget?.jobName) {
          jobTargetArr.push(this.jobTarget?.jobName);
        }
        if (this.jobTarget?.jobDemand) {
          jobTargetArr.push(this.jobTarget?.jobDemand);
        }
        this.jobTargetArr = jobTargetArr;
      } else {
        this.jobTargetArr = [];
      }
      //求职期望列表展示
      if (Object.keys(this.jobWant || {}).length !== 0) {
        if (this.jobWant?.workType) {
          wantArr.push(this.jobWant?.workType);
        }
        if (this.jobWant?.jobTypeLabel) {
          wantArr.push(this.jobWant?.jobTypeLabel);
        }
        if (this.jobWant?.city) {
          wantArr.push(this.jobWant?.city);
        }
        if (this.jobWant?.startSalary && this.jobWant?.endSalary) {
          wantArr.push(
            this.jobWant?.startSalary === "面议" &&
              this.jobWant?.endSalary === "面议"
              ? this.jobWant?.startSalary
              : `${this.jobWant?.startSalary}k-${this.jobWant?.endSalary}k`
          );
        }
        if (this.jobWant?.industryLabel) {
          wantArr.push(this.jobWant?.industryLabel);
        }
        this.wantArr = wantArr;
      } else {
        this.wantArr = [];
      }
    },
    handleBlur() {
      this.scrollMiddleHeight = "height: calc(1250rpx - 244rpx);";
    },
    //触发滑动到顶部(加载历史信息记录)
    loadHistory(e) {
      if (this.isHistoryLoading) {
        return;
      }
      this.isHistoryLoading = true; //参数作为进入请求标识，防止重复请求
    },
    // 跳转求职期望
    goExpectationFn() {
      if (this.loading) {
        return;
      }
      // 跳转期望岗位
      if (Object.keys(this.jobWant).length === 0) {
        this.navTo(`/pages_user/resume/expectation`);
      } else if (this.jobWant?.id) {
        this.navTo(`/pages_user/resume/expectation?id=${this.jobWant?.id}`);
      }
    },
    // 上传文件后回调函数
    getDataOrUrl(data) {
      if (data) {
        this.resumeShow = false;
        this.formData = { ...this.formData, ...data };
        this.fileDetail = true;
        this.createTime = this.formData.upload?.createTime;
        this.formData.fileName = data.fileName;
        this.preViewShow = true;
        this.loadUserInfo();
      }
    },
    goToResumeFile() {
      if (this.loading) {
        return;
      }
      if (this.fileDetail) {
        if (this.systemInfo.platform === "android") {
          let localData = this.storageUtil.getItem("localData") || {};
          const url = encodeURIComponent(
            this.staticBaseUrl +
              "h5/webView/?token=" +
              localData.vuex_token +
              "&clientid=" +
              this.clientId
          );
          this.navTo(`/pages_h5/webview/index?url=${url}`);
        } else {
          this.navTo(`/pages_h5/webview/index?url=${this.fileUrl}`);
        }
      } else {
        this.navTo(`/pages_user/resume/index`);
      }
    },
    // 删除简历
    deleteResume() {
      if (this.loading) return;
      this.showFile = false;
      this.initShowFile = true;
    },
    // 提交
    async optimizationFn() {
      console.log('优化开始')
      let result = "";
      // 上传简历
      if (this.Scene != "S2" && this.Scene != "S4") {
        if (!this.formData?.id || !this.showFile) {
          uni.$u.toast("请上传简历!");
          return;
        }
      }
      // 求职期望
      if (this.Scene != "S2" && this.Scene != "S7" && this.Scene != "S8") {
        if (!this.formData.jobWant) {
          uni.$u.toast("请填写求职期望!");
          return;
        }
      }
      // 面试轮次
      if (this.Scene == "S7") {
        if (!this.interviewVal) {
          uni.$u.toast("请选择面试轮次！");
          return;
        }
      }
      // 感兴趣的公司
      if (this.Scene == "S4") {
        if (!this.analysisCompanyName) {
          uni.$u.toast("请输入感兴趣的公司！");
          return;
        }
      }
      if (this.Scene === "S5") {
        result = await OPTIMIZATION_OPT({
          demand: this.demand,
          // test: true,
        });
      } else if (this.Scene === "S2") {
        result = await OFFER_ONCLICK({
          offers: this.$refs.optionForm.data || [],
        });
      } else if (this.Scene === "S6") {
        result = await POSITION_ONCLICK();
      } else if (this.Scene === "S7") {
        result = await INTERVIEW_ONCLICK({
          rounds: this.interviewVal,
          demand: this.demand,
        });
      } else if (this.Scene === "S3") {
        console.log("职业测评");
        result = await CAREER_ONCLICK({
          expect: this.optimizeText,
        });
      } else if (this.Scene === "S4") {
        // console.log("行业分析");
        result = await INDUSTRY_ONCLICK({
          companyName: this.analysisCompanyName,
        });
        this.analysisCompanyName = "";
      } else if (this.Scene === "S8") {
        result = await EXAM_ONCLICK();
      }
      if (result && this.qUtil.validResp(result) && result.code == 200) {
        this.remainingTimes--;
        this.getLocalHello();
        this.loading = true;
        if (this.Scene === "S3" && !result.data.finished) {
          this.navTo(
            `/pages_h5/webview/index?url=${encodeURIComponent(
              result.data.testUrl
            )}&type=noPDF`
          );
        }
        this.$emit("close");
        let localTopTips = this.storageUtil.getItem("localTopTips") || "";
        if ((localTopTips && localTopTips[this.Scene]) || "") {
          delete localTopTips[this.Scene];
          if (Object.keys(localTopTips).length === 0) {
            this.storageUtil.removeItem("localTopTips");
          } else {
            this.storageUtil.setItem("localTopTips", localTopTips);
          }
        }
        this.close("submitEnd");

      } else {
        uni.showModal({
          title: "",
          content: result.msg,
          confirmText: "关闭",
          showCancel: false,
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./style/index.scss";
.bottomSecure {
  width: 100%;
  height: 30rpx;
}
</style>
