<template>
  <view class="FromBox">
    <view v-for="(item,index) in data" :key="index"  style="margin-top:20rpx;">
      <view class="option_item flex-between" @click="deleteOption(index)">
        <text>offer {{ index+1 }}</text>
        <image :src="setImg('images/delete.png')" />
      </view>
      <u-form
        :model="item"
        :rules="rules"
        labelPosition="top"
        label-width="100"
        :labelStyle="{color:'#00000099',fontSize:'14px',padding:'28rpx 0'}"
        ref="uForm"
      >
        <u-form-item label="公司" borderBottom prop="item.company">
          <input
            v-model="item.company"
            border="none"
            placeholder="请输入公司名称"
            placeholder-class="custom-placeholder" 
          ></input>
        </u-form-item>
        <u-form-item
            label="地区"
            borderBottom
            prop="item.city"
            @click="openCity(index)"
          >
          <view class="flex-between">
            <input
              v-model="item.city"
              placeholder="请选择只为所在地区"
              disabled
              readonly
              placeholder-class="custom-placeholder" 
            />
            <image class="right_icon"  :src="setImg('images/right_icon.png')" />
          </view>
        </u-form-item>
        <u-form-item
            label="岗位"
            borderBottom
            prop="item.posName"
            @click="getJob(index)"
          >
          <view class="flex-between">
            <input
              v-model="item.posName"
              placeholder="请选择岗位名称"
              readonly
              disabled
              placeholder-class="custom-placeholder" 
            />
            <image class="right_icon"  :src="setImg('images/right_icon.png')" />
          </view>
        </u-form-item>
        <u-form-item label="薪资福利" borderBottom prop="salary">
          <input
            v-model="item.salary"
            border="none"
            placeholder="请输入薪资及相关福利"
            placeholder-class="custom-placeholder" 
          ></input>
        </u-form-item>
      </u-form>
    </view>
    <view class="add" @click="addOption" >
      <image :src="setImg('images/add.png')" />
      <text>添加offer</text>
    </view>
    <barry-picker ref="cityPicker" @get-address="getCity"></barry-picker>
  </view>
</template>
<script>
import global from "@/common/global";
export default {
  name: "OptionForm",
  props: {
    option: {
      type: Object,
      default: () => ({}),
    },
  },
  onReady(){
  },
  mounted(){
    uni.$on("updateJobPosition", (data) => {
      this.data[this.currentInd].jobType = data.id;
      this.data[this.currentInd].posName = data.name;
    });
  },
  data() {
    return {
      form: {
        company: "",
        posName:"",
        city:"",
        salary:""
      },
      currJobType:null,
      flag:false,
      data: [
        {
          company: "",
          posName:"",
          city:"",
          salary:"",
          jobType:null
        }
      ],
      currentInd:0,
      rules: {
        city: [
          {
            required: true,
            message: "请选择所在地区",
            trigger: "blur",
          },
        ],
        posName: [
          {
            required: true,
            message: "请选择岗位名称",
            trigger: "blur",
          },
        ],
        salary: [
          {
            required: true,
            message: "请输入薪资及相关福利",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    openCity(index) {
      // 保存当前index
      this.currentInd = index;
      this.$refs.cityPicker.show = true;
    },
    getCity(e) {
      this.data[this.currentInd].city = e;
    },
    getJob(index) {
      this.flag = true;
      this.currentInd = index;
      uni.navigateTo({
        url: "/pages_user/resume/jobSelect?jobType=" + this.data[this.currentInd].jobType,
      });
    },
    addOption(){
      this.flag = false;
      if(this.data.length>=5){
        uni.showToast({
          title:"最多添加5条",
          icon:"none"
        })
        return
      }
      this.data.push({
        company: "",
        posName:"",
        city:"",
        salary:"",
        jobType:null
      })
    },
    deleteOption(i){
      this.data = this.data.filter((item,ind)=>ind!==i)
    },
    setImg(url) {
      return global.STATIC_URL + url;
    },
  },
};
</script>
<style lang="scss" scoped>
.right_icon {
  width: 40rpx;
  height: 40rpx;
  display: inline-block;
  // margin-left: 20rpx;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.FromBox {
  margin-top: 20rpx;
  padding: 0 32rpx;
}
::v-deep.custom-placeholder {
  color: #0000004D;
  font-weight: 600;
  // font-size: 16px;
}
.add {
  width: 686rpx;
  height: 88rpx;
  border-radius: 8rpx;
  background-color: #F3F3F3;
  border: 1px dashed #999999;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 44rpx;
  image {
    width: 60rpx;
    height: 60rpx;
  }
  text {
    font-size: 16px;
    color: #0000004D;
  }
}
.option_item {
  image {
    width: 32rpx;
    height: 32rpx;
  }
}
</style>
