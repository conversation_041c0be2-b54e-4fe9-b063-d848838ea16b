<template>
  <u-popup :show="show" round="19">
    <view class="content">
      <view class="header">
        <text @click="close">取消</text>
        <view>面试轮次</view>
        <text @click="hanldConfirm">确定</text>
      </view>
      <view class="pickerView">
        <picker-view
          class="picker"
          indicator-class="indicator"
          :value="value"
          @change="bindChange"
        >
          <picker-view-column class="column">
            <view
              class="text"
              v-for="(item, index) in InterviewList"
              :key="index"
              >{{ item }}</view
            >
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
    },
    sex: {
      type: Number,
    },
  },
  data() {
    return {
      value: [0],
      InterviewList: ["初面/群面", "二面/专业面试", "HR面试", "终面/高管面试"],
    };
  },

  methods: {
    bindChange(e) {
      this.value = e.detail.value;
    },
    close() {
      this.$emit("close");
    },
    hanldConfirm() {
      this.$emit("confirm", this.InterviewList[this.value[0]] || "");
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
// .content {
//   padding: 0 38rpx;
// }
.header {
  display: flex;
  justify-content: space-between;
  height: 110rpx;
  align-items: center;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #e6e6e6;
  text:first-child {
    font-size: 31rpx;
    color: #666666;
  }
  text:last-child {
    font-size: 31rpx;
    color: #18C2A5;
  }
  view {
    font-size: 35rpx;
    font-weight: 500;
    color: #333333;
  }
}
.pickerView {
  padding: 100rpx 0;
  .picker {
    background-color: #f4f4f5;
    width: 100%;
    height: 247rpx;
  }
}
</style>
<style lang="scss" scoped>
::v-deep .column {
  font-size: 31rpx;
  color: #000;
  text-align: center;
  line-height: 96rpx;
}
// ::v-deep .indicator::after {
//   border-bottom: 0 !important;
// }

// ::v-deep .indicator::before {
//   border-top: 0 !important;
// }
::v-deep .column .indicator {
  width: 100% !important;
  border-radius: 48rpx;
  overflow: hidden;
  // background-color: #eee;
  // opacity: 0.6;
  height: 96rpx;
}
</style>
