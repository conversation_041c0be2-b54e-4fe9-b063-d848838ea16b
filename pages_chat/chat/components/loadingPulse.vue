<template>
  <view class="container">
    <view
      class="pulse-bubble pulse-bubble-1"
      :style="{ background: bgColor }"
    ></view>
    <view
      class="pulse-bubble pulse-bubble-2"
      :style="{ background: bgColor }"
    ></view>
    <view
      class="pulse-bubble pulse-bubble-3"
      :style="{ background: bgColor }"
    ></view>
  </view>
</template>

<script>
export default {
  props: {
    bgColor: {
      type: String,
      default: "#999",
    },
    overtime: {
      type: Number,
      default: 5 * 1000,
    },
    noNetwork: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    if (this.noNetwork) {
      this.timeoutId = setTimeout(() => {
        // 在超时后执行的方法
        this.handleOvertime();
      }, this.overtime);
    }
  },
  methods: {
    handleOvertime() {
      this.$emit("overtime");
    },
  },
  data() {
    return {};
  },
  beforeDestroy() {
    // 在组件销毁前清除setTimeout
    if (this.timeoutId && this.noNetwork) {
      clearTimeout(this.timeoutId);
    }
  },
};
</script>

<style lang="scss" scoped>
/* pulse */
.container {
  max-width: 90rpx;
  display: flex;
  align-items: center;
}

.pulse-bubble {
  width: 16rpx;
  height: 16rpx;
  margin-right: 8rpx;
  border-radius: 50%;
  background: #999;
}

.pulse-bubble-1 {
  background: #999;
  animation: pulse 0.4s ease 0s infinite alternate;
}

.pulse-bubble-2 {
  background: #999;
  animation: pulse 0.4s ease 0.2s infinite alternate;
}

.pulse-bubble-3 {
  background: #999;
  animation: pulse 0.4s ease 0.4s infinite alternate;
}

@keyframes pulse {
  from {
    opacity: 1;
    // transform: scale(1.25);
  }

  to {
    opacity: 0.25;
    // transform: scale(0.75);
  }
}
</style>
