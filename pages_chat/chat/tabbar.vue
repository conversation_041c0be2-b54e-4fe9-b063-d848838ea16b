<template>
  <view class="tab-view" :style="{ bottom: tabBottom }">
    <scroll-view
      :scroll-x="true"
      :scroll-with-animation="true"
      :scroll-left="scrollLeft"
      @scroll="onScroll"
    >
      <view class="tab-box">
        <view
          :class="['tab-item', scene.active && 'item']"
          v-for="(scene, fdx) in chatScene"
          :key="fdx"
          @click="changeMenu(scene.key)"
          v-if="!scene.hidden"
        >
          <image :src="scene.image" mode="widthFix"></image>
          <text>{{ scene.label }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import ChatScene from "@/model/enums/ChatScene";
import { checkLogin } from "@/utils/auth.js";
export default {
  data() {
    return {
      userInfo: {},
      msgList: [],
      msgImgList: [],
      memberId: 0,
      chatScene: [],
      sceneKey: "",
      scrollLeft: 0,
      lastScrollLeft: 0,
      // 是否显示重试文案
      showRetry: false,
      tabBottom: "166rpx",
    };
  },
  props: {
    currScene: {
      type: String,
      default: "",
    },
  },
  computed: {},
  mounted() {
    if (this.systemInfo.platform === "android") {
      this.tabBottom = "150rpx";
    } else {
      this.tabBottom = "203rpx";
    }
  },
  methods: {
    initList(type) {
      this.sceneKey = type;
      let newChatScene = ChatScene;
      if (type === "S10" || type === "S1" || type === "S2" || type === "S9") {
        newChatScene = ChatScene.filter((item) => {
          return item.key !== "" && item.key !== type;
        });
      } else {
        newChatScene = ChatScene.map((item) => {
          if (item.key === "") {
            if (type === "S3") {
              item.label = "一键规划";
            } else if (type === "S4") {
              item.label = "一键分析";
            } else if (type === "S7" || type === "S8") {
              item.label = "一键指导";
            } else if (type === "S6") {
              item.label = "一键匹配";
            } else {
              item.label = "一键优化";
            }
            item.sceneKey = type;
          }

          return item;
        });
        newChatScene = ChatScene.filter((item) => {
          return item.key !== type;
        });
      }

      this.chatScene = newChatScene;
    },
    onScroll(event) {
      // 记录当前滚动位置
      this.lastScrollLeft = event.detail.scrollLeft;
    },
    // 切换场景
    changeMenu(key) {
      if (!checkLogin(false)) {
        return uni.showModal({
          showCancel: false,
          title: "提示",
          content: "请先授权登陆后再进行操作!",
        });
      }
      const targetObj = this.chatScene.filter((item) => item.key === key);
      // 一键优化
      if (key === "") {
        this.$emit("optimization", targetObj[0]);
        return;
      }
      if (this.$store.getters.msgStatus !== 0) {
        uni.showModal({
          title: "温馨提示",
          content: "对话内容正在接收中,切换场景会影响数据正确响应,是否继续切换",
          confirmText: "继续",
          success: (res) => {
            if (res.confirm) {
              this.$emit("stopOpt");
              this.$emit("changeScene", key);
              this.initList(key);
            }
          },
        });
      } else {
        this.$emit("changeScene", key, "", "tabBar");
      }
      this.sceneActive = false;
      setTimeout(() => {
        this.scrollLeft = 0;
      }, 600);
    },
  },
};
</script>

<style></style>
