// 引入依赖
import Vue from "vue";
import App from "@/App";
import queryUtil from "@/utils/queryUtil";
import baseUtil from "@/utils/baseUtil";
import storageUtil from "@/utils/storageUtil";
import uniRequest from "@/utils/request";
import navigateUtil from "@/utils/navigateUtil";
import store from "@/store";
import uView from "@/uni_modules/uview-ui";
import dayjs from "dayjs";
import global from "@/common/global";
import Modal from "@/components/modal.vue";
Vue.component("Modal", Modal);

// 赋别名
Vue.prototype.$store = store;
Vue.prototype.baseUtil = baseUtil;
Vue.prototype.qUtil = queryUtil;
Vue.prototype.storageUtil = storageUtil;
Vue.prototype.navigateUtil = navigateUtil;
Vue.prototype.uniRequest = uniRequest;
Vue.prototype.$dayjs = dayjs;
Vue.prototype.staticBaseUrl = global.STATIC_URL;
Vue.prototype.clientId = global.CLIENT_ID;
Vue.prototype.systemInfo = uni.getSystemInfoSync();
Vue.prototype.openModal = (data) => {
  uni.$emit("openModal", data);
};

// 禁用生产提示
Vue.config.productionTip = false;
// 指定应用程序类型
App.mpType = "app";

Vue.use(uView);

// #ifdef MP
// 引入uView对小程序分享的mixin封装
const mpShare = require("@/uni_modules/uview-ui/libs/mixin/mpShare.js");
Vue.mixin(mpShare);
// #endif

const app = new Vue({
  store,
  ...App,
});

app.$mount();
