<template>
  <view class="select">
    <view class="selectBox">
      <view class="selectTypeBox">
        <scroll-view
          class="scrollBoxL"
          :scroll-top="scrollBoxL"
          :scroll-y="true"
          :show-scrollbar="false"
          :scroll-with-animation="scrollWithAnimation"
          :scroll-into-view="scrollLIndex"
        >
          <view class="selectTypeList">
            <view
              class="selectTypeItem"
              v-for="(item, index) in dataLeftList"
              :key="index"
              :id="'scrollLIndex' + index"
              :class="
                `scrollLIndex${index}` === scrollLIndex &&
                `${selectClass} active`
              "
              @click="clickLItem(item, index)"
            >
              <view class="typeSlot leftTitle">
                <!-- 只在微信小程序编译 -->
                <!--  #ifdef MP-WEIXIN -->
                <slot :name="`type${index}`"></slot>
                <!--  #endif -->
                <!-- 除微信小程序以外编译 -->
                <!--  #ifndef MP-WEIXIN -->
                <slot name="type" :item="item" :index="index">{{
                  item[options.leftTitle]
                }}</slot>
                <!--  #endif -->
              </view>
              <!-- 只在微信小程序编译 - 默认值 -->
              <!--  #ifdef MP-WEIXIN -->
              <view class="typeSlotDefault leftTitle">{{
                item[options.leftTitle]
              }}</view>
              <!--  #endif -->
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="selectContentBox">
        <scroll-view
          class="scrollBoxR"
          :scroll-top="scrollBoxR"
          :scroll-y="true"
          :show-scrollbar="false"
          :scroll-with-animation="scrollWithAnimation"
        >
          <view class="selectContentList">
            <view v-for="(item, index) in dataRightList" :key="index">
              <view class="minTypeTitleSlot minTitle">
                <!-- 只在微信小程序编译 -->
                <!--  #ifdef MP-WEIXIN -->
                <slot :name="'minTitle' + index"></slot>
                <!--  #endif -->
                <!-- 除微信小程序以外编译 -->
                <!--  #ifndef MP-WEIXIN -->
                <slot name="minTitle" :item="item" :index="index">
                  {{ item[options.minTitle] }}
                </slot>
                <!--  #endif -->
              </view>
              <!-- 只在微信小程序编译 - 默认值 -->
              <!--  #ifdef MP-WEIXIN -->
              <view class="minTypeTitleSlotDefault minTitle">
                {{ item[options.minTitle] || item[options.leftTitle] }}
              </view>
              <!--  #endif -->
              <view class="minTypeContent">
                <view
                  class="selectContentItem"
                  v-for="(itemChild, indexChild) in item[options.child]"
                  :key="indexChild"
                  :class="itemChild.active && 'active'"
                  @click="selectValueFn(itemChild, indexChild)"
                >
                  <view class="contentSlot label">
                    <!-- 只在微信小程序编译 -->
                    <!--  #ifdef MP-WEIXIN -->
                    <slot :name="'label' + index + indexChild"></slot>
                    <!--  #endif -->
                    <!-- 除微信小程序以外编译 -->
                    <!--  #ifndef MP-WEIXIN -->
                    <slot
                      name="label"
                      :itemChild="itemChild"
                      :indexChild="indexChild"
                    >
                      {{ itemChild[options.label] }}
                    </slot>
                    <!--  #endif -->
                  </view>
                  <!-- 只在微信小程序编译 - 默认值 -->
                  <!--  #ifdef MP-WEIXIN -->
                  <view class="contentSlotDefault label">
                    {{ itemChild[options.label] }}
                  </view>
                  <!--  #endif -->
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 一个二级分类选择器
 */
export default {
  props: {
    value: {
      type: [Object, Array],
      default() {
        return [];
      },
    },
    data: {
      type: Array,
      default() {
        return [];
      },
    },
    multiple: {
      type: Boolean,
      default() {
        return false;
      },
    },
    scrollWithAnimation: {
      type: Boolean,
      default() {
        return true;
      },
    },
    options: {
      type: Object,
      default() {
        return {
          id: "id",
          leftTitle: "leftTitle",
          minTitle: "minTitle",
          child: "child",
          label: "label",
        };
      },
    },
    selectClass: {
      type: String,
      default() {
        return "";
      },
    },
  },
  data() {
    return {
      scrollBoxL: 0, // 左侧滚动条位置
      scrollBoxR: 0, // 右侧滚动条位置
      scrollBoxRHeightArr: [],
      scrollLIndex: "scrollLIndex0",
      scrollTimer: null,
      dataLeftList: [],
      dataRightList: [],
      selectData: [],
    };
  },
  watch: {
    data: {
      deep: true,
      handler(val) {
        this.init();
      },
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      let dataList = this.data || [];
      this.dataRightList = dataList[0] ? dataList[0][this.options.child] : [];
      this.dataLeftList = dataList;
    },
    // 点击左侧滚动到当前位置并同步右侧
    clickLItem(item, index) {
      this.scrollLIndex = "scrollLIndex" + index;
      this.dataRightList = this.data[index][this.options.child] || [];
    },
    // 清空选中项
    clear() {
      for (let i = 0; i < this.dataRightList.length; i++) {
        let c = this.dataRightList[i][this.options.child];
        for (let j = 0; j < c.length; j++) {
          c[j].active = false;
        }
      }
    },
    // 选中数据
    selectValueFn(itemLeaf, index) {
      // itemLeaf.active = !itemLeaf.active;
      if (this.multiple) {
        if (itemLeaf.active) {
          this.selectData.push(itemLeaf);
        } else {
          this.selectData = this.selectData.filter((m) => m.id !== itemLeaf.id);
        }
        this.$set(itemLeaf, "active", !itemLeaf.active);
      } else {
        this.clear();
        this.$set(itemLeaf, "active", true);
        this.selectData = [itemLeaf];
      }
      // ↓解决选中数据不回显，需要滚动页面才能回显
      // this.scrollBoxR++;
      // this.scrollBoxR--;
      this.setValue();
    },
    // 设置值
    setValue() {
      this.$emit("input", this.selectData);
      this.$emit("select", this.selectData);
      this.$emit("selectValue", this.selectData);
    },
  },
};
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: relative;
  box-sizing: border-box;
  font-size: 28rpx;

  .selectBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .selectTypeBox {
    width: 220rpx;
    background-color: #f5f6f7;
    height: 100%;

    .scrollBoxL {
      height: 100%;

      .selectTypeList {
        .selectTypeItem {
          min-height: 84rpx;
          line-height: 84rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          background-color: #f5f6f7;
          color: #333;
          padding: 0 10rpx 0 30rpx;

          &.active {
            background-color: #fff;
            color: #18c2a5;
          }

          .leftTitle {
          }

          .typeSlot {
            &:empty {
              display: none;

              & + .typeSlotDefault {
                display: block;
              }
            }
          }

          .typeSlotDefault {
            display: none;

            &.leftTitle {
              text-align: left;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              //text-align: center;
            }
          }
        }
      }
    }
  }

  .selectContentBox {
    flex: 1;
    height: 100%;
    overflow: auto;

    .scrollBoxR {
      height: 100%;

      .selectContentList {
        padding: 0 30rpx;

        .minTypeTitleSlot {
          &:empty {
            display: none;

            & + .minTypeTitleSlotDefault {
              display: block;
            }
          }
        }

        .minTitle {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-start;
          padding: 10rpx 0 30rpx 0;
          font-weight: bold;
        }

        .minTypeContent {
          display: flex;
          justify-content: space-between;
          align-items: stretch;
          flex-wrap: wrap;
        }

        .selectContentItem {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 47%;
          box-sizing: border-box;
          padding: 12rpx 16rpx;
          border-radius: 8rpx;
          background-color: #f5f5f5;
          text-align: center;
          color: #333;
          font-size: 28rpx;
          line-height: 44rpx;
          margin-bottom: 25rpx;
          border: 2rpx solid #f5f5f5;

          &.active {
            color: #18c2a5;
            background-color: #e3fffb;
            border-color: #18c2a5;
            position: relative;
          }

          .label {
          }

          .contentSlot {
            &:empty {
              display: none;

              & + .contentSlotDefault {
                display: block;
              }
            }
          }

          .contentSlotDefault {
            display: none;

            &.label {
              // padding: 12rpx 24rpx;
            }
          }
        }
      }
    }
  }
}
</style>
