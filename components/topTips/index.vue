<template>
  <view>
    <view
      :class="'status'"
      v-if="show"
      :style="{ top: show ? top : '',background:bgColor }"
      @touchstart="startMove"
      @touchmove.stop.prevent="moveHide"
      @click="backFn"
    >
      <view class="status_left">
        <image :src="statusImg" v-if="type != 'submitEnd'" />
        <u-loading-icon color="#18C2A5" size="16" v-if="type == 'submitEnd'" />
        <text :style="{color:color}">{{ desc }}</text>
      </view>
      <view class="status_right"  :style="{background:btnColor,opacity: btnShow?0:1}" @click="backFn"> {{ btnText }}</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    desc: {
      type: String,
      default: "",
    },
    btnText: {
      type: String,
      default: "",
    },
    statusImg: {
      type: String,
      default: "",
    },
    styles: {
      type: String,
      default: () => {},
    },
    show: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    top: {
      type: String,
      default: "",
    },
    bgColor:{
      type: String,
      default: "#e6ebff",
    },
    color:{
      type: String,
      default:'rgba(87, 123, 255, 1)'
    },
    btnColor:{
      type: String,
      default:'rgba(87, 123, 255, 1)'
    },
    btnShow:{
      type: Boolean,
      default:false
    }
  },
  watch: {
    // show(val) {
    //   console.log(val, "val");
    //   if (val) {
    //     console.log(this.top, "0000");
    //   }
    // },
  },
  onReady() {
    // 状态栏高度
    let statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
    // 获取菜单按钮（右上角胶囊按钮）的布局位置信息
    const custom = wx.getMenuButtonBoundingClientRect();
    let navigationBarHeight =
      custom.height + (custom.top - statusBarHeight) * 2;

    let navHeight = navigationBarHeight + statusBarHeight;
    this.tipsStyle = {
      top: `${navHeight}px`,
    };
  },
  methods: {
    backFn() {
      this.$emit("backFn");
    },
    startMove(e) {
      this.startPoint = e.touches[0];
    },
    moveHide(e) {
      var moveLenght = this.startPoint.clientY - 20; //移动距离
      if (e.touches[e.touches.length - 1].clientY < moveLenght) {
        this.$emit("moveHide");
      }
      // this.$emit("moveHide");
    },
  },
};
</script>

<style lang="scss">
.status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 48rpx 20rpx 32rpx;
  background-color: #e6ebff;
  position: fixed;
  z-index: 8;
  top: 75rpx;
  width: 100%;
  box-sizing: border-box;
  transition: top 0.3s ease;
  &_left {
    display: flex;
    align-items: center;
    image {
      min-width: 32rpx;
      width: 32rpx;
      height: 32rpx;
    }
    text {
      font-weight: 600;
      font-size: 12px;
      padding-left: 16rpx;
      color: rgba(87, 123, 255, 1);
    }
  }
  &_right {
    display: flex;
    padding: 4rpx 0;
    font-size: 12px;
    line-height: 42rpx;
    background-color: rgba(87, 123, 255, 1);
    color: #fff;
    border-radius: 40rpx;
    min-width: 118rpx;
    width: 118rpx;
    text-align: center;
    display: inline-block;
  }
  
}
.top {
  top: 175rpx;
}
</style>
