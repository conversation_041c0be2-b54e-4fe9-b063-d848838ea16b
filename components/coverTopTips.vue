<template>
  <cover-view class="status" :style="styles" v-if="show">
    <cover-view class="status_left">
      <cover-image
        class="img"
        @click="hideFn"
        src="https://static.yujian.chat/images/webview/close.png"
      />
      <cover-view class="text">{{ desc }}</cover-view>
    </cover-view>
    <cover-view class="status_right" @click="backFn">{{ btnText }}</cover-view>
  </cover-view>
</template>

<script>
export default {
  data() {
    return {
      show: true,
    };
  },
  props: {
    desc: {
      type: String,
      default: "",
    },
    btnText: {
      type: String,
      default: "",
    },
    statusImg: {
      type: String,
      default: "",
    },
    styles: {
      type: String,
      default: () => {},
    },
  },
  methods: {
    backFn() {
      this.$emit("backFn");
    },
    hideFn() {
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.status {
  width: 100vw;
  position: fixed;
  top: 0;
  // left: 50%;
  z-index: 999999999;
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  padding: 20rpx 48rpx 20rpx 32rpx;
  background-color: #ccdaff;
  &_left {
    display: flex;
    align-items: center;
    .img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
      background-size: cover;
      background-color: center;
    }
    .text {
      font-size: 12px;
      color: rgba(87, 123, 255, 1);
    }
  }
  &_right {
    display: flex;
    padding: 4rpx 24rpx;
    font-size: 12px;
    line-height: 42rpx;
    background-color: rgba(87, 123, 255, 1);
    color: #fff;
    border-radius: 40rpx;
  }
}
</style>
