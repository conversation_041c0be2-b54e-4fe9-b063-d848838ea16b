<template>
  <u-tabbar
    :value="tabbarValue"
    @change="tabbarChange"
    customStyle="box-shadow: 0 -2rpx 24rpx 0 rgba(0, 0, 0, 0.1);border:0;z-index: 99;"
    :fixed="fixed"
    :placeholder="true"
    activeColor="#18C2A5"
    :safeAreaInsetBottom="true"
  >
    <u-tabbar-item text="全网职位">
      <view class="u-page__item__slot-icon_none" slot="active-icon">
        <image :src="setImg(`/images/hTabbar/onHome.png`)" />
      </view>
      <view class="u-page__item__slot-icon_none" slot="inactive-icon">
        <image :src="setImg(`/images/hTabbar/offHome.png`)" />
      </view>
    </u-tabbar-item>
    <u-tabbar-item text="一箭职达">
      <view class="u-page__item__slot-icon_none" slot="active-icon">
        <image :src="setImg(`/images/hTabbar/onMatch.png`)" />
      </view>
      <view class="u-page__item__slot-icon_none" slot="inactive-icon">
        <image :src="setImg(`/images/hTabbar/offMatch.png`)" />
      </view>
    </u-tabbar-item>
    <u-tabbar-item text="我的">
      <view class="u-page__item__slot-icon_none" slot="active-icon">
        <image :src="setImg(`/images/hTabbar/onUser.png`)" />
      </view>
      <view class="u-page__item__slot-icon_none" slot="inactive-icon">
        <image :src="setImg(`/images/hTabbar/offUser.png`)" />
      </view>
    </u-tabbar-item>
  </u-tabbar>
</template>

<script>
export default {
  name: "hTabbar",
  props: {
    tabbarValue: {
      type: Number,
      default: 0,
    },
    fixed: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isAndroid: true,
    };
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    tabbarChange(name) {
      this.$emit("update:tabbarValue", name);
      switch (name) {
        case 1:
          uni.redirectTo({
            url: "/pages/match/index",
          });
          break;
        case 2:
          uni.redirectTo({
            url: "/pages/user/index",
          });
          break;
        default:
          uni.redirectTo({
            url: "/pages/index/index",
          });
      }
    },
  },
  created() {
    console.log(this.systemInfo.platform, "this.systemInfo.platform");
    if (this.systemInfo.platform === "android") {
      this.isAndroid = true;
    } else {
      this.isAndroid = false;
    }
  },
};
</script>

<!-- <style lang="scss" scoped>
.icon_view {
  width: 71rpx;
  height: 71rpx;
}
.box {
  width: 71rpx;
  height: 71rpx;
}
.u-page__item__slot-icon {
  width: 71rpx;
  height: 71rpx;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
  image {
    position: absolute;
    bottom: 0rpx;
    width: 100%;
    height: 100%;
  }
  text {
    font-weight: 600;
    font-size: 14px;
    position: absolute;
    bottom: -40rpx;
    color: rgba(0, 0, 0, 0.9);
  }
}
.u-page__item__slot-icon_none {
  width: 71rpx;
  height: 71rpx;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
  image {
    position: absolute;
    bottom: 0rpx;
    width: 46rpx;
    height: 46rpx;
  }
  text {
    font-size: 14px;
    position: absolute;
    bottom: -40rpx;
  }
}
</style> -->
<style lang="scss" scoped>
.icon_view {
  width: 71rpx;
  height: 71rpx;
}
.box {
  width: 71rpx;
  height: 71rpx;
}
.u-page__item__slot-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
  image {
    width: 100%;
    height: 100%;
  }
}
.u-page__item__slot-icon_none {
  padding-top: 20rpx;
  height: 48rpx;
  image {
    width: 48rpx;
    height: 48rpx;
  }
  text {
    font-size: 14px;
    position: absolute;
    color: #000000e5;
    bottom: -32rpx;
  }
}
.inner {
  width: 100%;
  display: flex;
}
</style>
