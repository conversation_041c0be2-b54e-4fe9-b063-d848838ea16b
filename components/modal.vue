<template>
  <u-popup :show="show" @close="close" mode="center" bgColor="transparent">
    <view class="modal">
      <image
        :src="setImg(`/images/public/success.png`)"
        mode="widthFix"
        class="status-icon"
        v-if="icon === 'success'"
      ></image>
      <image
        :src="setImg(`/images/public/fail.png`)"
        mode="widthFix"
        class="status-icon"
        v-else-if="icon === 'fail'"
      ></image>
      <view class="modal-title">{{ title }}</view>
      <view class="modal-content">{{ content }}</view>
      <view class="modal-footer">
        <button class="btn flex-1" v-if="showCancel">{{ cancelText }}</button>
        <button class="btn flex-1" @click="handleConfirm">
          {{ confirmText }}
        </button>
      </view>
    </view>
  </u-popup>
</template>
<script>
export default {
  data() {
    return {
      content: "",
      title: "",
      show: false,
      showCancel: false,
      cancelText: "取消",
      confirmText: "我已知晓",
      icon: "success",
    };
  },
  created() {
    uni.$on("openModal", (data) => {
      this.show = true;
      this.title = data.title;
      this.content = data.content;
      this.showCancel = data.showCancel || false;
      this.cancelText = data.cancelText || "取消";
      this.confirmText = data.confirmText || "我已知晓";
      this.icon = data.icon || "success";
    });
  },
  beforeDestroy() {
    uni.$off("openModal");
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    close() {
      this.show = false;
    },
    handleConfirm() {
      this.show = false;
    },
  },
};
</script>
<style scoped lang="scss">
.modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  width: 600rpx;
  padding: 32rpx;
  border-radius: 32rpx;
  background-color: #fff;
  text-align: center;
}
.status-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 8rpx;
}
.modal-title {
  width: 100%;
  font-weight: bold;
  font-size: 32rpx;
}
.modal-content {
  width: 100%;
  font-size: 28rpx;
  color: #4e5969;
}
.modal-footer {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 32rpx;
  .btn {
    height: 74rpx;
  }
}
</style>
