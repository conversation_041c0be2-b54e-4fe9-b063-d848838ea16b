<template>
  <u-popup :show="show" round="19" @close="close" :safeAreaInsetBottom="false">
    <view class="content">
      <view class="m-item" @click="uploadFile('wx')">微信聊天文件上传</view>
      <view class="m-item" @click="uploadFile('phone')">手机文件上传</view>
      <view class="m-close" @click="close">取消</view>
    </view>
  </u-popup>
</template>
<script>
import global from "@/common/global";
export default {
  mixins: [],
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  data() {
    return {
      resumeShow: false,
    };
  },
  computed: {},
  methods: {
    close() {
      this.$emit("close");
    },
    uploadFile(type) {
      // #ifdef MP-WEIXIN
      if (type === "wx") {
        wx.chooseMessageFile({
          count: 1,
          type: "all",
          extension: [".pdf", ".jpg", ".png"],
          success: (res) => {
            const currFile = res.tempFiles[0];
            const fileName = currFile.name;
            if (!fileName.endsWith("pdf") && currFile.type !== "image") {
              setTimeout(() => {
                uni.showToast({
                  title: "请选择图片或者PDF文件",
                  icon: "none",
                  duration: 2000,
                });
              }, 1000);
              return;
            }
            setTimeout(() => {
              uni.showLoading({
                title: "上传解析中...",
              });
            }, 100);
            this.uniRequest.uploadResumeFile({
              filePath: currFile.path,
              fileName: currFile.name,
              formData: {},
              success: ({ data }) => {
                uni.hideLoading();
                uni.$u.toast("上传成功！");
                this.$emit("getDataOrUrl", {
                  fileName: currFile.name,
                  tempFileUrl: currFile.path,
                });
              },
              error: (data) => {
                uni.hideLoading();
                uni.$u.toast(data.msg);
              },
            });
          },
          fail: (error) => {
            console.error(error);
          },
        });
      } else {
        let localData = this.storageUtil.getItem("localData") || {};
        const url =
          global.STATIC_URL + "h5/load_h5/?token=" + localData.vuex_token;
        uni.navigateTo({
          url: `/pages_h5/webview/index?url=${encodeURIComponent(
            url
          )}&type=noPDF`,
        });
      }
      this.close();
      // #endif
    },
    handleFileChange(e) {
      const filePath = e.detail.tempFiles[0].path;
      console.log(filePath);
      // 处理文件路径
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  // background: #F5F6F7;
  text-align: center;
  border-radius: 38rpx 38rpx 0 0;
  overflow: hidden;
}
.m-item {
  background: #ffffff;
  // height: 96rpx;
  // line-height: 96rpx;
  padding: 44rpx 0;
  font-size: 31rpx;
  color: #333333;
}
.m-item:nth-child(1) {
  border-bottom: 2rpx solid #f5f6f7;
}
.m-close {
  height: 130rpx;
  line-height: 111rpx;
  background: #ffffff;
  border-top: 19rpx solid #f5f6f7;
  font-size: 31rpx;
  font-weight: 500;
  color: #333333;
}
</style>
