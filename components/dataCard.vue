<template>
  <view class="data-card">
    <CardTitle>
      <slot name="title"></slot>
    </CardTitle>
    <slot></slot>
  </view>
</template>
<script>
import CardTitle from "./cardTitle.vue";
export default {
  components: {
    CardTitle,
  },
  data() {
    return {
      showTips: false,
    };
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
  },
};
</script>
<style lang="scss">
.data-card {
  position: relative;
  padding: 76rpx 40rpx 40rpx;
  z-index: 2;
  background: linear-gradient(180deg, #eefefb 0%, #fff 100%);
  border-radius: 32rpx;
  box-shadow: 0 4px 20px #aeaeae1a;

  .title {
    position: absolute;
    top: -12rpx;
    left: -10rpx;
    display: flex;
    align-items: center;
    width: 252rpx;
    height: 70rpx;
    box-sizing: border-box;
    padding-left: 20rpx;
    padding-bottom: 16rpx;
    background-size: 100% 100%;
    font-size: 28rpx;
    color: #fff;
    text-shadow: 0 1px 4px #0f8a78;
    font-weight: bold;

    .icon {
      width: 28rpx;
      height: 28rpx;
      padding: 10rpx; /* 增加内边距扩大可点击区域 */
    }
  }
}
</style>
