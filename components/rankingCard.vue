<template>
  <view :class="['ranking-card', type]">
    <image
      class="ranking-card-bg"
      :src="setImg(`/images/public/hot_${type}_icon.png?v=3`)"
      mode="scaleToFill"
    ></image>
    <view class="ranking-card-content">
      <view class="title">
        <image
          v-if="color == 'org'"
          class="title-bg"
          :src="setImg(`/images/public/vector4.png`)"
          mode="widthFix"
        >
        </image>
        <image
          v-else
          class="title-bg"
          :src="setImg(`/images/public/vector3.png`)"
          mode="widthFix"
        ></image>
        <text>{{ title }}</text>
        <text
          :class="['en', fontLoaded && 'jinbu']"
          :style="{ color: color === 'org' ? '#EDD2CC' : '#D0DBED' }"
          >{{ enTitle }}</text
        >
      </view>
      <slot></slot>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    enTitle: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      fontLoaded: false,
    };
  },
  created() {
    this.loadJbFont();
  },
  methods: {
    loadJbFont() {
      const app = getApp();
      if (app.globalData.jbFontLoaded) {
        this.fontLoaded = true;
        return;
      }
      if (!app.globalData.fontPromise) {
        app.globalData.fontPromise = new Promise((resolve, reject) => {
          uni.loadFontFace({
            family: "JinBu",
            source: `url("${this.staticBaseUrl}/images/DingTalk.ttf")`,
            success: (res) => {
              app.globalData.jbFontLoaded = true;
              this.fontLoaded = true;
              resolve();
            },
            fail: (err) => {
              app.globalData.fontLoaded = true;
              resolve();
            },
          });
        });
      }
      app.globalData.fontPromise.then(() => {
        this.fontLoaded = true;
      });
    },
    setImg(url) {
      return this.staticBaseUrl + url;
    },
  },
};
</script>
<style lang="scss" scoped>
.ranking-card {
  position: relative;
  // min-height: 718rpx;
  // margin-bottom: 40rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 32rpx;

  .ranking-card-bg {
    position: absolute;
    top: -32rpx;
    left: 0;
    width: 100%;
    height: 718rpx;
    z-index: 0;
  }
  .ranking-card-content {
    position: relative;
    z-index: 1;
    padding: 28rpx 40rpx 40rpx;
    font-size: 32rpx;
    color: #1d2129;
    box-sizing: border-box;
    .title {
      display: flex;
      align-items: center;
      gap: 10rpx;
      font-weight: bold;
      position: relative;
      margin-bottom: 40rpx;
      font-size: 32rpx;

      .title-bg {
        width: 120rpx;
        position: absolute;
        top: 30rpx;
        z-index: 0;
      }
      text {
        position: relative;
        z-index: 1;
        &.en {
          font-style: italic;
          font-weight: normal;
        }
      }
    }
  }
}
</style>
