<template>
  <view class="ranking-list">
    <view :class="['rank-item']" v-for="(i, idx) in list" :key="idx">
      <view :class="['rank-cunt', `s${idx + 1}`]">{{ idx + 1 }}</view>
      <view class="name">
        <view class="logo" v-if="type == 'companies'">
          <u-avatar
            :src="i.logoUrl"
            :fontSize="10"
            size="40rpx"
            mode="aspectFit"
            :text="getLogoName(i)"
            randomBgColor
          ></u-avatar>
        </view>

        <text class="name-text">{{ i.name }}</text>
      </view>
      <text class="data-right" v-if="['industries', 'companies'].includes(type)"
        >在招{{ i.jobCount }}个岗位</text
      >
      <text class="data-right" v-else>总岗位：{{ i.jobCount }}</text>
      <view :class="['item-bg', `o${idx}`]"></view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "",
    },
  },
  methods: {
    getLogoName(item) {
      if (item.logoUrl) {
        return "";
      }
      let name = item.name.replace(
        /(中国|北京|上海|广州|深圳|厦门|杭州|苏州|南京|天津|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)(省|市)?/g,
        ""
      );
      return name.substr(0, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  .rank-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 72rpx;
    padding: 0 30rpx;
    gap: 20rpx;
    box-sizing: border-box;

    .item-bg {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 0;
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
      transform: skewX(-10deg);

      &.o0 {
        background: linear-gradient(
          90deg,
          #ffefb2 1.29%,
          rgba(255, 255, 255, 0) 79.23%
        );
      }

      &.o1 {
        background: linear-gradient(
          90deg,
          #dbe2ea 0%,
          rgba(237, 241, 248, 0) 80%
        );
      }

      &.o2 {
        background: linear-gradient(
          90deg,
          #f9ebdb 0%,
          rgba(254, 244, 235, 0) 80%
        );
      }

      &.o3,
      &.o4 {
        background: linear-gradient(
          90deg,
          #f7f8fa 0%,
          rgba(247, 248, 250, 0) 80%
        );
      }
    }

    .rank-cunt {
      position: relative;
      z-index: 1;
      flex-shrink: 0;
      font-size: 32rpx;
      font-weight: 600;
      color: #4e5969;
      -webkit-background-clip: text;
      background-clip: text;
      display: inline-block;
      &.s1 {
        background-image: linear-gradient(to bottom, #ffc000 60%, #f8a617);
      }
      &.s2 {
        background-image: linear-gradient(to bottom, #b6c5d2 60%, #7a8790);
      }
      &.s3 {
        background-image: linear-gradient(to bottom, #dcb79c 60%, #977056);
      }
      &.s1,
      &.s2,
      &.s3 {
        color: transparent;
      }
      &.s1,
      &.s2,
      &.s3,
      &.s4,
      &.s5 {
        transform: skewX(-10deg);
        font-weight: 900;
      }
    }
    .name {
      display: flex;
      align-items: center;
      gap: 8rpx;
      flex: 1;
      overflow: hidden;
      position: relative;
      z-index: 1;
      padding-right: 30rpx;
      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40rpx;
        height: 40rpx;
        background: #fff;
        border-radius: 50%;
      }
      .name-text {
        flex: 1;
        width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 28rpx;
        color: #1d2129;
        font-weight: 500;
      }
    }
    .data-right {
      position: relative;
      z-index: 1;
      font-size: 24rpx;
      color: #4e5969;
      white-space: normal;
    }
  }
}
</style>
