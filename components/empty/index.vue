<template>
  <view class="emptyBox">
    <image
      src="https://static.yujian.chat/images/empty/empty_icon.png"
      mode="widthFix"
    />
    <view class="text" :style="{color:textColor}">{{ text }}</view>
    <view class="text" v-if="desc">{{ desc }}</view>
  </view>
</template>
<script>
export default {
  name: "Empty",
  props: {
    text: {
      type: String,
      default: "暂无数据",
    },
    desc: {
      type: String,
      default: "",
    },
    textColor: {
      type: String,
      default: "#c0c4cc",
    }
  },
};
</script>
<style lang="scss" scoped>
.emptyBox {
  min-width: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 28%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  image {
    width: 180rpx;
    height: 180rpx;
  }
  .text {
    display: inline-block;
    width: 400rpx;
    color: #c0c4cc;
    font-size: 14px;
    margin-top: 20rpx;
  }
}
</style>
