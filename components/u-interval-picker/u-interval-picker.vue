<template>
  <view>
    <u-picker
      :show="show"
      ref="yearPicker"
      :columns="yearColumns"
      @confirm="onConfirm"
      @cancel="onCancel"
      @change="onYearChange"
    ></u-picker>
  </view>
</template>

<script>
import { getYearRange } from '@/utils';
import dayjs from "dayjs";
const diff = 20;
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      startYear: 2000, // 设置开始年份的初始值
      endYear: dayjs().year(), // 设置结束年份的初始值
      yearColumns: [
        getYearRange(dayjs().subtract(diff, 'year').year(), dayjs().year()), // 第一列为开始年份的选择范围
        getYearRange(dayjs().year(), dayjs().add(diff, 'year').year()), // 第二列为结束年份的选择范围
      ],
    };
  },
  watch: {
  
  },
  methods: {
    onYearChange(e) {
      const { columnIndex, value, picker = this.$refs.yearPicker } = e;
      if (columnIndex === 0) {
        // 当第一列值发生变化时，变化第二列对应的选项
        this.startYear = parseInt(value[0]);
        const endYearRange = getYearRange(this.startYear, this.startYear + diff);
        picker.setColumnValues(1, endYearRange);
        if (this.endYear < this.startYear) {
          this.endYear = this.startYear;
        }
      }
    },
    onConfirm(e) {
      console.log(e)
      this.$emit("confirm", e);
    },
    onCancel(){
      this.$emit("cancel");
    }
  },
};
</script>