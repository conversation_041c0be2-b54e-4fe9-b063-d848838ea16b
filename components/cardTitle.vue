<template>
  <view
    :class="['card-title', size]"
    :style="{
      backgroundImage:
        size === 'sm'
          ? `url(${setImg(`/images/public/title-bg-sm.png`)})`
          : `url(${setImg(`/images/public/title-bg.png?v=2`)})`,
      ...style,
    }"
  >
    <slot></slot>
    <image
      class="shadow"
      :src="setImg(`/images/public/title-shadow.png`)"
      mode="scaleToFill"
    ></image>
  </view>
</template>
<script>
export default {
  props: {
    size: {
      type: String,
      default: "",
    },
    style: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
  },
};
</script>
<style lang="scss" scoped>
.card-title {
  position: absolute;
  top: -12rpx;
  left: -10rpx;
  height: 56rpx;
  width: 220rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #fff;
  text-shadow: 0 1px 4px #0f8a78;
  font-weight: bold;
  background-size: 100% 100%;
  white-space: nowrap;
  .shadow {
    position: absolute;
    left: 0;
    top: 56rpx;
    width: 10rpx;
    height: 14rpx;
  }
  &.sm {
    width: 180rpx;
  }
}
</style>
