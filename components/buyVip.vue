<template>
  <u-popup
    :show="vipShow"
    :round="20"
    bgColor="#3B3B3B"
    :closeable="true"
    @close="closePop"
  >
    <view class="vipPopup">
      <view class="pop_top">
        <image :src="setImg('images/icon_vipCard.png')" mode="widthFix"></image>
        <view class="ptit">VIP权限（7天）</view>
      </view>
      <image
        :src="setImg('images/icon_vipCard.png')"
        class="bgimg"
        mode="widthFix"
      ></image>
      <view class="pop_box">
        <view class="btit">权益介绍</view>
        <view class="bcon">
          <view class="bitem">
            <view class="b1">权益名称</view>
            <view class="b2">普通权益</view>
            <view class="b3">
              <image
                :src="setImg('images/icon_vip.png')"
                mode="widthFix"
              ></image>
              <text>VIP权益</text>
            </view>
          </view>
          <view class="bitem">
            <view class="b1">ְ职位匹配数</view>
            <view class="b2">3次/天</view>
            <view class="b3">
              <text>5次/天</text>
            </view>
          </view>
          <view class="bitem">
            <view class="b1">职业测评</view>
            <view class="b2">基础测评</view>
            <view class="b3">
              <text>深度测评</text>
            </view>
          </view>
          <view class="bitem">
            <view class="b1">行业分析</view>
            <view class="b2">
              <text></text>
            </view>
            <view class="b3">
              <text>实时查看</text>
            </view>
          </view>
        </view>
        <view class="bvip"></view>
      </view>
      <view class="pop_bottom">
        <view class="bt" @click="buyVip">立即兑换并激活</view>
        <view class="bc">
          <image :src="setImg('images/coin.png')" mode="widthFix"></image>
          <text>{{ point || 0 }}</text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    point: {
      type: Number,
      default: 0,
    },
    vipShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  onReady() {},
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    closePop() {
      this.$emit("closePop");
    },
    buyVip() {
      uni.showModal({
        showCancel: false,
        title: "提醒",
        content: "功能正在开发中,敬请期待!",
      });
      // this.$emit('buyVip')
    },
  },
};
</script>

<style lang="scss">
.vipPopup {
  height: auto;
  // overflow:hidden;
  // background:#3B3B3B;
  padding: 0 30rpx 30rpx 30rpx;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  position: relative;

  .bgimg {
    width: 280rpx;
    position: absolute;
    right: 0;
    top: -40rpx;
    opacity: 0.1;
  }

  .pop_top {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    image {
      width: 160rpx;
      margin: -40rpx 20rpx 0 0;
    }

    .ptit {
      font-size: 40rpx;
      background: -webkit-linear-gradient(
        rgba(255, 239, 197, 1),
        rgba(223, 195, 97, 1)
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .pop_box {
    height: auto;
    overflow: hidden;
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin: 0rpx 0 40rpx 0;
    position: relative;

    .btit {
      border-left: 10rpx #ffd055 solid;
      font-size: 30rpx;
      color: #333;
      padding-left: 20rpx;
    }

    .bcon {
      position: relative;
      z-index: 1;
      margin: 20rpx 0;
    }

    .bitem {
      height: 80rpx;
      line-height: 80rpx;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      color: #333;

      .b1 {
        width: 30%;
        padding-left: 20rpx;
        box-sizing: border-box;
      }

      .b2 {
        width: 33%;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;

        text {
          width: 40rpx;
          height: 10rpx;
          background: #e2e2e2;
          border-radius: 10rpx;
        }
      }

      .b3 {
        width: 33%;
        display: flex;
        justify-content: center;
        align-items: center;

        image {
          width: 40rpx;
          margin-right: 5rpx;
        }

        text {
          background: -webkit-linear-gradient(top, #b99763, #9c5b36);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      &:nth-child(odd) {
        background: rgba($color: #999, $alpha: 0.1);
      }
    }

    .bvip {
      width: 30%;
      height: 320rpx;
      overflow: hidden;
      border-radius: 20rpx;
      background: -webkit-linear-gradient(
        top,
        rgba(255, 238, 194, 1),
        rgba(255, 223, 141, 1)
      );
      position: absolute;
      top: 90rpx;
      right: 30rpx;
    }
  }

  .pop_bottom {
    height: 80rpx;
    overflow: hidden;
    text-align: center;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: -webkit-linear-gradient(top, #ffeec2, #ffdf8d);
    color: #a46b42;

    .bt {
      font-size: 30rpx;
    }

    .bc {
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 25rpx;
        margin-right: 10rpx;
      }

      text {
        font-size: 20rpx;
      }
    }
  }
}
</style>
