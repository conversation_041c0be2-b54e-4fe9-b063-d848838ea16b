<template>
  <view :class="['job-card', inReport ? 'in-report' : '']" @click="gotoDetail">
    <view class="job-card-header">
      <view class="flex items-center justify-between">
        <view class="flex items-center gap-1 flex-1 overflow-hidden">
          <view class="job-name ellipsis">{{ item.posName || "--" }}</view>
          <template v-if="inReport || scene == 'ExactMatch'">
            <text class="tag-plain org" v-if="item.recommendType === 2">
              挑战型
            </text>
            <text class="tag-plain blue" v-else-if="item.recommendType === 4">
              稳健型
            </text>
            <text class="tag-plain primary" v-if="item.recommendType === 6">
              保底型
            </text>
          </template>
          <text class="tag-plain"></text>
        </view>
        <view class="job-salary ellipsis">{{ item.salary || "面议" }}</view>
      </view>
      <view class="job-desc mt-1" v-if="!inReport">
        {{ item.internTag }} · 发布于{{ item.pubDate }}
      </view>
    </view>
    <view class="flex flex-wrap gap-1" v-if="!inReport">
      <text class="tag-plain" v-for="item in item.tags" :key="item">{{
        item
      }}</text>
    </view>
    <view class="job-company">
      <view class="company-logo">
        <u-avatar
          :src="item.logoUrl || ''"
          fontSize="14"
          mode="aspectFit"
          :text="logoName(item)"
          randomBgColor
          size="80rpx"
        ></u-avatar>
      </view>
      <!-- <image class="company-logo" :src="setImg(`/images/avatar.png`)" /> -->
      <view class="flex-1">
        <view class="flex items-center">
          <view class="company-name ellipsis">
            {{ item.companyName || "--" }}
          </view>
          <view class="job-desc company-loc ellipsis">{{ item.location }}</view>
        </view>
        <view class="flex flex-wrap gap-1 mt-1" v-if="companyTag.length > 0">
          <text class="tag-plain outline" v-for="item in companyTag">
            {{ item }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    inReport: {
      type: Boolean,
      default: false,
    },
    scene: {
      type: String,
      default: "",
    },
  },
  computed: {
    companyTag() {
      if (this.item.companyTag) {
        return this.item.companyTag.split(",");
      }
      return [];
    },
  },
  methods: {
    setImg(url) {
      return this.staticBaseUrl + url;
    },
    gotoDetail() {
      let url = `/pages_user/job/jobDetail?id=${this.item.id}`;
      if (this.scene) {
        url += `&scene=${this.scene}`;
      }
      uni.navigateTo({
        url,
      });
    },
    logoName(item) {
      if (item.logoUrl || !item.companyName) {
        return "";
      }
      let name = item.companyName.replace(
        /(中国|北京|上海|广州|深圳|厦门|杭州|苏州|南京|天津|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)(省|市)?/g,
        ""
      );
      return name.substr(0, 2);
    },
  },
};
</script>
<style lang="scss" scoped>
.job-card {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  border-radius: 32rpx;
  padding: 32rpx;
  box-sizing: border-box;
  background: #fff;

  .job-name {
    flex: 1;
    max-width: max-content;
    font-size: 32rpx;
    font-weight: 500;
  }
  .job-salary {
    flex-shrink: 0;
    max-width: 300rpx;
    color: #ff7d00;
    font-size: 32rpx;
    font-weight: 500;
  }
  .job-desc {
    font-size: 24rpx;
    color: #86909c;
    line-height: 36rpx;
  }
  .job-company {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 16rpx;
    .flex-1 {
      width: 50%;
      overflow: hidden;
    }
    .company-logo {
      flex-shrink: 0;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      border: 4rpx solid #fff;
    }
    .company-name {
      width: 50%;
      flex: 1;
      font-size: 28rpx;
    }
    .company-loc {
      max-width: 50%;
    }
  }
  &.in-report {
    border: 1px solid #e5e6eb;
    .job-company {
      .tag-plain {
        border-color: #94bfff;
        color: #165dff;
      }
    }
  }
}
</style>
