<script>
import { mapMutations } from "vuex";

export default {
  globalData: {
    jbFontLoaded: false,
    jbFontPromise: null,
  },
  methods: {
    ...mapMutations(["SET_DEVICE"]),
    autoUpdate: function () {
      let self = this;
      // 获取小程序更新机制兼容
      if (uni.canIUse("getUpdateManager")) {
        const updateManager = uni.getUpdateManager();
        //1. 检查小程序是否有新版本发布
        updateManager.onCheckForUpdate(function (res) {
          // 请求完新版本信息的回调
          if (res.hasUpdate) {
            //检测到新版本，需要更新，给出提示
            uni.showModal({
              title: "更新提示",
              content: "检测到新版本，是否下载新版本并重启小程序？",
              success: function (res) {
                if (res.confirm) {
                  //2. 用户确定下载更新小程序，小程序下载及更新静默进行
                  self.downLoadAndUpdate(updateManager);
                } else if (res.cancel) {
                  //用户点击取消按钮的处理，如果需要强制更新，则给出二次弹窗，如果不需要，则这里的代码都可以删掉了
                  uni.showModal({
                    title: "温馨提示~",
                    content:
                      "本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~",
                    showCancel: false, //隐藏取消按钮
                    confirmText: "确定更新", //只保留确定更新按钮
                    success: function (res) {
                      if (res.confirm) {
                        //下载新版本，并重新应用
                        self.downLoadAndUpdate(updateManager);
                      }
                    },
                  });
                }
              },
            });
          }
        });
      } else {
        // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
        uni.showModal({
          title: "提示",
          content:
            "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。",
        });
      }
    },
    /**
     * 下载小程序新版本并重启应用
     */
    downLoadAndUpdate: function (updateManager) {
      uni.showLoading();
      //静默下载更新小程序新版本
      updateManager.onUpdateReady(function () {
        uni.hideLoading();
        //新的版本已经下载好，调用 applyUpdate 应用新版本并重启
        updateManager.applyUpdate();
      });
      updateManager.onUpdateFailed(function () {
        // 新的版本下载失败
        uni.showModal({
          title: "已经有新版本了哟~",
          content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~",
        });
      });
    },
  },
  onLaunch: function (options) {
    // 版本更新检测
    this.autoUpdate();
    // 加载设备信息
    uni.getSystemInfo({
      success: (res) => {
        this.SET_DEVICE(res.brand + " " + res.model);
      },
    });
  },
  onShow() {
    // console.log('App.onShow');
    // 初始化websocket连接
    this.$nextTick(() => {
      this.$store.dispatch("initWS");
    });
  },
  onHide() {
    // console.log('App.onHide');
    this.$store.dispatch("closeSocket");
  },
};
</script>

<style lang="scss">
/*每个页面公共css */
@import "@/uni_modules/uview-ui/index.scss";
@import "./static/style/global.scss";

* {
  box-sizing: border-box;
}

image {
  height: auto;
}

button {
  margin: 0;
  padding: 0;

  &::after {
    display: none;
  }
}

.footbar {
  width: 100%;
  height: auto;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 32rpx;
  padding: 20rpx 30rpx 50rpx 30rpx;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 0 -5rpx 5rpx rgba($color: #000, $alpha: 0.06);
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;

  button {
    height: 80rpx;
    line-height: 80rpx;
    border: 0;
    border-radius: 40rpx;

    &::after {
      display: none;
    }
  }

  .fbtn0 {
    width: 48%;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e3fffb;
    background: #e3fffb;
    font-size: 30rpx;
    color: $uv-primary;
    box-sizing: border-box;
    &.outline {
      background: transparent;
      border-color: $uv-primary;
    }
  }

  .fbtn {
    width: 100%;
    background: #18c2a5;
    font-size: 30rpx;
    color: #fff;
  }

  .fbtn1 {
    width: 48%;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #18c2a5;
    font-size: 30rpx;
    color: #fff;
    box-sizing: border-box;
  }
}

.floatPanel {
  position: fixed;
  right: 20rpx;
  bottom: 120rpx;
  z-index: 10;

  .floatBtn {
    width: 120rpx;
    height: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 100%;
    }

    text {
      text-align: center;
    }

    &.btn1 {
      // font-size: 30rpx;
      // color: #fff;
      // line-height: 1.2;
      background: -webkit-linear-gradient(left, #344399, #495abd);
    }

    &.btn2 {
      background: -webkit-linear-gradient(
        left,
        rgb(210, 180, 44),
        rgb(238, 208, 69)
      );
    }
  }
}

.footspace {
  height: 100rpx;
  overflow: hidden;
}
.industry-list {
  ::v-deep .u-collapse {
    padding: 0 30rpx;
    .u-cell__body {
      padding: 20rpx 0;
    }
    .u-collapse-item__content__text {
      padding: 10rpx 0 30rpx;
    }
    .u-collapse-item {
      border-bottom: 1px solid #dbdbdb;
      &:last-child {
        border: none;
      }
    }
  }
}

uni-swiper .uni-swiper-dot {
  width: 15rpx;
  height: 15rpx;
}

uni-swiper .uni-swiper-dot-active {
  width: 19rpx;
  height: 19rpx;
  transform: translateY(1px);
}

wx-swiper .wx-swiper-dot {
  width: 15rpx;
  height: 15rpx;
}

wx-swiper .wx-swiper-dot-active {
  width: 19rpx;
  height: 19rpx;
  transform: translateY(1px);
}
</style>
