* {
  margin: 0;
  padding: 0;
}
.container {
  width: 100vw;
  height: 100vh;
}
.header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #000000bf;
  margin: 31px 0 auto;
}
.header span {
  font-weight: 600;
}
img {
  width: 48px;
  height: 48px;
  margin-bottom: 9px;
}
.content {
  border-radius: 8px;
  width: 90%;
  padding: 12px 16px;
  background-color: #0000000f;
  margin: 32px auto;
  box-sizing: border-box;
}
.content p {
  font-size: 14px;
  font-weight: 600;
  color: #000000e5;
}
.content span {
  font-size: 14px;
  font-weight: 400;
  color: #000000e5;
}
.footer {
  width: 203px;
  height: 53px;
  background: linear-gradient(to right, #59bbfa, #889ff8, #b696f4);
  border-radius: 32px;
  margin: 0 auto;
  color: #fff;
  line-height: 53px;
  text-align: center;
  position: relative;
}
#fileInput {
  position: absolute;
  width: 100%;
  height: 53px;
  left: 0;
  opacity: 0;
}
