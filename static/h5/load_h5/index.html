<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>手机文件上传</title>
    <!-- 公众号 JSSDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.2.js"></script>
    <!--uni-app-->
    <script src="https://yjaioss.kongxuan.com/js/uni.webview.1.5.5.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="./index.css" />
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img src="./image/file.png" />
        <p>从手机上传中选取</p>
        <span> 「 使用说明 」</span>
      </div>
      <div class="content">
        <p>
          点击下方「上传文件」<span>（支持pdf、jpg、png格式，大小不超过20M）</span>，选择手机中的简历文件进行上传
        </p>
      </div>
      <div class="footer">上传文件<input type="file" id="fileInput" accept=".pdf, .jpg, .png" /></div>
    </div>
    <script>
      const token = window.location.search.split("=")[1];
      const CLIENT_ID = "eab5997c5484c03ade20474a2f73a5db";
      let BASE_URL = "";
      if (window.location.href.includes("testapi")) {
        BASE_URL = "https://testapi.yujian.chat/app/api";
      } else {
        BASE_URL = "https://miniapi.yujian.chat/api";
      }
      // 设置请求头
      const config = {
        headers: {
          Authorization: "Bearer " + token || "",
          clientid: CLIENT_ID,
        },
      };

      const fileInput = document.getElementById("fileInput");

      fileInput.addEventListener("change", function (e) {
        const file = e.target.files[0];
        if (!file) {
          return;
        }
        if (file && file.size > 1024 * 1024 * 20) {
          // 20MB限制
          uni.$u.toast("文件大小不能超过20MB！");
          return;
        }
        // 创建FormData对象
        const formData = new FormData();
        formData.append("file", file);
        axios
          .post(
            BASE_URL + "/member/resume/attach?fileName=" + file.name,
            formData,
            config
          )
          .then((response) => {
            uni.navigateBack({
              delta: 1,
              success: () => {
                uni.$emit("updateResume");
              },
            });
          })
          .catch((error) => {
            console.error(error);
          });
      });
    </script>
  </body>
</html>
