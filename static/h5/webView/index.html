<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>简历预览</title>
    <!-- 公众号 JSSDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.2.js"></script>
    <!--uni-app-->
    <script src="https://yjaioss.kongxuan.com/js/uni.webview.1.5.5.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="./css/pdfh5.css" />
    <link rel="stylesheet" href="./css/index.css" />
    <script src="js/pdf.js" type="text/javascript" charset="utf-8"></script>
    <script
      src="js/pdf.worker.js"
      type="text/javascript"
      charset="utf-8"
    ></script>
    <script
      src="js/jquery-3.6.0.min.js"
      type="text/javascript"
      charset="utf-8"
    ></script>
    <script src="js/pdfh5.js" type="text/javascript" charset="utf-8"></script>
  </head>
  <body>
    <div style="padding: 24px 0;">
    </div>
    <div class="container" id="demo">
      <div class="footer_temp">
        <div class="button_temp button_temp_default" onclick="adjustTempFn()">
          调整模版
        </div>
        <div class="button_temp" onclick="useTempFn()">使用模版</div>
      </div>
    </div>
    <script>
      const searchParams = new URLSearchParams(
        decodeURIComponent(window.location.search)
      );
      const token =
        searchParams.get("token") ||
        window.location.search.split("=")[1].split("&")[0];
      const type = searchParams.get("type") || "";
      const resultId = searchParams.get("resultId") || "";
      const CLIENT_ID =
        searchParams.get("clientid") || "eab5997c5484c03ade20474a2f73a5db";
      let templateId = searchParams.get("id") || "";
      const Url = searchParams.get("url") || "";
      let pdfh5 = "";
      let pdfName = "";
      let BASE_URL = "";
      let TipsShow = true;
      let pdfUrl = "";

      if (
        window.location.href.includes("testapi") ||
        window.location.href.includes("127.0.0.1") ||
        window.location.href.includes("localhost")
      ) {
        BASE_URL = "https://testapi.yujian.chat/app/api";
      } else {
        BASE_URL = "https://miniapi.yujian.chat/api";
      }
      // 设置请求头
      const config = {
        headers: {
          Authorization: "Bearer " + token || "",
          clientid: CLIENT_ID,
        },
      };
      // 获取路径
      async function getData() {
        if (templateId) {
          pdfName = "简历模版";
          pdfUrl = Url;
          pdfh5 = new Pdfh5("#demo", {
            pdfurl: Url,
          });
          return;
        }
        if (type === "preview") {
          // 预览我的在线简历
          const PdfResult = await axios.post(
            BASE_URL + "/member/resume/generate",
            {},
            config
          );
          if (
            PdfResult.status === 200 &&
            PdfResult.data.code === 200 &&
            PdfResult.data.data?.url
          ) {
            pdfName = PdfResult.data.data.name;
            pdfUrl = PdfResult.data.data.url;
            pdfh5 = new Pdfh5("#demo", {
              pdfurl: PdfResult.data.data.url,
            });
          }
        } else {
          let result = "";
          if (resultId) {
            result = await axios.get(
              BASE_URL + `/member/result/${resultId}`,
              config
            );
            if (result && result.data.code == 200 && result.status === 200) {
              pdfName = "简历.pdf";
              pdfUrl = result.data.data.url;
              pdfh5 = new Pdfh5("#demo", {
                pdfurl: result.data.data.url,
              });
            }
          } else {
            result = await axios.get(
              BASE_URL + "/member/resume/latest",
              config
            );
            if (result && result.data.code == 200 && result.data.data.upload) {
              pdfName = result.data.data.upload.name;
              pdfUrl = result.data.data.upload.url;
              pdfh5 = new Pdfh5("#demo", {
                pdfurl: result.data.data.upload.url,
              });
            }
          }
        }
      }
      // 显示底部按钮
      function hideBottomFn() {
        if (type === "download") {
          $(".footer").css("display", "block");
        } else if (type === "temp") {
          $(".footer_temp").css("display", "flex");
        } else if (
          type !== "preview" &&
          type !== "temp" &&
          type !== "download" &&
          type != "noPDF"
        ) {
          $(".footerSubmit").css("display", "block");
        }
      }
      // 下载PDF
      function downloadFn() {
        const a = document.createElement('a');
            a.href = pdfUrl;
            a.download = pdfName+'.pdf';
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            // console.log(a)
            // document.body.removeChild(a);
            // pdfh5.download(pdfName);
            // uni.downloadFile({
              //   // 示例 url，并非真实存在
              //   url: pdfUrl,
              //   success: function (res) {
              //     const filePath = res.tempFilePath;
              //     uni.openDocument({
              //       filePath: filePath,
              //       showMenu: true,
              //       success: function (res) {
              //         console.log('打开文档成功');
              //       }
              //     });
              //   },
              // });
      }
      // 调整模版
      function adjustTempFn() {
        uni.navigateTo({
          url: `/pages_user/resume/adjustTemplate?url=${pdfUrl}&type=${type}&id=${templateId}`,
        });
      }
      function submitSure() {
        uni.navigateBack({
          delta: 1,
        });
      }
      //使用模版
      async function useTempFn() {
        const params = {
          templateId: templateId * 1,
        };
        const result = await axios.post(
          BASE_URL + "/member/resume/create",
          params,
          config
        );
        if (result.status === 200) {
          uni.redirectTo({
            url: `/pages_user/resume/index?url=${pdfUrl}&type=${type}&id=${templateId}&navTo=webView`,
          });
        }
      }
      function hideFn() {
        const elStyle = $(".status").css("display");
        if (elStyle === "none") {
          $(".status").css("display", "flex");
        } else {
          $(".status").css("display", "none");
        }
      }
      // 初始化
      function init() {
        getData();
        hideBottomFn();
      }
      init();
    </script>
  </body>
</html>
