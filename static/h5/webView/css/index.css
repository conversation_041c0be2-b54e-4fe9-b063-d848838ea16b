* {
  padding: 0;
  margin: 0;
}
.container {
  width: 100vw;
  height: 100vh;
}

.footer,
.footerSubmit {
  position: fixed;
  display: none;
  bottom: 0;
  left: 50%;
  width: 100%;
  height: 20%;
  z-index: 9;
  transform: translateX(-50%);
}
.footer_temp {
  position: fixed;
  bottom: 10%;
  display: none;
  /* display: flex; */
  width: 100%;
  z-index: 9;
}
.button_temp {
  height: 53px;
  line-height: 53px;
  text-align: center;
  width: 40%;
  border-radius: 32px;
  color: #ffffff;
  margin: 10px auto;
  background-color: #18C2A5;
  z-index: 99999;
}
.button_temp_default {
  box-sizing: border-box;
  border: 1px solid #18C2A5;
  color: #18C2A5;
  background-color: #ffffff;
}
.button_box {
  height: 53px;
  line-height: 53px;
  text-align: center;
  width: 203px;
  text-align: center;
  border-radius: 32px;
  color: #ffffff;
  margin: 10px auto;
  background-color: #18C2A5;
  z-index: 9999;
}
.footer_text {
  font-size: 12px;
  color: rgba(153, 153, 153, 1);
  padding-top: 8px;
  text-align: center;
  line-height: 18px;
}

.status {
  width: 100vw;
  position: fixed;
  top: 0;
  z-index: 10;
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  padding: 10px 24px 10px 16px;
  background-color: #ccdaff;
}
.status_left {
  display: flex;
  align-items: center;
}
.status_left .img {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-size: cover;
  background-color: center;
}
.status_left .text {
  font-size: 12px;
  color: rgba(87, 123, 255, 1);
}
.status_right {
  display: flex;
  padding: 2px 12px;
  font-size: 12px;
  line-height: 21px;
  background-color: rgba(87, 123, 255, 1);
  color: #fff;
  border-radius: 20px;
}
