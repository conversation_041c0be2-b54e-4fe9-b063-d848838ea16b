@font-face {
  font-family: "iconfont"; /* Project id 4957850 */
  src: url("data:font/ttf;charset=utf-8;base64,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")
    format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 32rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  &.icon-close:before {
    content: "\e71b";
  }
  &.icon-add:before {
    content: "\e6a7";
  }
  &.icon-check:before {
    content: "\e9fb";
  }
  &.icon-tri:before {
    content: "\e620";
  }
}
page {
  background: #f5f6f7;
  color: #1d2129;
  line-height: 1.4;
}
.flex {
  display: flex;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.flex-1 {
  flex: 1;
}
.w-full {
  width: 100%;
}
.gap-1 {
  gap: 8rpx;
}
.gap-2 {
  gap: 16rpx;
}
.gap-3 {
  gap: 24rpx;
}
.gap-4 {
  gap: 32rpx;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.p-4 {
  padding: 32rpx;
}
.mt-1 {
  margin-top: 8rpx;
}
.mt-2 {
  margin-top: 16rpx;
}
.mt-3 {
  margin-top: 24rpx;
}
.mt-4 {
  margin-top: 32rpx;
}
.mb-4 {
  margin-bottom: 32rpx;
}
.mt-5 {
  margin-top: 40rpx;
}
.overflow-hidden {
  overflow: hidden;
}
.text-base {
  font-size: 32rpx !important;
}
.text-sm {
  font-size: 28rpx !important;
}
.text-primary {
  color: $uv-primary;
}
.text-secondary {
  color: $uv-secondary;
}
.font-bold {
  font-weight: bold;
}
.placeholder {
  color: #bdbdbd;
  font-size: 28rpx;
  line-height: 44rpx;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
page {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    height: 64rpx;
    padding: 0 32rpx;
    border: 2rpx solid #e5e6eb;
    box-sizing: border-box;
    color: #1d2129;
    background: transparent;
    border-radius: 200rpx;
    &.primary {
      background: $uv-primary;
      color: #fff;
      border-color: $uv-primary;
    }
    &.secondary {
      background: #e3fffb;
      color: $uv-primary;
      border-color: #e3fffb;
    }
    &.big {
      height: 72rpx;
      font-size: 30rpx;
    }
    &.small {
      height: 48rpx;
      font-size: 24rpx;
    }
    &.disabled,
    &[disabled] {
      // background-color: #c9cdd4 !important;
      // border-color: #c9cdd4 !important;
      // color: #fff !important;
      opacity: 0.7;
    }
  }
}
.match-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 98rpx;
  background-color: $uv-primary;
  color: #fff;
  font-size: 28rpx;
  border-radius: 50rpx;
  line-height: 1.4;
  text {
    &:last-child {
      font-size: 20rpx;
      opacity: 0.8;
    }
  }
  &.brown {
    background: #9e5540;
    border-color: #9e5540;
  }
  &.blue {
    background: #40429e;
    border-color: #40429e;
  }
  &.disabled {
    background: #c9cdd4;
  }
  &.mid {
    height: 90rpx;
  }
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 32rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 12rpx 16rpx;
  border: 2rpx solid $uv-primary;
  color: $uv-primary;
  background: #e3fffb;
  border-radius: 8rpx;
  .iconfont {
    font-size: 20rpx;
  }
  &.sm {
    font-size: 24rpx;
    line-height: 32rpx;
    padding: 8rpx 12rpx;
    gap: 16rpx;
    .iconfont {
      font-size: 16rpx;
    }
  }
  &.gray {
    border-color: #e5e6eb;
    color: #1d2129;
    background: #fff;
  }
}
.tag-plain {
  background: #f7f8fa;
  border-radius: 8rpx;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  flex-shrink: 0;
  gap: 32rpx;
  font-size: 24rpx;
  line-height: 1.4;
  padding: 2rpx 8rpx;
  color: #4e5969;
  &.outline {
    border: 1px solid #c9cdd4;
    background: transparent;
    padding: 0 8rpx;
  }
  &.primary {
    background: #e8ffea;
    color: $uv-primary;
  }
  &.success {
    background: #e8ffea;
    color: $uv-success;
  }
  &.brown {
    background: #fff7e7;
    color: #905118;
  }
  &.org {
    background: #fff7e8;
    color: #ff7d00;
  }
  &.blue {
    background: #e8f3ff;
    color: #165dff;
  }
}
.white-card {
  position: relative;
  background: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  box-sizing: border-box;
  &.shadow {
    box-shadow: 0 4px 10px rgba(#000000, 0.2);
  }
}

.company-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  width: 100%;

  .company-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8rpx 0;

    .logo-col {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .info-col {
      width: calc(100% - 160rpx);
      display: flex;
      flex-direction: column;

      .company-name {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 28rpx;
        font-weight: 500;
        color: #000000;
      }

      .job-count {
        font-size: 24rpx;
        color: #86909c;
        margin-top: 10rpx;
      }
    }

    .arrow-col {
      width: 43rpx;
      height: 43rpx;
      image {
        width: 43rpx;
        height: 43rpx;
      }
    }
  }
}

.mainForm {
  //表单通用样式
  height: auto;
  overflow: hidden;
  background: #fff;
  border-radius: 20rpx;
  padding: 0 30rpx 0 30rpx;
  // box-shadow:0rpx 0rpx 10rpx rgba($color: #000, $alpha:0.1);
  ::v-deep .u-form-item__body {
    padding: 32rpx 0 !important;
  }
  .coverBtn {
    margin-left: auto;
    width: 60px;
    height: 60px;
    border-radius: 100px;
  }
  ::v-deep .u-form-item__body__left__content__label {
    font-size: 28rpx !important;
  }
  ::v-deep .u-form-item__body__left__content__required {
    position: static !important;
  }

  ::v-deep .u-add-tips {
    display: none !important;
  }

  ::v-deep .u-input {
    .u-input__content__field-wrapper__field {
      color: #333 !important;
      font-size: 15px !important;
      font-weight: bold;
      // padding-right:15rpx;
    }
  }

  ::v-deep .input-placeholder {
    font-size: 14px !important;
  }

  ::v-deep .u-textarea {
    padding: 10rpx 0 !important;
    background: none !important;

    .u-textarea__field {
      font-weight: bold;
      color: #333;
    }
  }

  ::v-deep .u-radio-group {
    justify-content: flex-end;
  }

  ::v-deep .u-radio {
    margin-right: 20rpx;
  }

  ::v-deep .u-upload {
    .u-upload__wrap {
      justify-content: flex-end;
    }

    .u-upload__button {
      margin: 0 !important;
    }
  }

  .textareaPlaceholder {
    text-align: right;
  }

  .ricon {
    width: 51rpx;
  }

  .formbox {
    // background: #f3f4fa;
    // margin: 0 -30rpx;
    // padding: 20rpx 30rpx;
    // border-top:1rpx #eee solid;
    // border-bottom:1rpx #eee solid;
  }

  .ftit {
    padding: 20rpx 40rpx 10rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-content: center;
    background: #f3f4fa;
    margin: 0 -40rpx;

    .tl {
      font-size: 30rpx;
      font-weight: bold;
      color: #000;
    }

    .tc {
      flex: 1;
      text-align: left;
      font-size: 26rpx;
      line-height: 45rpx;
      color: #c0c4cc;
      margin: 0 20rpx;
    }

    .tr {
    }
  }

  .rbtn {
    width: 180rpx;
    height: 60rpx;
    line-height: 60rpx;
    border: 0;
    border-radius: 100rpx;
    background: -webkit-linear-gradient(top, #1e98a7, #1c819f);
    font-size: 30rpx;
    color: #fff;
  }
}
