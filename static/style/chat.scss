page {
  background-color: #f5f6f7;
}
@font-face {
  font-family: "HMfont-home";
  src: url("data:application/x-font-woff2;charset=utf-8;base64,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")
    format("woff2");
}
.icon {
  font-family: "HMfont-home" !important;
  font-size: 56rpx;
  font-style: normal;
  color: #333;
  &.biaoqing:before {
    content: "\e797";
  }
  &.jianpan:before {
    content: "\e7b2";
  }
  &.yuyin:before {
    content: "\e805";
  }
  &.tupian:before {
    content: "\e639";
  }
  &.chehui:before {
    content: "\e904";
  }
  &.luyin:before {
    content: "\e905";
  }
  &.luyin2:before {
    content: "\e677";
  }
  &.other-voice:before {
    content: "\e667";
  }
  &.my-voice:before {
    content: "\e906";
  }
  &.hongbao:before {
    content: "\e626";
  }
  &.tupian2:before {
    content: "\e674";
  }
  &.paizhao:before {
    content: "\e63e";
  }
  &.add:before {
    content: "\e655";
  }
  &.close:before {
    content: "\e607";
  }
  &.to:before {
    content: "\e675";
  }
}
.hidden {
  display: none !important;
}
.popup-layer {
  &.showLayer {
    transform: translate3d(0, -30vw, 0);
  }
  transition: all 0.15s linear;
  width: 96%;
  height: 30vw;
  padding: 20rpx 2% 0 2%;
  background-color: #f5f6f7;
  // border-top: solid 1rpx #ddd;
  position: fixed;
  z-index: 101;
  top: 99%;
  .more-layer {
    width: 100%;
    .list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .box {
        width: 20vw;
        height: 20vw;
        background: #fff;
        border-radius: 8rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 4vw 2vw 4vw;
        .icon {
          width: 35%;
          margin-bottom: 5rpx;
          image {
            width: 100%;
          }
        }
        .txt {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}
.tab-view {
  position: fixed;
  bottom: 203rpx;
  z-index: 9;
  // background-color: #fff;
  width: 100%;
  // height: 112rpx;

  .tab-box {
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    padding: 16rpx 32rpx 0 32rpx;
    // overflow-x: auto;
    .tab-item {
      display: flex;
      flex-shrink: 0;
      justify-content: space-around;
      align-items: center;
      background-color: #fff;
      // border: 2.5rpx solid #18C2A5;
      padding: 8rpx 15rpx;
      margin-right: 16rpx;
      border-radius: 40rpx;
      image {
        width: 29rpx;
        height: 32rpx;
        margin-right: 2rpx;
      }
      text {
        font-size: 12px;
        color: #18C2A5;
        line-height: 18px;
        font-weight: 500;
      }
    }
    .item:first-child {
      background-color: #18C2A5;
      text {
        color: #fff;
      }
    }
    .disable {
      background-color: #ccd8ff;
    }
    .tab-item:last-child {
      margin-right: 0;
    }
  }
}
.input-main {
  position: fixed;
  z-index: 200;
  background-color: #f6f6f6;
  // bottom: 0rpx;
  width: 100%;
  transition: bottom 0.2s ease;
}
// .input-main {

// }
.input-box {
  width: 100%;
  // padding: 30rpx 30rpx 30rpx 30rpx;
  padding: 8rpx 16rpx 0 16rpx;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  // align-items: flex-end;
  align-items: center;
  background-color: #f6f6f6;
  // background-color: #f5f6f7;
  .em {
    flex-shrink: 0;
    width: 59rpx;
    height: 59rpx;
    // width: 70rpx;
    padding-right: 16rpx;
    // height: 70rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    image {
      width: 59rpx;
    }
  }
  &.showLayer {
    transform: translate3d(0, -30vw, 0);
  }
  transition: all 0.15s linear;
  .voice,
  .more {
    background: #fff;
    flex-shrink: 0;
    width: 70rpx;
    height: 70rpx;
    border-radius: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    image {
      width: 40rpx;
    }
  }
  .more {
    image {
      width: 45rpx;
    }
  }
  .send {
    // margin-left: 20rpx;
    width: 59rpx;
    height: 59rpx;
    // border-radius: 8rpx;
    display: flex;
    margin-left: 16rpx;
    align-items: center;
    image {
      width: 59rpx;
      height: 59rpx;
      margin: 0 auto;
      // margin: 0 32rpx;
    }
    .btn {
      width: 170rpx;
      height: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      //   background: #5465ff;
      background: linear-gradient(to right, #cee0f8, #dedaf7);
      color: #fff;
      border-radius: 50rpx;
      font-size: 26rpx;
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
    .u-button--primary {
      border-color: #f5f6f7 !important;
      background-color: #f5f6f7 !important;
      color: #fff !important;
    }
  }
  .textbox {
    flex: 1;
    // height: 75rpx;
    // overflow: hidden;
    // margin: 0 20rpx;
    .text-mode {
      width: 100%;
      max-height: 300rpx;
      height: auto;
      display: flex;
      align-items: flex-end;
      background-color: #fff;
      border-radius: 8rpx;
      .box {
        width: 100%;
        height: 75rpx;
        max-height: 300rpx;
        display: flex;
        align-items: center;
        .textarea {
          position: relative;
          width: 100%;
          max-height: 80rpx;
          padding: 0 32rpx;
          box-sizing: border-box;
          white-space: pre-wrap; /* 保持换行符和空白符 */
          word-wrap: break-word; /* 自动换行 */
          overflow-y: scroll; /* 使textarea垂直滚动 */
        }
      }
      .em {
        flex-shrink: 0;
        width: 70rpx;
        padding-left: 10rpx;
        height: 70rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
          width: 40rpx;
        }
      }
    }
  }
}
.record {
  width: 40vw;
  height: 40vw;
  position: fixed;
  top: 55%;
  left: 30%;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  .ing {
    width: 100%;
    height: 30vw;
    display: flex;
    justify-content: center;
    align-items: center;
    // 模拟录音音效动画
    @keyframes volatility {
      0% {
        background-position: 0% 130%;
      }
      20% {
        background-position: 0% 150%;
      }
      30% {
        background-position: 0% 155%;
      }
      40% {
        background-position: 0% 150%;
      }
      50% {
        background-position: 0% 145%;
      }
      70% {
        background-position: 0% 150%;
      }
      80% {
        background-position: 0% 155%;
      }
      90% {
        background-position: 0% 140%;
      }
      100% {
        background-position: 0% 135%;
      }
    }
    .icon {
      background-image: linear-gradient(to bottom, #f09b37, #fff 50%);
      background-size: 100% 200%;
      animation: volatility 1.5s ease-in-out -1.5s infinite alternate;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 150rpx;
      color: #f09b37;
    }
  }
  .cancel {
    width: 100%;
    height: 30vw;
    display: flex;
    justify-content: center;
    align-items: center;
    .icon {
      color: #fff;
      font-size: 150rpx;
    }
  }
  .tis {
    width: 100%;
    height: 10vw;
    display: flex;
    justify-content: center;
    font-size: 28rpx;
    color: #fff;
    &.change {
      color: #f09b37;
    }
  }
}
.content {
  width: 100%;
  .msg-list {
    width: 100%;
    // padding: 0% 2%;
    // height: calc(100% - 585rpx);
    // height: calc(100% - 512rpx);
    height: calc(100% - 430rpx);
    padding: 2% 26rpx;
    padding-bottom: 20rpx;
    box-sizing: border-box;
    position: absolute;
    // top: 247rpx;
    top: 160rpx;
    // top: 152rpx;
    bottom: 180rpx;
    .loading {
      //loading动画
      display: flex;
      justify-content: center;
      @keyframes stretchdelay {
        0%,
        40%,
        100% {
          transform: scaleY(0.6);
        }
        20% {
          transform: scaleY(1);
        }
      }
      .spinner {
        margin: 20rpx 0;
        width: 60rpx;
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        view {
          background-color: #f06c7a;
          height: 50rpx;
          width: 6rpx;
          border-radius: 6rpx;
          animation: stretchdelay 1.2s infinite ease-in-out;
        }
        .rect2 {
          animation-delay: -1.1s;
        }
        .rect3 {
          animation-delay: -1s;
        }
        .rect4 {
          animation-delay: -0.9s;
        }
        .rect5 {
          animation-delay: -0.8s;
        }
      }
    }
    .row {
      .system {
        display: flex;
        justify-content: center;
        .text {
          padding: 0 30rpx;
          height: 50rpx;
          font-size: 24rpx;
          background-color: #c9c9c9;
          color: #fff;
          border-radius: 40rpx;
        }
        .time {
          padding: 0 30rpx;
          height: 50rpx;
          font-size: 26rpx;
          color: #c8c8c8;
        }
        .red-envelope {
          image {
            margin-right: 5rpx;
            width: 30rpx;
            height: 30rpx;
          }
        }
        .interview {
          width: 95%;
          height: auto;
          overflow: hidden;
          margin: 30rpx 0;
          position: relative;
          .time {
            width: 100%;
            position: absolute;
            left: 0;
            top: 10rpx;
            z-index: 1;
            font-size: 24rpx;
            color: #fff;
            text-align: center;
            box-sizing: border-box;
          }
          .changeBtn {
            position: absolute;
            left: 30rpx;
            top: 50rpx;
            z-index: 1;
            text-align: center;
            .icon {
              width: 50rpx;
              height: 50rpx;
              overflow: hidden;
              display: flex;
              justify-content: center;
              align-items: center;
              background: rgba($color: #fff, $alpha: 0.1);
              border-radius: 100rpx;
              border: rgba($color: #fff, $alpha: 0.5) 1rpx solid;
              margin-bottom: 10rpx;
              image {
                width: 25rpx;
              }
            }
            .txt {
              font-size: 24rpx;
              color: #fff;
            }
          }
          .mainWindow {
            width: 100%;
            min-height: 630rpx;
            overflow: hidden;
            background: -webkit-linear-gradient(top, #0565d2, #89b2f1);
            position: relative;
            padding-bottom: 20rpx;
            image {
              width: 100%;
              height: 100%;
            }
          }
          .myWindow {
            width: 25%;
            position: absolute;
            right: 30rpx;
            top: 60rpx;
            z-index: 1;
            image {
              width: 100%;
              height: 100%;
            }
          }
          .progressbar {
            width: 160rpx;
            height: 160rpx;
            overflow: hidden;
            background: #fff;
            border-radius: 200rpx;
            box-shadow: 0 0 20rpx rgba($color: #000, $alpha: 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: -120rpx auto 40rpx auto;
            position: relative;
            z-index: 2;
            .progressText {
              width: 100%;
              position: absolute;
              left: 0;
              top: 35%;
              z-index: 2;
              text-align: center;
              .num {
                font-size: 26rpx;
                color: #000;
                text {
                  font-size: 30rpx;
                  font-weight: bold;
                }
              }
            }
          }
          .exambox {
            position: relative;
            z-index: 1;
            background: #fff;
            overflow: hidden;
            margin-top: -150rpx;
            border-top-left-radius: 50rpx;
            border-top-right-radius: 50rpx;
            padding: 150rpx 40rpx 30rpx 40rpx;
            .ex_tit {
              font-size: 28rpx;
              color: #000;
              text-align: left;
              font-weight: bold;
              line-height: 1.6;
              height: 220rpx;
            }
            .ex_btn {
              padding: 10rpx 30rpx;
              button {
                height: 80rpx;
                line-height: 80rpx;
                background: -webkit-linear-gradient(left, #15c0fd, #4290ff);
                box-shadow: 0 0 20rpx rgba($color: #000, $alpha: 0.2);
                border-radius: 100rpx;
                font-size: 30rpx;
                color: #fff;
              }
            }
            .ex_icon {
              image {
                width: 40%;
                display: block;
                margin: 10rpx auto 0 auto;
              }
            }
          }
        }
      }
      &:first-child {
        margin-top: 20rpx;
      }
      padding: 20rpx 0;
      .my .left,
      .other .right {
        width: 95.5%;
        display: flex;
        .bubble {
          // max-width: 80%;
          max-width: 89%;
          min-height: 50rpx;
          border-radius: 8rpx;
          padding: 15rpx 20rpx 15rpx 20rpx;
          // padding: 7.5rpx 15rpx;
          display: flex;
          align-items: center;
          font-size: 26rpx;
          line-height: 1.6;
          word-break: break-word;
          &.img {
            background-color: transparent;
            padding: 0;
            overflow: hidden;
            image {
              width: 350rpx;
              height: auto;
              // max-height: 350rpx;
            }
          }
          &.voice {
            .icon {
              font-size: 30rpx;
              display: flex;
              align-items: center;
            }
            .icon:after {
              content: " ";
              width: 53rpx;
              height: 53rpx;
              border-radius: 100%;
              position: absolute;
              box-sizing: border-box;
            }
            .length {
              font-size: 24rpx;
            }
          }
          &.choose {
            flex-direction: column;
            align-items: flex-start;
            .btns {
              width: 100%;
              padding: 20rpx 0 10rpx 0;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              button {
                width: 140rpx;
                height: 40rpx;
                line-height: 40rpx;
                background: #eee;
                font-size: 24rpx;
                color: #333;
                border-radius: 100rpx;
                margin-right: 10rpx;
                &.btn0 {
                  background: rgba($color: #f60, $alpha: 0.1);
                  color: #f60;
                }
              }
            }
          }
          &.resume {
            flex-direction: column;
            .btns {
              width: 100%;
              padding: 20rpx 0 10rpx 0;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              button {
                width: 140rpx;
                height: 40rpx;
                line-height: 40rpx;
                background: #eee;
                font-size: 24rpx;
                color: #333;
                border-radius: 100rpx;
                margin-right: 10rpx;
                &.btn0 {
                  background: rgba($color: #f60, $alpha: 0.1);
                  color: #f60;
                }
                &.btn1 {
                  background: rgba($color: #f90, $alpha: 0.2);
                  color: #f90;
                }
              }
            }
          }
          &.confirm {
            button {
              width: 100rpx;
              height: 40rpx;
              line-height: 40rpx;
              background: #f90;
              color: #fff;
              font-size: 24rpx;
              border-radius: 100rpx;
              margin-left: 10rpx;
            }
          }
          &.auth {
            flex-direction: column;
            align-items: flex-start;
            button {
              width: 100%;
              height: 60rpx;
              line-height: 60rpx;
              background: #18C2A5;
              color: #fff;
              font-size: 28rpx;
              border-radius: 8rpx;
              margin-top: 10rpx;
            }
          }
          &.jobs {
            flex-direction: column;
            align-items: flex-start;
            padding: 20rpx 20rpx 10rpx 20rpx;
            .job_list {
              width: 100%;
            }
            .item {
              height: auto;
              overflow: hidden;
              padding: 20rpx 0rpx;
              border-bottom: 5rpx #eee solid;
              .item_top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .tl {
                  flex: 1;
                  font-size: 30rpx;
                  color: #000;
                  font-weight: bold;
                }
                .tr {
                  font-size: 30rpx;
                  color: #52a6a6;
                  font-weight: bold;
                }
              }
              .item_com {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin: 10rpx 0;
                text {
                  font-size: 24rpx;
                  color: #666;
                  margin-right: 10rpx;
                }
              }
              .item_tags {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;
                align-items: center;
                text {
                  font-size: 24rpx;
                  color: #666;
                  background: #f6f6f6;
                  padding: 0 10rpx;
                  border-radius: 5rpx;
                  margin: 10rpx 10rpx 10rpx 0;
                }
              }
              .item_bt {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 10rpx;
                .bl {
                  image {
                    width: 40rpx;
                    height: 40rpx;
                    border-radius: 100rpx;
                  }
                }
                .bc {
                  flex: 1;
                  font-size: 24rpx;
                  color: #333;
                  margin: 0 10rpx;
                }
                .br {
                  font-size: 22rpx;
                  color: #ccc;
                }
              }
              &:last-child {
                border: 0;
              }
            }
            .morebtn {
              width: 100%;
              text-align: center;
              font-size: 22rpx;
              color: #ccc;
              padding: 0rpx 0 10rpx 0;
            }
          }
          &.offer {
            width: 80%;
            flex-direction: column;
            align-items: flex-start;
            padding: 20rpx 20rpx 10rpx 20rpx;
            .offer_list {
              width: 100%;
            }
            .item {
              height: auto;
              overflow: hidden;
              padding: 10rpx 20rpx;
              background: #f6f6f6;
              margin: 10rpx 0;
              .item_top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                line-height: 1;
                .tl {
                  flex: 1;
                  font-size: 26rpx;
                  color: #000;
                  font-weight: bold;
                }
                .tr {
                  font-size: 24rpx;
                  color: #f60;
                  // font-weight: bold;
                }
              }
              .item_bt {
                text {
                  font-size: 18rpx;
                  color: #aaa;
                  margin-right: 20rpx;
                }
              }
            }
          }
          &.exam {
            width: 80%;
            flex-direction: column;
            align-items: flex-start;
            padding: 20rpx 20rpx 10rpx 20rpx;
            .exam_tit {
              width: 100%;
              font-size: 26rpx;
              color: #333;
              margin-bottom: 10rpx;
            }
            .exam_options {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              .opt {
                font-size: 24rpx;
                color: #333;
                margin-right: 25rpx;
              }
            }
          }
          &.report {
            image {
              width: 600rpx;
              height: auto;
            }
          }
          &.evaluation {
            width: 80%;
            flex-direction: column;
            align-items: flex-start;
            padding: 20rpx 20rpx 10rpx 20rpx;
            .eva_step {
              width: 100%;
              box-sizing: border-box;
              background: #f3f3f3;
              padding: 15rpx;
            }
            ::v-deep .u-steps-item__wrapper {
              background: #f3f3f3;
            }
            ::v-deep .u-steps-item__content {
              padding-bottom: 20rpx;
              .u-text__value {
                font-size: 20rpx !important;
                color: #999 !important;
              }
              .desc {
                font-size: 20rpx !important;
                color: #999 !important;
              }
            }
            .eva_tit {
              width: 100%;
              font-size: 26rpx;
              color: #333;
              padding: 0 10rpx;
              box-sizing: border-box;
              margin: 30rpx 0 20rpx 0;
            }
            .eva_options {
              width: 100%;
              box-sizing: border-box;
              padding: 0 10rpx;
              .opt {
                height: auto;
                overflow: hidden;
                border-left: 4rpx #8876ff solid;
                text-align: left;
                font-size: 24rpx;
                color: #333;
                padding: 15rpx;
                box-shadow: 0 0 10rpx rgba($color: #000, $alpha: 0.1);
                margin-bottom: 15rpx;
                &.active {
                  background: #8876ff;
                  color: #fff;
                }
              }
            }
          }
        }
      }
      .my .right,
      .other .left {
        flex-shrink: 0;
        width: 54rpx;
        height: 54rpx;
        image {
          width: 54rpx;
          height: 54rpx;
          border-radius: 100rpx;
        }
      }
      .my {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        .left {
          min-height: 70rpx;
          align-items: center;
          justify-content: flex-end;
          .bubble {
            background: #18C2A5;
            color: #fff;
            // font-size: 30rpx;
            font-size: 15px;
            line-height: 1.6;
            // border-radius: 100rpx;
            border-radius: 15rpx;
            .bubbleEdit {
              line-height: 1.6;
              padding-bottom: 8rpx;
              // text-decoration: underline;
              border-bottom: 2rpx solid rgba(255, 255, 255, 0.3);
            }
            .icon {
              color: #fff;
              font-size: 20rpx;
              margin-right: 10rpx;
            }
            &.voice {
              .icon {
                color: #333;
              }
              .length {
                margin-right: 10rpx;
              }
            }
            &.play {
              @keyframes my-play {
                0% {
                  transform: translateX(80%);
                }
                100% {
                  transform: translateX(0%);
                }
              }
              .icon:after {
                border-left: solid 10rpx rgba(240, 108, 122, 0.5);
                animation: my-play 1s linear infinite;
              }
            }
          }
        }
        .right {
          margin-left: 15rpx;
        }
      }
      .other {
        width: 100%;
        display: flex;
        .left {
          margin-right: 15rpx;
        }
        .right {
          flex-wrap: wrap;
          .username {
            width: 100%;
            height: 45rpx;
            font-size: 24rpx;
            color: #333;
            display: flex;
            .name {
              margin-right: 10rpx;
            }
          }
          .bubble {
            // background:-webkit-linear-gradient(top,#F3F5FF,#ffffff);
            background: #eff0fe;
            color: #000;
            // color: #181819;
            border-radius: 15rpx;
            // border-radius: 12rpx;
            &.voice {
              .icon {
                color: #333;
              }
              .length {
                margin-left: 10rpx;
              }
            }
            &.play {
              @keyframes other-play {
                0% {
                  transform: translateX(-80%);
                }
                100% {
                  transform: translateX(0%);
                }
              }
              .icon:after {
                border-right: solid 10rpx rgba(255, 255, 255, 0.8);

                animation: other-play 1s linear infinite;
              }
            }
          }
        }
      }
    }
  }
  .noTabBar {
    height: calc(100% - 469rpx);
  }
}
.windows {
  .mask {
    position: fixed;
    top: 100%;
    width: 100%;
    height: 100%;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 0;
    transition: opacity 0.2s ease-out;
  }
  .layer {
    position: fixed;
    width: 80%;
    height: 70%;
    left: 10%;
    z-index: 1001;
    border-radius: 20rpx;
    overflow: hidden;
    top: 100%;
    transform: scale3d(0.5, 0.5, 1);
    transition: all 0.2s ease-out;
  }
  &.show {
    display: block;
    .mask {
      top: 0;
      opacity: 1;
    }
    .layer {
      transform: translate3d(0, -85vh, 0) scale3d(1, 1, 1);
    }
  }
  &.hide {
    display: block;
    .mask {
      top: 0;
      opacity: 0;
    }
    .layer {
      //transform: translate3d(0,-85vh,0) scale3d(.5,.5,1);
    }
  }
}
.chatMenu {
  position: fixed;
  left: 30rpx;
  bottom: 140rpx;
  z-index: 200;
  overflow: hidden;
  transform: translateY(150%);
  transition: all 0.4s ease;
  .fitem {
    height: 70rpx;
    line-height: 70rpx;
    font-size: 26rpx;
    color: #fff;
    border-radius: 8rpx;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.25);
    padding: 0 20rpx;
    margin-bottom: 20rpx;
    &.c0 {
      background: -webkit-linear-gradient(top, #9d70ff, #8060ff);
    }
    &.c1 {
      background: -webkit-linear-gradient(top, #708fff, #6070ff);
    }
    &.c2 {
      background: -webkit-linear-gradient(top, #ffb470, #ff9960);
    }
  }
  &.active {
    transform: translateY(0);
  }
}
.menuMask {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  background: rgba($color: #000, $alpha: 0);
}
.recordbox {
  height: 50%;
  overflow: hidden;
  padding: 0 30rpx;
  ::v-deep .u-textarea {
    background: transparent !important;
    textarea {
      color: #000;
    }
  }
  .textbox {
    background: -webkit-linear-gradient(top, #dce0ff, #f0f2fe);
    min-height: 400rpx;
    border-radius: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .tip {
      font-size: 28rpx;
      color: #333;
    }
    image {
      width: 35rpx;
      position: absolute;
      bottom: -22rpx;
      left: 48%;
      z-index: 1;
    }
  }
  .btnbox {
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    margin: 80rpx 0 50rpx 0;
    .btn_close {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      image {
        width: 60rpx;
        margin-bottom: 5rpx;
      }
      text {
        font-size: 26rpx;
        color: #fff;
      }
    }
    .btn_play {
      width: 140rpx;
      height: 140rpx;
      line-height: 140rpx;
      overflow: hidden;
      border-radius: 140rpx;
      background: #18C2A5;
      font-size: 30rpx;
      color: #fff;
    }
    .btn_pause {
      width: 140rpx;
      height: 140rpx;
      line-height: 140rpx;
      overflow: hidden;
      border-radius: 140rpx;
      background: #ff5454;
      font-size: 30rpx;
      color: #fff;
    }
    .btn_send {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      image {
        width: 60rpx;
        margin-bottom: 5rpx;
      }
      text {
        font-size: 26rpx;
        color: #fff;
      }
    }
  }
}

.chat {
  position: relative;
  background-color: #f5f6f7;
  height: 100vh;
  // height: calc(100vh - 672rpx);
}

.edit-input-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.6), #fff);
  .editBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    bottom: 80rpx;
    box-sizing: border-box;
    padding: 16rpx 36rpx 16rpx 26rpx;
    background-color: #fff;
    image {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
    image:last-child {
      margin-right: 0;
      margin-left: 28rpx;
    }
    .box {
      flex: 1;
      min-height: 88rpx;
      max-height: 50vh;
      overflow: hidden;
      display: flex;
      align-items: center;
      background-color: rgba(255, 255, 255, 1);
      box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.3);
      border-radius: 16rpx;
      padding-left: 18rpx;
      .textarea {
        position: relative;
        width: 100%;
        max-height: 100rpx;
        padding: 0 32rpx;
        box-sizing: border-box;
        white-space: pre-wrap; /* 保持换行符和空白符 */
        word-wrap: break-word; /* 自动换行 */
        overflow-y: scroll; /* 使textarea垂直滚动 */
      }
      // ::v-deep .u-textarea {
      //   width: 100%;
      //   max-height: 100rpx;
      //   padding-left: 32rpx;
      //   box-sizing: border-box;
      //   white-space: pre-wrap; /* 保持换行符和空白符 */
      //   word-wrap: break-word; /* 自动换行 */
      //   overflow-y: scroll; /* 使textarea垂直滚动 */
      // }
    }
  }
}

.copy {
  width: 30rpx;
  float: right;
}

.guideBox {
  padding: 16rpx 0 0 67rpx;
  > text {
    font-size: 16px;
    color: #333;
    line-height: 24px;
    padding-left: 25rpx;
  }
  &_list {
    // position: relative;
    display: flex;
    flex-direction: column;
  }

  .item {
    max-width: 95%;
    // display: inline-block;
    display: inline;
    // position: absolute;
    font-size: 13px;
    box-sizing: border-box;
    // min-width: 100rpx;
    line-height: 24px;
    border: 2rpx solid #acb2ff;
    border-radius: 15rpx;
    margin-bottom: 16rpx;
    color: #333;
    padding: 8rpx 12rpx 8rpx 24rpx;
  }
  // .item:nth-child(1) {
  //   top: 0;
  // }
  // .item:nth-child(2) {
  //   top: 70rpx;
  // }
  // .item:nth-child(3) {
  //   top: 138rpx;
  // }
}
.error {
  // padding: 7.5rpx 15rpx;
  padding: 4rpx 4rpx;
  display: flex;
  align-items: center;
  font-size: 15px;
  line-height: 1.6;
  word-break: break-word;
}
